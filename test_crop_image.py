#!/usr/bin/env python3
"""
Create a test image for verifying crop area restriction to image bounds
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    # Create a test image with different sections
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Draw a blue border around the entire image
    border_width = 10
    draw.rectangle([0, 0, width-1, height-1], outline='blue', width=border_width)
    
    # Draw a red rectangle in the center
    center_rect = [width//4, height//4, 3*width//4, 3*height//4]
    draw.rectangle(center_rect, fill='lightcoral', outline='red', width=3)
    
    # Add some text to identify the image area
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    # Add text in corners to identify image boundaries
    draw.text((20, 20), "TOP LEFT", fill='black', font=font)
    draw.text((width-120, 20), "TOP RIGHT", fill='black', font=font)
    draw.text((20, height-40), "BOTTOM LEFT", fill='black', font=font)
    draw.text((width-140, height-40), "BOTTOM RIGHT", fill='black', font=font)
    
    # Add center text
    draw.text((width//2-50, height//2), "CENTER", fill='white', font=font)
    
    # Draw grid lines to help with testing
    grid_spacing = 50
    for x in range(0, width, grid_spacing):
        draw.line([(x, 0), (x, height)], fill='lightgray', width=1)
    for y in range(0, height, grid_spacing):
        draw.line([(0, y), (width, y)], fill='lightgray', width=1)
    
    # Save the image
    output_path = os.path.join(os.path.dirname(__file__), 'test_crop_bounds.png')
    image.save(output_path)
    print(f"Test image saved to: {output_path}")
    return output_path

if __name__ == "__main__":
    create_test_image()
