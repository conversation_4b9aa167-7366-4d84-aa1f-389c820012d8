/* Copyright (C) 2014 the mpv developers */

#ifndef MPV_CLIENT_H_
#define MPV_CLIENT_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * The mpv client API allows clients to control mpv. This includes loading
 * files, controlling playback, and receiving events.
 */

/**
 * Handle to the mpv context.
 */
typedef struct mpv_handle mpv_handle;

/**
 * Create a new mpv instance and an associated client API handle to control
 * the mpv instance.
 */
mpv_handle *mpv_create(void);

/**
 * Initialize the mpv core. Will fail if the mpv core was already initialized.
 */
int mpv_initialize(mpv_handle *ctx);

/**
 * Destroy the mpv_handle.
 */
void mpv_terminate_destroy(mpv_handle *ctx);

/**
 * Set an option.
 */
int mpv_set_option_string(mpv_handle *ctx, const char *name, const char *data);

/**
 * Set a property to a given string value.
 */
int mpv_set_property_string(mpv_handle *ctx, const char *name, const char *data);

/**
 * Send a command to the player.
 */
int mpv_command(mpv_handle *ctx, const char **args);

/**
 * Return a string describing the error.
 */
const char *mpv_error_string(int error);

/* Error codes */
#define MPV_ERROR_SUCCESS                0
#define MPV_ERROR_EVENT_QUEUE_FULL      -1
#define MPV_ERROR_NOMEM                 -2
#define MPV_ERROR_UNINITIALIZED         -3
#define MPV_ERROR_INVALID_PARAMETER     -4
#define MPV_ERROR_OPTION_NOT_FOUND      -5
#define MPV_ERROR_OPTION_FORMAT         -6
#define MPV_ERROR_OPTION_ERROR          -7
#define MPV_ERROR_PROPERTY_NOT_FOUND    -8
#define MPV_ERROR_PROPERTY_FORMAT       -9
#define MPV_ERROR_PROPERTY_UNAVAILABLE  -10
#define MPV_ERROR_PROPERTY_ERROR        -11
#define MPV_ERROR_COMMAND               -12

#ifdef __cplusplus
}
#endif

#endif
