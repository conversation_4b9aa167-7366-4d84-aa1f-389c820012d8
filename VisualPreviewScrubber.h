#ifndef VISUALPREVIEWSCRUBBER_H
#define VISUALPREVIEWSCRUBBER_H

#include <QWidget>
#include <QSlider>
#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QTimer>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QPainter>
#include <QPixmap>
#include <QHash>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QThread>
#include <QMutex>
#include <memory>

// Forward declarations
class FrameIndexer;
class ScrubController;
class EnhancedPlaybackEngine;

class VisualPreviewScrubber : public QWidget
{
    Q_OBJECT

public:
    enum class ScrubMode {
        Disabled,       // No scrubbing
        Hover,          // Show preview on hover
        Drag,          // Show preview while dragging
        JKL            // J-K-L keyboard scrubbing
    };
    
    struct ScrubConfig {
        int previewWidth = 160;
        int previewHeight = 90;
        int trackHeight = 6;
        int handleSize = 12;
        int previewMargin = 10;
        bool showTimecode = true;
        bool showKeyFrameMarkers = true;
        bool enableSmoothAnimation = true;
        double animationDuration = 200.0; // milliseconds
        int maxCachedPreviews = 50;
    };

public:
    explicit VisualPreviewScrubber(QWidget *parent = nullptr);
    ~VisualPreviewScrubber();
    
    // Configuration
    void setConfig(const ScrubConfig &config);
    ScrubConfig getConfig() const { return m_config; }
    
    // Integration
    void setFrameIndexer(FrameIndexer *indexer);
    void setScrubController(ScrubController *controller);
    void setPlaybackEngine(EnhancedPlaybackEngine *engine);
    
    // Video properties
    void setDuration(double duration);
    void setPosition(double position);
    void setFrameRate(double fps);
    
    // Scrub modes
    void setScrubMode(ScrubMode mode);
    ScrubMode getScrubMode() const { return m_scrubMode; }
    
    // Preview management
    void enablePreviewGeneration(bool enable);
    void clearPreviewCache();
    void preloadPreviews(double startTime, double endTime);
    
    // Appearance
    void setTrackColor(const QColor &color);
    void setHandleColor(const QColor &color);
    void setPreviewBorderColor(const QColor &color);
    void setTimecodeFont(const QFont &font);
    
    // J-K-L scrubbing
    void enableJKLScrubbing(bool enable);
    void handleJKey();
    void handleKKey();
    void handleLKey();
    
    // Keyboard shortcuts
    void enableKeyboardShortcuts(bool enable);

signals:
    void positionChanged(double position);
    void scrubStarted(double position);
    void scrubEnded(double position);
    void seekRequested(double position);
    void previewRequested(double timestamp);
    void jklModeChanged(bool enabled);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onPreviewReady(double timestamp, const QPixmap &preview);
    void onPositionUpdateTimer();
    void onAnimationFinished();
    void onPreviewGenerationFinished();

private:
    // Core components
    FrameIndexer *m_frameIndexer;
    ScrubController *m_scrubController;
    EnhancedPlaybackEngine *m_playbackEngine;
    
    // Configuration
    ScrubConfig m_config;
    ScrubMode m_scrubMode;
    
    // State
    double m_duration;
    double m_currentPosition;
    double m_frameRate;
    bool m_isDragging;
    bool m_isHovering;
    bool m_previewVisible;
    bool m_jklEnabled;
    bool m_keyboardShortcutsEnabled;
    
    // Mouse interaction
    QPoint m_lastMousePos;
    double m_dragStartPosition;
    bool m_wasPlayingBeforeDrag;
    
    // Visual elements
    QRect m_trackRect;
    QRect m_handleRect;
    QRect m_previewRect;
    QPoint m_previewPosition;
    
    // Colors and styling
    QColor m_trackColor;
    QColor m_handleColor;
    QColor m_previewBorderColor;
    QFont m_timecodeFont;
    
    // Preview management
    QHash<double, QPixmap> m_previewCache;
    mutable QMutex m_previewMutex;
    QTimer *m_previewRequestTimer;
    double m_pendingPreviewTime;
    
    // Animation
    QPropertyAnimation *m_positionAnimation;
    QPropertyAnimation *m_previewAnimation;
    QGraphicsOpacityEffect *m_previewOpacity;
    
    // Update timers
    QTimer *m_positionTimer;
    QTimer *m_jklUpdateTimer;
    
    // Preview generation thread
    QThread *m_previewThread;
    
    // Helper methods
    void updateLayout();
    void updateTrackRect();
    void updateHandlePosition();
    void updatePreviewPosition(const QPoint &mousePos);
    
    // Coordinate conversion
    double positionFromX(int x) const;
    int xFromPosition(double position) const;
    QRect getHandleRect() const;
    
    // Preview handling
    void showPreview(double timestamp, const QPoint &position);
    void hidePreview();
    void requestPreview(double timestamp);
    QPixmap getPreviewAtTime(double timestamp);
    bool isPreviewCached(double timestamp) const;
    void cleanupPreviewCache();
    
    // Drawing
    void drawTrack(QPainter &painter);
    void drawHandle(QPainter &painter);
    void drawKeyFrameMarkers(QPainter &painter);
    void drawPreview(QPainter &painter);
    void drawTimecode(QPainter &painter, double timestamp, const QPoint &position);
    
    // J-K-L scrubbing
    void updateJKLPosition();
    QString formatTimecode(double seconds) const;
    
    // Animation helpers
    void animateToPosition(double position);
    void animatePreviewVisibility(bool visible);
};

// Preview generation worker
class PreviewWorker : public QObject
{
    Q_OBJECT

public:
    PreviewWorker(FrameIndexer *indexer, QObject *parent = nullptr);

public slots:
    void generatePreview(double timestamp, const QSize &size);
    void generatePreviewBatch(const QList<double> &timestamps, const QSize &size);

signals:
    void previewReady(double timestamp, const QPixmap &preview);
    void batchComplete();

private:
    FrameIndexer *m_indexer;
};

// Enhanced slider with visual feedback
class EnhancedSeekSlider : public QSlider
{
    Q_OBJECT

public:
    explicit EnhancedSeekSlider(Qt::Orientation orientation, QWidget *parent = nullptr);
    
    // Key frame markers
    void setKeyFrameMarkers(const QList<double> &positions);
    void clearKeyFrameMarkers();
    
    // Buffer visualization
    void setBufferRanges(const QList<QPair<double, double>> &ranges);
    void clearBufferRanges();
    
    // A-B loop markers
    void setABLoopMarkers(double aPosition, double bPosition);
    void clearABLoopMarkers();
    
    // Chapter markers
    void setChapterMarkers(const QList<double> &positions);
    void clearChapterMarkers();
    
    // Visual customization
    void setMarkerColor(const QColor &color);
    void setBufferColor(const QColor &color);
    void setABLoopColor(const QColor &color);
    void setChapterColor(const QColor &color);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    void drawMarkers(QPainter &painter);
    void drawBufferRanges(QPainter &painter);
    void drawABLoopMarkers(QPainter &painter);
    void drawChapterMarkers(QPainter &painter);
    
    QList<double> m_keyFrameMarkers;
    QList<QPair<double, double>> m_bufferRanges;
    double m_aPosition, m_bPosition;
    QList<double> m_chapterMarkers;
    
    QColor m_markerColor;
    QColor m_bufferColor;
    QColor m_abLoopColor;
    QColor m_chapterColor;
    
    bool m_hasABLoop;
};

#endif // VISUALPREVIEWSCRUBBER_H
