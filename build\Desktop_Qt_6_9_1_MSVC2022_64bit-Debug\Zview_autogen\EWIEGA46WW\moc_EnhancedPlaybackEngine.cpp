/****************************************************************************
** Meta object code from reading C++ file 'EnhancedPlaybackEngine.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../EnhancedPlaybackEngine.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'EnhancedPlaybackEngine.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN22EnhancedPlaybackEngineE_t {};
} // unnamed namespace

template <> constexpr inline auto EnhancedPlaybackEngine::qt_create_metaobjectdata<qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "EnhancedPlaybackEngine",
        "frameReady",
        "",
        "positionChanged",
        "position",
        "durationChanged",
        "duration",
        "playStateChanged",
        "playing",
        "bufferStateChanged",
        "buffering",
        "health",
        "performanceMetricsUpdated",
        "PerformanceMetrics",
        "metrics",
        "frameIndexReady",
        "previewGenerated",
        "timestamp",
        "preview",
        "errorOccurred",
        "error",
        "onFrameReady",
        "onPositionChanged",
        "onDurationChanged",
        "updateMetrics",
        "processFrameQueue"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'frameReady'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'positionChanged'
        QtMocHelpers::SignalData<void(double)>(3, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 4 },
        }}),
        // Signal 'durationChanged'
        QtMocHelpers::SignalData<void(double)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 6 },
        }}),
        // Signal 'playStateChanged'
        QtMocHelpers::SignalData<void(bool)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 8 },
        }}),
        // Signal 'bufferStateChanged'
        QtMocHelpers::SignalData<void(bool, double)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 10 }, { QMetaType::Double, 11 },
        }}),
        // Signal 'performanceMetricsUpdated'
        QtMocHelpers::SignalData<void(const PerformanceMetrics &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 13, 14 },
        }}),
        // Signal 'frameIndexReady'
        QtMocHelpers::SignalData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'previewGenerated'
        QtMocHelpers::SignalData<void(double, const QImage &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 17 }, { QMetaType::QImage, 18 },
        }}),
        // Signal 'errorOccurred'
        QtMocHelpers::SignalData<void(const QString &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 },
        }}),
        // Slot 'onFrameReady'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onPositionChanged'
        QtMocHelpers::SlotData<void(double)>(22, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 4 },
        }}),
        // Slot 'onDurationChanged'
        QtMocHelpers::SlotData<void(double)>(23, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 6 },
        }}),
        // Slot 'updateMetrics'
        QtMocHelpers::SlotData<void()>(24, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'processFrameQueue'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<EnhancedPlaybackEngine, qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject EnhancedPlaybackEngine::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>.metaTypes,
    nullptr
} };

void EnhancedPlaybackEngine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<EnhancedPlaybackEngine *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->frameReady(); break;
        case 1: _t->positionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 2: _t->durationChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 3: _t->playStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 4: _t->bufferStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 5: _t->performanceMetricsUpdated((*reinterpret_cast< std::add_pointer_t<PerformanceMetrics>>(_a[1]))); break;
        case 6: _t->frameIndexReady(); break;
        case 7: _t->previewGenerated((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2]))); break;
        case 8: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->onFrameReady(); break;
        case 10: _t->onPositionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 11: _t->onDurationChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 12: _t->updateMetrics(); break;
        case 13: _t->processFrameQueue(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)()>(_a, &EnhancedPlaybackEngine::frameReady, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(double )>(_a, &EnhancedPlaybackEngine::positionChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(double )>(_a, &EnhancedPlaybackEngine::durationChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(bool )>(_a, &EnhancedPlaybackEngine::playStateChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(bool , double )>(_a, &EnhancedPlaybackEngine::bufferStateChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(const PerformanceMetrics & )>(_a, &EnhancedPlaybackEngine::performanceMetricsUpdated, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)()>(_a, &EnhancedPlaybackEngine::frameIndexReady, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(double , const QImage & )>(_a, &EnhancedPlaybackEngine::previewGenerated, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (EnhancedPlaybackEngine::*)(const QString & )>(_a, &EnhancedPlaybackEngine::errorOccurred, 8))
            return;
    }
}

const QMetaObject *EnhancedPlaybackEngine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EnhancedPlaybackEngine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22EnhancedPlaybackEngineE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int EnhancedPlaybackEngine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void EnhancedPlaybackEngine::frameReady()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void EnhancedPlaybackEngine::positionChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void EnhancedPlaybackEngine::durationChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void EnhancedPlaybackEngine::playStateChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void EnhancedPlaybackEngine::bufferStateChanged(bool _t1, double _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void EnhancedPlaybackEngine::performanceMetricsUpdated(const PerformanceMetrics & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void EnhancedPlaybackEngine::frameIndexReady()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void EnhancedPlaybackEngine::previewGenerated(double _t1, const QImage & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}

// SIGNAL 8
void EnhancedPlaybackEngine::errorOccurred(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1);
}
QT_WARNING_POP
