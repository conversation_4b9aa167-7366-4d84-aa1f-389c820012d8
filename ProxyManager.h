#ifndef PROXYMANAGER_H
#define PROXYMANAGER_H

#include <QObject>
#include <QString>
#include <QQueue>
#include <QTimer>
#include <QProcess>
#include <QMutex>
#include <QThread>
#include <QHash>
#include <QFileInfo>
#include <QSettings>

struct ProxyJob {
    QString sourceFilePath;
    QString outputFilePath;
    int width;
    int height;
    int bitrate;
    bool isLowRes;  // true for scrubbing proxy, false for preview proxy
    int priority;   // 0 = highest priority
    QDateTime queued;
    
    ProxyJob() : width(0), height(0), bitrate(0), isLowRes(true), priority(5) {}
};

class ProxyManager : public QObject
{
    Q_OBJECT

public:
    explicit ProxyManager(QObject *parent = nullptr);
    ~ProxyManager();    // Proxy generation requests (single proxy approach)
    void generateScrubbingProxy(const QString &filePath);      // Single proxy for fast scrubbing
    void generateProxyWithSettings(const QString &filePath, int width, int height, int bitrate, int priority = 5);
    
    // Proxy availability
    bool hasScrubbingProxy(const QString &filePath);
    QString getScrubbingProxyPath(const QString &filePath);
    
    // For backward compatibility and future flexibility
    bool hasPreviewProxy(const QString &filePath) { return false; }  // Always use original for preview
    QString getPreviewProxyPath(const QString &filePath) { return QString(); }  // Always use original
    
    // Queue management
    void clearQueue();
    void cancelJob(const QString &filePath);
    int getQueueSize();
    QStringList getQueuedFiles();
    
    // Settings
    void setProxyDirectory(const QString &path);
    void setMaxConcurrentJobs(int maxJobs);
    void setFFmpegPath(const QString &path);
    
    // Cleanup
    void cleanupOldProxies(int daysOld = 30);
    qint64 getProxyDirectorySize();

public slots:
    void processQueue();

signals:
    void proxyGenerationStarted(const QString &sourceFile, const QString &outputFile);
    void proxyGenerationProgress(const QString &sourceFile, int percentage);
    void proxyGenerationCompleted(const QString &sourceFile, const QString &outputFile);
    void proxyGenerationFailed(const QString &sourceFile, const QString &error);
    void queueSizeChanged(int size);

private slots:
    void onProxyProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProxyProcessError(QProcess::ProcessError error);
    void onProxyProcessOutput();

private:
    QString generateProxyPath(const QString &sourceFile, bool isLowRes);
    QString generateProxyKey(const QString &sourceFile);
    void startNextJob();
    void ensureProxyDirectory();
    QStringList buildFFmpegArgs(const ProxyJob &job);
    void updateProgress(const QString &output, const ProxyJob &job);
    
    // Queue management
    QQueue<ProxyJob> m_jobQueue;
    QMutex m_queueMutex;
    
    // Active jobs
    QHash<QProcess*, ProxyJob> m_activeJobs;
    int m_maxConcurrentJobs;
    
    // Configuration
    QString m_proxyDirectory;
    QString m_ffmpegPath;
    QSettings *m_settings;
    
    // Processing
    QTimer *m_processTimer;
    
    // Constants
    static const int SCRUBBING_PROXY_WIDTH = 640;
    static const int SCRUBBING_PROXY_HEIGHT = 360;
    static const int SCRUBBING_PROXY_BITRATE = 500;  // 500 kbps
    
    static const int PREVIEW_PROXY_WIDTH = 1280;
    static const int PREVIEW_PROXY_HEIGHT = 720;
    static const int PREVIEW_PROXY_BITRATE = 2000;  // 2 Mbps
};

// Thread-safe cache for quick proxy lookups
class ProxyCache
{
public:
    static ProxyCache* instance();
    
    void addProxy(const QString &sourceFile, const QString &proxyFile, bool isLowRes);
    void removeProxy(const QString &sourceFile, bool isLowRes);
    QString getProxy(const QString &sourceFile, bool isLowRes);
    bool hasProxy(const QString &sourceFile, bool isLowRes);
    void clear();
    
private:
    ProxyCache() = default;
    static ProxyCache *s_instance;
    
    QHash<QString, QString> m_scrubbingProxies;  // sourceFile -> proxyFile
    QHash<QString, QString> m_previewProxies;    // sourceFile -> proxyFile
    QMutex m_mutex;
};

#endif // PROXYMANAGER_H
