[SmartCache]
# Enable smart caching and proxy generation
enabled=true

# Cache segment duration in seconds (10-300)
segment_duration=30

# Maximum cache size in megabytes (100-10000)
max_cache_size=2048

# Cache strategy: 0=LRU, 1=Predictive, 2=Adaptive
cache_strategy=2

# Enable automatic proxy generation
auto_proxy_generation=true

# Proxy generation priorities (0=highest, 10=lowest)
scrubbing_proxy_priority=0
preview_proxy_priority=3

# Prefetch settings
prefetch_enabled=true
prefetch_segments=3

# Cleanup settings
cleanup_old_proxies_days=30
cleanup_interval_minutes=60

[ProxyGeneration]
# FFmpeg path (leave empty to use system PATH)
ffmpeg_path=

# Maximum concurrent proxy generation jobs
max_concurrent_jobs=2

# Proxy generation timeout in minutes
generation_timeout_minutes=30

# Scrubbing proxy settings (for fast scrubbing)
scrubbing_width=640
scrubbing_height=360
scrubbing_bitrate=500

# Preview proxy settings (for preview quality)
preview_width=1280
preview_height=720
preview_bitrate=2000

# High quality proxy settings (for high quality preview)
high_quality_width=1920
high_quality_height=1080
high_quality_bitrate=5000

[Performance]
# GPU acceleration for proxy generation (experimental)
gpu_acceleration=false

# Use multiple threads for processing
multi_threading=true

# Background processing priority (0=lowest, 2=highest)
background_priority=1

# Memory usage limits
max_memory_usage_mb=1024

# Disk I/O optimization
use_fast_disk_cache=true
disk_cache_block_size=64

[UI]
# Show proxy generation progress in UI
show_progress=true

# Show cache statistics
show_cache_stats=false

# Auto-hide proxy generation notifications
auto_hide_notifications=true

# Notification timeout in seconds
notification_timeout=5

[Advanced]
# Enable debug logging for caching
debug_logging=false

# Use predictive caching based on viewing patterns
predictive_caching=true

# Cache segments around current position
context_caching=true

# Maximum seek history for pattern analysis
max_seek_history=100

# Access pattern analysis interval in seconds
pattern_analysis_interval=5

# Enable experimental features
experimental_features=false
