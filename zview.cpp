#include "zview.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif
#include "ui_zview.h"
#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLTexture>
#include <QMatrix4x4>
#include <QMouseEvent>
#include <QApplication>
#include <QPainter>
#include <QPainterPath>
#include <QBitmap>
#include <QRegion>
#include <QShowEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QFileInfo>
#include <QImageReader>
#include <QWheelEvent>
#include <QKeyEvent>
#include <QResizeEvent>
#include <QTimer>
#include <QDir>
#include <QDebug>
#include <QDateTime>
#include <QStyle>
#include <QLabel>
#include <QGroupBox>
#include <QComboBox>
#include <QCheckBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QColorDialog>
#include <cmath>
#include <algorithm>
#include "MpvWrapper.h"
#include "HeicImageLoader.h"
#include "SimpleCache.h"

Zview::Zview(QWidget *parent)
    : QOpenGLWidget(parent)
    , ui(new Ui::Zview)
    , m_vertexBuffer(QOpenGLBuffer::VertexBuffer)
    , m_elementBuffer(QOpenGLBuffer::IndexBuffer)
{
    ui->setupUi(this);
      // Set crop icon programmatically as fallback
    QIcon cropIcon("c:/Users/<USER>/Desktop/Zview/crop-icon-white.svg");
    if (!cropIcon.isNull()) {
        ui->cropTool->setIcon(cropIcon);
        ui->cropTool->setIconSize(QSize(20, 20));
        qDebug() << "Crop icon loaded successfully";
    } else {
        qDebug() << "Failed to load crop icon";
    }
    
    // Remove title bar and window decorations
    setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
    
    // Enable transparency for rounded corners
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);
      // Enable drag and drop
    setAcceptDrops(true);
    
    // Enable mouse tracking for cursor changes on hover
    setMouseTracking(true);    // Set default window size
    resize(1400, 1000);
    
    // Position buttons correctly
    repositionButtons();
    
    // Initialize video playback components    m_videoWidget = nullptr;
    m_mpvPlayer = nullptr;
    m_isPlayingVideo = false;
    m_seekbarDragging = false;
    m_wasPlayingBeforeSeek = false;
    m_pendingSeekPosition = -1.0;
    
    // Initialize A-B loop variables
    m_abLoopEnabled = false;
    m_abLoopStartTime = -1.0;    m_abLoopEndTime = -1.0;
    m_hasAPoint = false;
    m_hasBPoint = false;
    m_aMarker = nullptr;
    m_bMarker = nullptr;    // Initialize crop tool variables
    m_cropModeActive = false;    m_cropOverlay = nullptr;
    m_draggingCrop = false;
    m_drawingCrop = false;
    m_movingCrop = false;
    m_draggingHandle = -1;
    m_cropRelativeX = 0.0f;
    m_cropRelativeY = 0.0f;
    m_cropRelativeWidth = 0.0f;
    m_cropRelativeHeight = 0.0f;
    m_hasCropRelativePosition = false;
      // Initialize cursor state tracking
    m_currentCursorShape = Qt::ArrowCursor;
    m_lastCursorUpdatePos = QPoint(0, 0);
    m_forceCropCursor = false;
    
    // Create timer for seekbar updates
    m_seekbarTimer = new QTimer(this);
    connect(m_seekbarTimer, &QTimer::timeout, this, [this]() {
        // This timer is kept for compatibility but the actual position updates
        // are now handled by MpvWrapper's internal timer which emits positionChanged signals
        // This timer can be used for other periodic UI updates if needed
    });
    m_seekbarTimer->setInterval(1000); // Update every second
      // Create seek throttle timer for smooth real-time seeking
    m_seekThrottleTimer = new QTimer(this);
    m_seekThrottleTimer->setSingleShot(true);
    m_seekThrottleTimer->setInterval(0); // Zero throttle for instantaneous response
    connect(m_seekThrottleTimer, &QTimer::timeout, this, [this]() {
        if (m_pendingSeekPosition >= 0.0 && m_mpvPlayer && m_isPlayingVideo) {
            m_mpvPlayer->seek(m_pendingSeekPosition);
            m_pendingSeekPosition = -1.0;
        }
    });
      // Initialize control visibility state
    m_controlsVisible = false;
    
    // Initialize toolbox visibility state
    m_toolboxVisible = false;
    
    // Create control hide timer for auto-hiding controls (will be created later when needed)
    m_controlHideTimer = nullptr;
    
    // Create toolbox hide timer for auto-hiding toolbox (will be created later when needed)
    m_toolboxHideTimer = nullptr;
      // Create cursor update timer for handling fast mouse movements
    m_cursorUpdateTimer = new QTimer(this);
    m_cursorUpdateTimer->setSingleShot(false); // Continuous updates
    m_cursorUpdateTimer->setInterval(4); // ~250 FPS update rate for optimal fast response
    connect(m_cursorUpdateTimer, &QTimer::timeout, this, &Zview::updateCursorForPosition);
    
    // Enable mouse tracking to detect hover
    setMouseTracking(true);
    
    // Ensure seekbar starts hidden and optimize for fastest response
    if (ui->seekSlider) {
        ui->seekSlider->setVisible(false);
        ui->seekSlider->setTracking(true); // Enable real-time tracking for instant feedback
        ui->seekSlider->setPageStep(1);    // Minimize step size for smooth movement
        ui->seekSlider->setSingleStep(1);  // Finest granularity for smooth seeking
        
        // Set high-frequency update policy for maximum responsiveness
        ui->seekSlider->setMouseTracking(true);
        ui->seekSlider->setAttribute(Qt::WA_OpaquePaintEvent, true);
        
        // Install event filter for even more responsive mouse handling
        ui->seekSlider->installEventFilter(this);
        
        // Store the click position for accurate seeking
        m_lastClickPosition = -1;
    }      // Connect seekbar signals for ultra-fast seeking
    connect(ui->seekSlider, &QSlider::sliderPressed, this, [this]() {
        m_seekbarDragging = true;
        m_isSeeking = true;
        
        // Enable scrubbing mode in smart cache
        if (m_smartCache) {
            m_smartCache->onSeekStarted();
        }
        
        if (m_mpvPlayer && m_isPlayingVideo) {            // Remember if video was playing before seeking
            m_wasPlayingBeforeSeek = !m_mpvPlayer->isPaused();            // Using original video with optimized scrubbing settings
            // NO PROXY GENERATION - MPV engine optimizations for smooth scrubbing
            
            // Enable ultra-fast scrubbing mode in MPV
            m_mpvPlayer->enableScrubbingMode(true);
            m_mpvPlayer->optimizeForScrubbing();
            
            // For single clicks, we want to always play after seeking
            // Only pause during actual dragging (this will be handled in sliderMoved)
            
            // Don't seek here - let sliderReleased handle the final seek
            // This prevents seeking to the old position before the click is processed
        }
    });      connect(ui->seekSlider, &QSlider::sliderReleased, this, [this]() {
        qDebug() << "Slider released - exiting scrubbing mode";
        
        // Disable scrubbing mode in smart cache
        if (m_smartCache) {
            m_smartCache->onSeekFinished();
        }
        
        if (m_mpvPlayer && m_isPlayingVideo) {
            double position;
            
            // Use the captured click position if available, otherwise fall back to slider value
            if (m_lastClickPosition >= 0.0) {
                position = m_lastClickPosition;
                qDebug() << "Using captured click position:" << position << "seconds";
                m_lastClickPosition = -1.0; // Reset after use
            } else {
                position = (double)ui->seekSlider->value() / 1000.0; // Convert from ms
                qDebug() << "Using slider value position:" << position << "seconds";
            }
            
            // Use proper seek method for final position
            m_mpvPlayer->seek(position);
            m_mpvPlayer->flushScrubbingSeeks();            // Switch back to original video for high-quality preview
            // TEMPORARY: Disabled since we're not switching to proxy in the first place
            /*
            if (!m_originalVideoFile.isEmpty() && m_originalVideoFile != m_currentVideoFile) {
                qDebug() << "Switching back to original video at position:" << position;
                m_mpvPlayer->loadFile(m_originalVideoFile);
                
                // Wait a moment for file to load, then seek to position
                QTimer::singleShot(100, this, [this, position]() {
                    if (m_mpvPlayer) {
                        m_mpvPlayer->seek(position);
                        // Trigger a frame update
                        update();
                    }
                });
                
                m_currentVideoFile = m_originalVideoFile;  // Update current file reference
                m_originalVideoFile.clear();  // Clear temporary reference
                qDebug() << "Switched back to original video for preview";
            }
            */
            qDebug() << "No proxy switching needed - continuing with current video";
            
            // Disable scrubbing mode and restore normal playback
            m_mpvPlayer->enableScrubbingMode(false);
            
            // Playback will be resumed in seekCompleted signal handler
        }
        m_seekbarDragging = false;
        
        // Reset seeking flag as fallback in case seekCompleted doesn't fire
        QTimer::singleShot(200, this, [this]() {
            if (m_isSeeking) {
                qDebug() << "Fallback: Resetting m_isSeeking flag after scrubbing";
                m_isSeeking = false;
            }
        });
    });
      // Ultra-fast seeking connections for maximum responsiveness
    // Primary connection: sliderMoved for immediate mouse movement feedback
    connect(ui->seekSlider, &QSlider::sliderMoved, this, [this](int value) {
        static int lastValue = -1;
        static qint64 lastTime = 0;
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        
        // Skip redundant updates within 2ms for optimal efficiency
        if (value == lastValue && (currentTime - lastTime) < 2) {
            return;
        }
        
        lastValue = value;
        lastTime = currentTime;
        
        if (m_mpvPlayer && m_isPlayingVideo) {
            double position = (double)value / 1000.0;              // Direct MPV command for ultra-fast scrubbing during drag
                qDebug() << "Scrubbing to position:" << position << "seconds";
                m_mpvPlayer->setScrubbingPosition(position);
                
                // Update cache immediately
                if (m_smartCache) {
                    m_smartCache->setScrubPosition(position);
                }
        }
    });
    
    // Secondary connection: valueChanged for all changes
    connect(ui->seekSlider, &QSlider::valueChanged, this, [this](int value) {
        if (m_mpvPlayer && m_isPlayingVideo) {
            double position = (double)value / 1000.0; // Convert from ms
            
            // INSTANT visual feedback - always update position display
            m_mpvPlayer->updatePositionInstant(position);
            
            // Only throttle actual MPV commands when dragging
            if (m_seekbarDragging) {
                m_pendingSeekPosition = position;
                m_seekThrottleTimer->start();
            }
        }
    });
    
    // Initialize volume slider
    if (ui->volumeSlider) {
        ui->volumeSlider->setVisible(false);
        ui->volumeSlider->setValue(100); // Default to 100% volume
    }
    
    // Connect volume slider for real-time volume control
    connect(ui->volumeSlider, &QSlider::valueChanged, this, [this](int value) {
        if (m_mpvPlayer && m_isPlayingVideo) {
            // Convert slider value (0-100) to mpv volume (0-100)
            m_mpvPlayer->setVolume(value);
        }
    });
      // Connect A and B buttons for A-B repeat functionality
    connect(ui->buttonA, &QPushButton::clicked, this, [this]() {
        setABLoopPoint(true); // Set A point
    });
    
    connect(ui->buttonB, &QPushButton::clicked, this, [this]() {
        setABLoopPoint(false); // Set B point
    });
    
    // Initialize smart caching (no proxy generation)
    m_smartCache = new SimpleSmartCache(this);
    
    // Configure smart caching defaults
    m_smartCache->enableSmartCaching(true);
    m_smartCache->setSegmentDuration(30);  // 30-second segments
    m_smartCache->setMaxCacheSize(2048);   // 2GB cache
    m_smartCache->setCacheStrategy(2);     // Adaptive strategy    // Connect crop button
    connect(ui->cropTool, &QToolButton::clicked, this, [this]() {
        if (m_cropModeActive) {
            // Simply hide crop tool instead of applying crop
            hideCropTool(); // This will call hideToolbox()
        } else {
            showCropTool(); // This sets m_cropModeActive = true, so toolbox should stay visible
        }
    });

    // Connect crop action buttons
    connect(ui->toolButton_5, &QToolButton::clicked, this, [this]() {
        // Apply Crop button
        applyCrop();
    });
    
    connect(ui->toolButton_4, &QToolButton::clicked, this, [this]() {
        // Undo button - for now just hide crop tool
        hideCropTool();
    });
    
    connect(ui->toolButton_3, &QToolButton::clicked, this, [this]() {
        // Exit Crop button
        hideCropTool();
    });    // Second button - Image Editor
    connect(ui->editTool, &QToolButton::clicked, this, [this]() {
        qDebug() << "Edit tool clicked";
        if (m_imageEditorActive) {
            hideImageEditor();
        } else {
            showImageEditor();
        }
    });
    
    // Third button available for future use
    connect(ui->viewTool, &QToolButton::clicked, this, [this]() {
        qDebug() << "View tool clicked";
        // Available for future functionality
        // Hide toolbox after clicking (force hide even in crop mode)
        hideToolbox(true);
    });
    
    // Initialize toolbox to be hidden by default
    if (ui->modernToolbox) {
        ui->modernToolbox->setVisible(false);
    }
    
    // Initialize image editor variables
    m_imageEditorActive = false;
    m_imageEditorToolbox = nullptr;
    m_brightnessSlider = nullptr;
    m_contrastSlider = nullptr;
    m_saturationSlider = nullptr;
    m_hueSlider = nullptr;
    m_blurSlider = nullptr;
    m_sharpenSlider = nullptr;
    m_rotateLeftBtn = nullptr;
    m_rotateRightBtn = nullptr;
    m_flipHorizontalBtn = nullptr;
    m_flipVerticalBtn = nullptr;
    m_grayscaleBtn = nullptr;
    m_sepiaBtn = nullptr;
    m_embossBtn = nullptr;
    m_resetEditsBtn = nullptr;
    m_applyEditsBtn = nullptr;
    m_cancelEditsBtn = nullptr;
    m_saveImageBtn = nullptr;
    
    // Initialize image editing values
    m_brightnessValue = 0;
    m_contrastValue = 0;
    m_saturationValue = 0;
    m_hueValue = 0;
    m_blurValue = 0;
    m_sharpenValue = 0;
    m_rotationAngle = 0;
    m_isFlippedHorizontal = false;
    m_isFlippedVertical = false;
    m_grayscaleApplied = false;
    m_sepiaApplied = false;
    m_embossApplied = false;
}

Zview::~Zview()
{
    makeCurrent();
    
    // Clean up performance optimization resources first
    cleanupMipmaps();
    
    // Clean up main texture only if it's not already cleaned up by mipmaps
    if (m_texture) {
        delete m_texture;
        m_texture = nullptr;
    }
    
    if (m_shaderProgram) {
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
    }
    
    if (m_uniformBuffer) {
        glDeleteBuffers(1, &m_uniformBuffer);
        m_uniformBuffer = 0;
    }
    
    if (m_framebuffer) {
        glDeleteFramebuffers(1, &m_framebuffer);
        m_framebuffer = 0;
    }
    
    if (m_colorTexture) {
        glDeleteTextures(1, &m_colorTexture);
        m_colorTexture = 0;
    }
    
    m_vao.destroy();
    m_vertexBuffer.destroy();
    m_elementBuffer.destroy();
    
    doneCurrent();
      // Clean up video resources
    if (m_mpvPlayer) {
        delete m_mpvPlayer;
        m_mpvPlayer = nullptr;
    }
    
    if (m_videoWidget) {
        delete m_videoWidget;
        m_videoWidget = nullptr;
    }
    
    // Clean up smart caching resources
    if (m_smartCache) {
        delete m_smartCache;
        m_smartCache = nullptr;
    }
    
    // Clean up timers that may have been created conditionally
    if (m_controlHideTimer) {
        delete m_controlHideTimer;
        m_controlHideTimer = nullptr;
    }
    
    if (m_toolboxHideTimer) {
        delete m_toolboxHideTimer;
        m_toolboxHideTimer = nullptr;
    }
      // Clean up crop overlay and handles
    if (m_cropOverlay) {
        delete m_cropOverlay;
        m_cropOverlay = nullptr;
    }
    
    for (QWidget* handle : m_cropHandles) {
        delete handle;
    }
    m_cropHandles.clear();
    
    // Clean up image editor toolbox
    if (m_imageEditorToolbox) {
        delete m_imageEditorToolbox;
        m_imageEditorToolbox = nullptr;
    }
    
    delete ui;
}

void Zview::initializeGL()
{
    initializeOpenGLFunctions();
    
    // Detect OpenGL capabilities for optimal performance
    detectOpenGLCapabilities();
    
    // Set up the OpenGL context with performance optimizations
    glClearColor(25.0f/255.0f, 25.0f/255.0f, 28.0f/255.0f, 1.0f);
    
    // Enable only necessary OpenGL features
    glDisable(GL_DEPTH_TEST); // We don't need depth testing for 2D rendering
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    // Enable seamless cubemap for better texture filtering
    if (context()->hasExtension("GL_ARB_seamless_cube_map")) {
        glEnable(GL_TEXTURE_CUBE_MAP_SEAMLESS);
    }
    
    // Setup optimized rendering pipeline
    setupShaders();
    setupVertexData();
    setupUniformBuffer();
    setupFramebuffer();
    
    // Initialize professional performance engine
    initializePerformanceEngine();
    
    m_glInitialized = true;
}

// Public method for opening files (called from main.cpp for command line arguments)
void Zview::openFile(const QString &filePath)
{
    // Check if it's a video file
    if (isVideoFile(filePath)) {
        loadVideo(filePath);
    } else {
        loadImage(filePath);
    }
}

void Zview::paintGL()
{
    // Performance optimization: only clear color buffer for 2D rendering
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Handle video rendering
    if (m_isPlayingVideo && m_mpvPlayer && m_mpvPlayer->isInitialized()) {
        // Ensure the viewport is set correctly
        glViewport(0, 0, width(), height());
        
        // Clear with black background for video
        glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT);
        
        // Render MPV frame directly to our framebuffer
        m_mpvPlayer->renderFrame(width(), height(), defaultFramebufferObject());
        m_mpvPlayer->acknowledgeFrame();
        
        // Reset clear color back to default
        glClearColor(25.0f/255.0f, 25.0f/255.0f, 28.0f/255.0f, 1.0f);
        return;
    }
    
    // Handle image rendering
    if (m_currentImage.isNull()) {
        return;
    }
    
    // Update texture if needed
    if (m_textureNeedsUpdate || !m_texture) {
        loadImageToTexture();
    }
    
    // Select optimal mipmap level based on zoom
    if (!m_mipmapLevels.isEmpty()) {
        selectOptimalMipLevel();
    }
    
    // Early exit if no texture available
    if (!m_texture) {
        return;
    }
    
    // Bind shader program once
    m_shaderProgram->bind();
    
    // Calculate transformation matrix only once
    float imageAspect = (float)m_currentImage.width() / m_currentImage.height();
    float widgetAspect = (float)width() / height();
    
    // Optimized scale calculation
    float scaleX, scaleY;
    if (imageAspect > widgetAspect) {
        scaleX = 1.0f;
        scaleY = widgetAspect / imageAspect;
    } else {
        scaleX = imageAspect / widgetAspect;
        scaleY = 1.0f;
    }
    
    // Apply zoom
    scaleX *= m_zoomFactor;
    scaleY *= m_zoomFactor;
    
    // Create model matrix efficiently
    QMatrix4x4 model;
    model.translate(m_imageOffset.x() / width() * 2.0f, -m_imageOffset.y() / height() * 2.0f, 0.0f);
    model.scale(scaleX, scaleY, 1.0f);
    
    // Use uniform buffer for better performance
    updateMatrixUniforms(model);
      // Set texture unit and bind optimal mipmap level
    glActiveTexture(GL_TEXTURE0);
    if (!m_mipmapLevels.empty() && m_currentMipLevel < m_mipmapLevels.size()) {
        m_mipmapLevels[m_currentMipLevel].texture->bind();
    } else if (m_texture) {
        m_texture->bind();
    }
    
    // Render quad with minimal state changes
    m_vao.bind();
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
    m_vao.release();
    
    // Cleanup
    m_texture->release();
    m_shaderProgram->release();
}

void Zview::resizeGL(int width, int height)
{
    glViewport(0, 0, width, height);
    
    // Set up orthographic projection
    m_projectionMatrix.setToIdentity();
    m_projectionMatrix.ortho(-1.0f, 1.0f, -1.0f, 1.0f, -1.0f, 1.0f);
    
    // Set up view matrix
    m_viewMatrix.setToIdentity();
    
    // Resize video widget if it exists and video is playing
    if (m_videoWidget && m_isPlayingVideo) {
        m_videoWidget->resize(width, height);
    }
    
    setRoundedMask(); // Update mask when window is resized
    repositionButtons(); // Reposition buttons when window is resized
    repositionCropRegion(); // Reposition crop region if active
}

void Zview::mousePressEvent(QMouseEvent *event)
{    // Check for A-B marker dragging first (highest priority)
    bool markerClicked = false;    if (event->button() == Qt::LeftButton) {        
        // Check markers with priority to the one closest to click
        bool aMarkerHit = m_hasAPoint && isPointOnMarker(event->pos(), m_aMarker);
        bool bMarkerHit = m_hasBPoint && isPointOnMarker(event->pos(), m_bMarker);
        
        if (aMarkerHit && bMarkerHit) {
            // Both markers hit - choose the closest one
            QPoint aCenter = m_aMarker->geometry().center();
            QPoint bCenter = m_bMarker->geometry().center();
            
            double distToA = QLineF(event->pos(), aCenter).length();
            double distToB = QLineF(event->pos(), bCenter).length();
            
            if (distToB < distToA) {
                // B is closer
                aMarkerHit = false;
            } else {
                // A is closer or equal
                bMarkerHit = false;
            }
        }
        
        if (aMarkerHit) {
            m_draggingAMarker = true;
            m_draggingBMarker = false; // Ensure B is not dragging
            m_markerDragStartPos = event->pos();
            setCursor(Qt::ClosedHandCursor);
            markerClicked = true;
            return; // Early return to prevent other mouse handling
        }
        
        if (bMarkerHit) {
            m_draggingAMarker = false; // Ensure A is not dragging
            m_draggingBMarker = true;
            m_markerDragStartPos = event->pos();
            setCursor(Qt::ClosedHandCursor);
            markerClicked = true;
            return; // Early return to prevent other mouse handling
        }
    }// Handle expanded seekbar click detection area (skip if marker was clicked)
    if (event->button() == Qt::LeftButton && ui->seekSlider && ui->seekSlider->isVisible() && !markerClicked) {
        QRect seekbarRect = ui->seekSlider->geometry();
        
        // Create expanded click area around the seekbar
        int expandVertical = 30; // Expand 30 pixels above and below
        int expandHorizontal = 10; // Expand 10 pixels left and right
        
        QRect expandedRect = seekbarRect.adjusted(-expandHorizontal, -expandVertical, 
                                                expandHorizontal, expandVertical);        // Check if click is in the expanded area
        if (expandedRect.contains(event->pos())) {
            // Set flag to track expanded seekbar dragging
            m_expandedSeekbarDragging = true;
            
            // Debug output to confirm expanded area detection
            qDebug() << "Expanded seekbar area clicked! Position:" << event->pos() 
                     << "Seekbar rect:" << seekbarRect << "Expanded rect:" << expandedRect;
            
            // Convert click to seekbar coordinates
            QPoint relativePos = event->pos() - seekbarRect.topLeft();
            
            // Make sure the relative position is within the seekbar's horizontal bounds
            if (relativePos.x() >= 0 && relativePos.x() <= seekbarRect.width()) {
                // Calculate the slider value based on horizontal position
                int sliderValue = (relativePos.x() * ui->seekSlider->maximum()) / seekbarRect.width();
                sliderValue = qBound(ui->seekSlider->minimum(), sliderValue, ui->seekSlider->maximum());
                
                // Set the slider value and trigger seeking
                ui->seekSlider->setValue(sliderValue);
                
                // Simulate slider press event for proper seek behavior
                QMouseEvent seekEvent(QEvent::MouseButtonPress, 
                                    QPoint(relativePos.x(), seekbarRect.height()/2),
                                    event->globalPosition(),
                                    Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
                QApplication::sendEvent(ui->seekSlider, &seekEvent);
                
                return; // Consume the event, don't process further
            }
        }
    }
    
    if (event->button() == Qt::MiddleButton) {
        QApplication::quit();
    } else if (event->button() == Qt::RightButton) {        // Start right mouse drag for zooming
        m_isRightDragging = true;
        m_lastMousePos = event->pos();
        m_zoomAnchor = event->pos();
        m_rightClickStartPos = event->pos();
        setCursor(Qt::SizeVerCursor);
    } else if (event->button() == Qt::LeftButton && !markerClicked) {
        // Handle crop tool drawing mode first
        if (m_cropModeActive) {
            
            // Check if clicking on a crop handle first
            int handleIndex = getCropHandleAtPosition(event->pos());
            if (handleIndex >= 0) {
                // Start dragging a handle
                m_draggingHandle = handleIndex;
                m_cropDragStart = event->pos();
                
                // Set appropriate cursor for handle
                if (handleIndex == 0 || handleIndex == 3) setCursor(Qt::SizeFDiagCursor);      // Top-left, Bottom-right
                else if (handleIndex == 1 || handleIndex == 2) setCursor(Qt::SizeBDiagCursor); // Top-right, Bottom-left
                else if (handleIndex == 4 || handleIndex == 5) setCursor(Qt::SizeVerCursor);   // Top, Bottom
                else if (handleIndex == 6 || handleIndex == 7) setCursor(Qt::SizeHorCursor);   // Left, Right
                
                return; // Don't process other mouse handling
            }
            
            // Check if clicking inside existing crop region for moving
            bool inExistingCrop = false;
            if (m_cropOverlay && m_cropOverlay->isVisible()) {
                inExistingCrop = m_cropOverlay->geometry().contains(event->pos());
            }
            
            // Use a smaller tolerance for click detection to prevent accidental crop moves            // Only add 2 pixel tolerance for better precision
            QRect expandedCropRect = m_cropRect.adjusted(-2, -2, 2, 2);
            bool finalInExistingCrop = inExistingCrop || expandedCropRect.contains(event->pos());
              
              if (finalInExistingCrop) {
                // Start moving the crop region
                m_movingCrop = true;
                m_cropMoveStart = event->pos();
                m_cropMoveStartRect = m_cropRect;                setCursor(Qt::SizeAllCursor);
                
                return; // Don't process other mouse handling
            }
              // If we reach here, we're clicking outside the existing crop area
            // This should ALWAYS start drawing a new crop rectangle, clearing the old one
            // Start drawing crop rectangle - always clear existing crop first
            m_drawingCrop = true;
              // Restrict crop start to image bounds
            QRect imageBounds = getImageBounds();
            if (imageBounds.isValid() && !imageBounds.contains(event->pos())) {
                // Clamp the start position to image bounds
                int clampedX = qMax(imageBounds.left(), qMin(event->pos().x(), imageBounds.right()));
                int clampedY = qMax(imageBounds.top(), qMin(event->pos().y(), imageBounds.bottom()));
                m_cropDrawStart = QPoint(clampedX, clampedY);
            } else {
                m_cropDrawStart = event->pos();            }
              // Clear any existing crop region and handles
            if (m_cropOverlay) {
                m_cropOverlay->hide();
                delete m_cropOverlay;
                m_cropOverlay = nullptr;
            }
            
            // Clear relative position data so old crop doesn't reappear during fullscreen transitions
            m_hasCropRelativePosition = false;
            
            // Clear existing handles
            for (QWidget* handle : m_cropHandles) {
                handle->hide();
                delete handle;
            }
            m_cropHandles.clear();
            
            // Create a dummy overlay widget for hit testing (invisible)
            m_cropOverlay = new QWidget(this);
            m_cropOverlay->setStyleSheet("QWidget { background: transparent; }");
            m_cropOverlay->setAttribute(Qt::WA_TransparentForMouseEvents, false);
            
            // Create handles (but keep them hidden until crop is finished)
            createCropHandles();
              // Set initial crop rectangle (single point)
            m_cropRect = QRect(m_cropDrawStart, QSize(0, 0));
            m_cropOverlay->setGeometry(m_cropRect);
            m_cropOverlay->show();            storeCropRelativePosition(); // Store initial position
            
            return; // Don't process other mouse handling
        }
        
        // Check if we're starting a resize operation
        m_resizeEdge = getResizeEdge(event->pos());
        
        if (m_resizeEdge != 0 && !isFullScreen()) {
            // Start resize operation
            m_isResizing = true;
            m_resizeStartPos = event->globalPosition().toPoint();
            m_resizeStartGeometry = geometry();
            return; // Don't process as normal left click
        }// Start left mouse drag
        m_isLeftDragging = true;
        m_actualDragOccurred = false; // Reset drag flag
        m_leftDragStartPos = event->globalPosition().toPoint();  // Use global coordinates
        m_lastMousePos = event->pos();
        m_panDragStartPos = event->pos(); // Store local start position for click detection
          // Determine drag mode based on zoom level and image availability
        if ((m_currentImage.isNull() || qAbs(m_zoomFactor - 1.0f) < 0.01f) && !isFullScreen()) {
            // No image loaded OR image is fit-to-window and in windowed mode: drag window
            m_windowDragStartPos = pos();  // Current window position
            m_lastWindowPos = pos();       // Initialize last position
            setCursor(Qt::SizeAllCursor);        } else {
            // Image is zoomed in: pan image
            m_panStartOffset = m_imageOffset; // Store initial offset for sticky panning
            m_panDragStartPos = event->pos(); // Store local start position
            
            // Calculate what image point is under the cursor when drag starts
            // This is critical for extreme sticky panning
            float imageAspect = (float)m_currentImage.width() / m_currentImage.height();
            float widgetAspect = (float)width() / height();
            
            // Calculate base scale (same logic as in paintGL)
            float baseScaleX, baseScaleY;
            if (imageAspect > widgetAspect) {
                baseScaleX = 1.0f;
                baseScaleY = widgetAspect / imageAspect;
            } else {
                baseScaleX = imageAspect / widgetAspect;
                baseScaleY = 1.0f;
            }
            
            // Convert mouse position to normalized coordinates (-1 to 1)
            float mouseNormX = (event->pos().x() / (float)width()) * 2.0f - 1.0f;
            float mouseNormY = -((event->pos().y() / (float)height()) * 2.0f - 1.0f); // Flip Y for OpenGL
            
            // Account for current offset in normalized coordinates
            float offsetNormX = m_imageOffset.x() / width() * 2.0f;
            float offsetNormY = -m_imageOffset.y() / height() * 2.0f;
            
            // Calculate the image coordinates (-1 to 1 in image space) under the cursor
            float imagePointX = (mouseNormX - offsetNormX) / (baseScaleX * m_zoomFactor);
            float imagePointY = (mouseNormY - offsetNormY) / (baseScaleY * m_zoomFactor);
            
            // Store this point - this is what will stay locked to the cursor
            m_panImagePoint = QPointF(imagePointX, imagePointY);
            
            setCursor(Qt::ClosedHandCursor);
        }
    }
    
    // Call parent implementation for other mouse events
    QOpenGLWidget::mousePressEvent(event);
}

void Zview::mouseMoveEvent(QMouseEvent *event)
{    
    // Debug all crop-related states
      // SAFETY CHECK: If we think we're moving crop but left button isn't pressed, stop moving
    if (m_movingCrop && !(event->buttons() & Qt::LeftButton)) {
        m_movingCrop = false;
        
        // Trigger immediate cursor update
        QMouseEvent safetyMoveEvent(QEvent::MouseMove, event->pos(), event->globalPosition(), 
                                  Qt::NoButton, Qt::NoButton, Qt::NoModifier);
        // Don't call recursively, just do the cursor logic inline
        if (m_cropModeActive && !m_drawingCrop && !m_movingCrop && m_draggingHandle < 0 && 
            !(event->buttons() & Qt::LeftButton) && !m_draggingAMarker && !m_draggingBMarker) {
            
            int handleIndex = getCropHandleAtPosition(event->pos());
            bool inCropArea = false;
            if (m_cropOverlay && m_cropOverlay->isVisible()) {
                inCropArea = m_cropOverlay->geometry().contains(event->pos());
            }            QRect expandedCropRect = m_cropRect.adjusted(-2, -2, 2, 2);
            bool finalInCropArea = inCropArea || expandedCropRect.contains(event->pos());
            
            if (handleIndex >= 0) {
                Qt::CursorShape resizeCursor = Qt::ArrowCursor;
                if (handleIndex == 0 || handleIndex == 3) {
                    resizeCursor = Qt::SizeFDiagCursor;
                } else if (handleIndex == 1 || handleIndex == 2) {
                    resizeCursor = Qt::SizeBDiagCursor;
                } else if (handleIndex == 4 || handleIndex == 5) {
                    resizeCursor = Qt::SizeVerCursor;
                } else if (handleIndex == 6 || handleIndex == 7) {
                    resizeCursor = Qt::SizeHorCursor;
                }
                setCursor(resizeCursor);
            } else if (finalInCropArea) {
                setCursor(Qt::SizeAllCursor);
            } else {
                setCursor(Qt::CrossCursor);
            }
            return; // Early return after safety fix
        }
    }    // Handle crop mode cursor changes FIRST (highest priority when not dragging)
    if (m_cropModeActive && !m_drawingCrop && !m_movingCrop && m_draggingHandle < 0 && 
        !(event->buttons() & Qt::LeftButton) && !m_draggingAMarker && !m_draggingBMarker) {
        
        // Handle toolbox hover even in crop mode
        if (!m_ignoreToolboxHover && isMouseInToolboxArea(event->pos())) {
            showToolbox();
            if (m_toolboxHideTimer) {
                m_toolboxHideTimer->stop();
            }
        } else if (!isMouseInToolboxArea(event->pos())) {
            // Mouse left toolbox area - hide immediately
            if (m_toolboxVisible) {
                if (m_toolboxHideTimer) {
                    m_toolboxHideTimer->stop();
                }
                hideToolbox(true);
            }
        }
        
        // Store current position for timer-based cursor update
        m_lastCursorUpdatePos = event->pos();
        
        // Immediate cursor update for responsive feel
        int handleIndex = getCropHandleAtPosition(event->pos());
          // Check if we're over the crop area
        bool inCropArea = false;
        if (m_cropOverlay && m_cropOverlay->isVisible()) {
            inCropArea = m_cropOverlay->geometry().contains(event->pos());
        }
          // Use moderate tolerance for cursor detection - 2 pixels to ensure good UX
        // Now that handle detection is more precise, we can be more generous with crop area
        QRect expandedCropRect = m_cropRect.adjusted(-2, -2, 2, 2);
        bool finalInCropArea = inCropArea || expandedCropRect.contains(event->pos());
        
        // Determine target cursor
        Qt::CursorShape targetCursor = Qt::CrossCursor;
        if (handleIndex >= 0) {
            // Hovering over a handle - set appropriate resize cursor
            if (handleIndex == 0 || handleIndex == 3) {
                targetCursor = Qt::SizeFDiagCursor;      // Top-left, Bottom-right (NW-SE)
            } else if (handleIndex == 1 || handleIndex == 2) {
                targetCursor = Qt::SizeBDiagCursor;      // Top-right, Bottom-left (NE-SW)
            } else if (handleIndex == 4 || handleIndex == 5) {
                targetCursor = Qt::SizeVerCursor;        // Top, Bottom (N-S)
            } else if (handleIndex == 6 || handleIndex == 7) {
                targetCursor = Qt::SizeHorCursor;        // Left, Right (E-W)
            }
        } else if (finalInCropArea) {
            // Not on a handle but inside crop area - show move cursor
            targetCursor = Qt::SizeAllCursor;
        }
          // Set cursor immediately and track state
        if (targetCursor != m_currentCursorShape) {
            setCursor(targetCursor);
            m_currentCursorShape = targetCursor;
        }
        
        // Start continuous timer for aggressive cursor validation during crop mode
        if (m_cursorUpdateTimer && !m_cursorUpdateTimer->isActive()) {
            m_cursorUpdateTimer->start();
        }
        
        return; // Always return early in crop mode to prevent other cursor logic from overriding
    }

    // Handle A-B marker dragging
    if ((m_draggingAMarker || m_draggingBMarker) && (event->buttons() & Qt::LeftButton)) {
        if (m_draggingAMarker) {
            updateMarkerTimeFromPosition(m_aMarker, event->pos(), true);
        } else if (m_draggingBMarker) {
            updateMarkerTimeFromPosition(m_bMarker, event->pos(), false);
        }
    }
    
    // Safety check: if we think we're dragging markers but left button isn't pressed, stop dragging
    if ((m_draggingAMarker || m_draggingBMarker) && !(event->buttons() & Qt::LeftButton)) {
        m_draggingAMarker = false;
        m_draggingBMarker = false;
        m_expandedSeekbarDragging = false;
        setCursor(Qt::ArrowCursor);
    }
    
    // Safety check: if we think we're dragging but left button isn't pressed, stop dragging
    if (m_expandedSeekbarDragging && !(event->buttons() & Qt::LeftButton)) {
        m_expandedSeekbarDragging = false;
    }

    // Check if mouse is in control area when playing video
    if (m_isPlayingVideo) {
        if (m_draggingAMarker || m_draggingBMarker) {
            // If we're dragging markers, always keep controls visible
            if (!m_controlsVisible) {
                showControls();
            }
            ensureControlHideTimer();
            if (m_controlHideTimer) {
                m_controlHideTimer->start();
            }
        } else if (isMouseInControlArea(event->pos())) {
            // Mouse is in control area - show controls and stop hide timer
            showControls();
            if (m_controlHideTimer) {
                m_controlHideTimer->stop();
            }
            
            // Update cursor and marker appearance based on hover (but not in crop mode)
            if (m_controlsVisible && !m_draggingAMarker && !m_draggingBMarker && !m_expandedSeekbarDragging && !m_cropModeActive) {
                bool hoveringOverMarker = false;
                
                if (m_hasAPoint && isPointOnMarker(event->pos(), m_aMarker)) {
                    setCursor(Qt::PointingHandCursor);
                    hoveringOverMarker = true;
                } else if (m_hasBPoint && isPointOnMarker(event->pos(), m_bMarker)) {
                    setCursor(Qt::PointingHandCursor);
                    hoveringOverMarker = true;
                }
                
                if (!hoveringOverMarker) {
                    setCursor(Qt::ArrowCursor);
                }
            }
        } else if (!m_seekbarDragging && !m_expandedSeekbarDragging && !m_draggingAMarker && !m_draggingBMarker) {
            // Mouse left control area - hide controls immediately
            if (m_controlsVisible) {
                if (m_controlHideTimer) {
                    m_controlHideTimer->stop();
                }
                hideControls();
            }
            
            // Only set arrow cursor if not in crop mode (crop mode handles its own cursor)
            if (!m_cropModeActive) {
                setCursor(Qt::ArrowCursor);
            }
        }
    }    // Handle crop tool interactions
    if (m_cropModeActive && (event->buttons() & Qt::LeftButton)) {
        if (m_draggingHandle >= 0) {
            // Update crop rectangle based on handle drag
            updateCropRectFromHandle(m_draggingHandle, event->pos());
            return;        } else if (m_movingCrop) {
            // Move the entire crop region
            QPoint currentPos = event->pos();
            QPoint delta = currentPos - m_cropMoveStart;
            
            QRect newCropRect = m_cropMoveStartRect.translated(delta);
            
            // Ensure crop stays within image bounds
            QRect imageBounds = getImageBounds();
            if (imageBounds.isValid()) {
                if (newCropRect.left() < imageBounds.left()) {
                    newCropRect.moveLeft(imageBounds.left());
                }
                if (newCropRect.top() < imageBounds.top()) {
                    newCropRect.moveTop(imageBounds.top());
                }
                if (newCropRect.right() > imageBounds.right()) {
                    newCropRect.moveRight(imageBounds.right());
                }
                if (newCropRect.bottom() > imageBounds.bottom()) {
                    newCropRect.moveBottom(imageBounds.bottom());
                }
            }
            
            m_cropRect = newCropRect;
            if (m_cropOverlay) {
                m_cropOverlay->setGeometry(m_cropRect);
                updateCropOverlay();
            }
            storeCropRelativePosition();
              // Update cursor during move - if mouse is still in the crop area, keep move cursor
            // If mouse has moved outside, show different cursor to indicate where it will end up
            bool stillInCropArea = m_cropRect.contains(event->pos());
            if (!stillInCropArea) {
                // Mouse is outside the crop area while moving - show crosshair to indicate 
                // that releasing here will allow drawing a new crop
                setCursor(Qt::CrossCursor);
            } else {
                // Still inside, keep move cursor
                setCursor(Qt::SizeAllCursor);
            }
            
            update();
            return;
        } else if (m_drawingCrop) {
            // Update crop rectangle as user drags
            QPoint currentPos = event->pos();
            QPoint constrainedCurrentPos = currentPos;
            
            // Constrain current position to image bounds
            QRect imageBounds = getImageBounds();
            if (imageBounds.isValid()) {
                constrainedCurrentPos.setX(qMax(imageBounds.left(), qMin(currentPos.x(), imageBounds.right())));
                constrainedCurrentPos.setY(qMax(imageBounds.top(), qMin(currentPos.y(), imageBounds.bottom())));
            }
            
            int left = qMin(m_cropDrawStart.x(), constrainedCurrentPos.x());
            int top = qMin(m_cropDrawStart.y(), constrainedCurrentPos.y());
            int width = qAbs(constrainedCurrentPos.x() - m_cropDrawStart.x());
            int height = qAbs(constrainedCurrentPos.y() - m_cropDrawStart.y());
            
            m_cropRect = QRect(left, top, width, height);
            
            if (imageBounds.isValid()) {
                m_cropRect = m_cropRect.intersected(imageBounds);
            }
            
            if (m_cropOverlay) {
                m_cropOverlay->setGeometry(m_cropRect);
                updateCropOverlay();
            }
            storeCropRelativePosition();
            update();
            return;
        }
    }

    // Handle expanded seekbar dragging
    if (m_expandedSeekbarDragging && ui->seekSlider && ui->seekSlider->isVisible() && 
        (event->buttons() & Qt::LeftButton)) {
        QRect seekbarRect = ui->seekSlider->geometry();
        QPoint relativePos = event->pos() - seekbarRect.topLeft();
        
        if (relativePos.x() >= 0 && relativePos.x() <= seekbarRect.width()) {
            int sliderValue = (relativePos.x() * ui->seekSlider->maximum()) / seekbarRect.width();
            sliderValue = qBound(ui->seekSlider->minimum(), sliderValue, ui->seekSlider->maximum());
            
            ui->seekSlider->setValue(sliderValue);
            
            QMouseEvent seekEvent(QEvent::MouseMove, 
                                QPoint(relativePos.x(), seekbarRect.height()/2),
                                event->globalPosition(),
                                Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
            QApplication::sendEvent(ui->seekSlider, &seekEvent);
        }
        return;
    }
    
    // Handle window resizing
    if (m_isResizing) {
        QPoint globalPos = event->globalPosition().toPoint();
        QPoint delta = globalPos - m_resizeStartPos;
        QRect newGeometry = m_resizeStartGeometry;
        
        if (m_resizeEdge & 1) { // Left edge
            newGeometry.setLeft(newGeometry.left() + delta.x());
        }
        if (m_resizeEdge & 2) { // Right edge
            newGeometry.setRight(newGeometry.right() + delta.x());
        }
        if (m_resizeEdge & 4) { // Top edge
            newGeometry.setTop(newGeometry.top() + delta.y());
        }
        if (m_resizeEdge & 8) { // Bottom edge
            newGeometry.setBottom(newGeometry.bottom() + delta.y());
        }
        
        const int minSize = 200;
        if (newGeometry.width() < minSize) {
            if (m_resizeEdge & 1) {
                newGeometry.setLeft(newGeometry.right() - minSize);
            } else {
                newGeometry.setRight(newGeometry.left() + minSize);
            }
        }
        if (newGeometry.height() < minSize) {
            if (m_resizeEdge & 4) {
                newGeometry.setTop(newGeometry.bottom() - minSize);
            } else {
                newGeometry.setBottom(newGeometry.top() + minSize);
            }
        }
        
        setGeometry(newGeometry);
        return;
    }

    // Handle toolbox hover when not in crop mode
    if (!m_cropModeActive) {
        if (!m_ignoreToolboxHover && isMouseInToolboxArea(event->pos())) {
            showToolbox();
            if (m_toolboxHideTimer) {
                m_toolboxHideTimer->stop();
            }
        } else if (!isMouseInToolboxArea(event->pos())) {
            // Mouse left toolbox area - hide immediately
            if (m_toolboxVisible) {
                if (m_toolboxHideTimer) {
                    m_toolboxHideTimer->stop();
                }
                hideToolbox(true);
            }
        }
    }

    // Update cursor for resize detection when not dragging and not in crop mode
    if (!m_isLeftDragging && !m_isRightDragging && !m_isResizing && !isFullScreen() && !m_cropModeActive) {
        updateCursorForResize(event->pos());
    }
    
    if (m_isRightDragging && !m_currentImage.isNull()) {
        // Calculate zoom based on vertical mouse movement
        int deltaY = event->pos().y() - m_lastMousePos.y();
        float zoomDelta = 1.0f + (-deltaY * 0.005f);
        
        float oldZoom = m_zoomFactor;
        m_zoomFactor *= zoomDelta;
        m_zoomFactor = qMax(0.1f, qMin(m_zoomFactor, 20.0f));
        
        float imageAspect = (float)m_currentImage.width() / m_currentImage.height();
        float widgetAspect = (float)width() / height();
        
        float baseScaleX, baseScaleY;
        if (imageAspect > widgetAspect) {
            baseScaleX = 1.0f;
            baseScaleY = widgetAspect / imageAspect;
        } else {
            baseScaleX = imageAspect / widgetAspect;
            baseScaleY = 1.0f;
        }
        
        float anchorNormX = (m_zoomAnchor.x() / (float)width()) * 2.0f - 1.0f;
        float anchorNormY = -((m_zoomAnchor.y() / (float)height()) * 2.0f - 1.0f);
        
        float offsetNormX = m_imageOffset.x() / width() * 2.0f;
        float offsetNormY = -m_imageOffset.y() / height() * 2.0f;
        
        float imagePointX = (anchorNormX - offsetNormX) / (baseScaleX * oldZoom);
        float imagePointY = (anchorNormY - offsetNormY) / (baseScaleY * oldZoom);
        
        float newOffsetNormX = anchorNormX - (imagePointX * baseScaleX * m_zoomFactor);
        float newOffsetNormY = anchorNormY - (imagePointY * baseScaleY * m_zoomFactor);
        
        m_imageOffset = QPointF(
            newOffsetNormX * width() / 2.0f,
            -newOffsetNormY * height() / 2.0f
        );
        
        m_lastMousePos = event->pos();
        update();
    } else if (m_isLeftDragging) {
        QPoint delta = event->pos() - m_lastMousePos;
        
        if ((m_currentImage.isNull() || qAbs(m_zoomFactor - 1.0f) < 0.01f) && !isFullScreen()) {
            // No image loaded OR image is fit-to-window and in windowed mode: drag window
            QPoint currentGlobalPos = event->globalPosition().toPoint();
            QPoint globalDelta = currentGlobalPos - m_leftDragStartPos;
            QPoint newWindowPos = m_windowDragStartPos + globalDelta;
            
            if (newWindowPos != m_lastWindowPos) {
                move(newWindowPos);
                m_lastWindowPos = newWindowPos;
                
                QPoint totalDelta = newWindowPos - m_windowDragStartPos;
                double dragDistance = std::sqrt(totalDelta.x() * totalDelta.x() + totalDelta.y() * totalDelta.y());
                if (dragDistance > 3.0) {
                    m_actualDragOccurred = true;
                }
            }
        } else if (!m_currentImage.isNull()) {
            // Image panning
            float imageAspect = (float)m_currentImage.width() / m_currentImage.height();
            float widgetAspect = (float)width() / height();
            
            float baseScaleX, baseScaleY;
            if (imageAspect > widgetAspect) {
                baseScaleX = 1.0f;
                baseScaleY = widgetAspect / imageAspect;
            } else {
                baseScaleX = imageAspect / widgetAspect;
                baseScaleY = 1.0f;
            }
            
            float mouseNormX = (event->pos().x() / (float)width()) * 2.0f - 1.0f;
            float mouseNormY = -((event->pos().y() / (float)height()) * 2.0f - 1.0f);
            
            float offsetNormX = mouseNormX - (m_panImagePoint.x() * baseScaleX * m_zoomFactor);
            float offsetNormY = mouseNormY - (m_panImagePoint.y() * baseScaleY * m_zoomFactor);
            
            m_imageOffset = QPointF(
                offsetNormX * width() / 2.0f,
                -offsetNormY * height() / 2.0f
            );
            
            QPoint panDelta = event->pos() - m_panDragStartPos;
            double panDistance = std::sqrt(panDelta.x() * panDelta.x() + panDelta.y() * panDelta.y());
            if (panDistance > 3.0) {
                m_actualDragOccurred = true;
            }
            
            update();
        }
          m_lastMousePos = event->pos();
    }
    
    // Force crop cursor override if in forced mode - this ensures crop cursor takes absolute priority
    if (m_forceCropCursor && m_cropModeActive) {
        // Immediately re-validate cursor position to override any other cursor changes
        QPoint globalCursorPos = QCursor::pos();
        QPoint currentPos = mapFromGlobal(globalCursorPos);
        
        Qt::CursorShape correctCursor = Qt::CrossCursor;
        
        if (m_cropOverlay && m_cropOverlay->isVisible()) {
            int handleIndex = getCropHandleAtPosition(currentPos);
            bool inCropArea = m_cropOverlay->geometry().contains(currentPos);
            QRect expandedCropRect = m_cropRect.adjusted(-4, -4, 4, 4);
            bool finalInCropArea = inCropArea || expandedCropRect.contains(currentPos);
            
            if (handleIndex >= 0) {
                if (handleIndex == 0 || handleIndex == 3) correctCursor = Qt::SizeFDiagCursor;
                else if (handleIndex == 1 || handleIndex == 2) correctCursor = Qt::SizeBDiagCursor;
                else if (handleIndex == 4 || handleIndex == 5) correctCursor = Qt::SizeVerCursor;
                else if (handleIndex == 6 || handleIndex == 7) correctCursor = Qt::SizeHorCursor;
            } else if (finalInCropArea) {
                correctCursor = Qt::SizeAllCursor;
            }
        }
        
        // Force the cursor to be correct
        if (cursor().shape() != correctCursor) {
            setCursor(correctCursor);
        }
    }
    
    QOpenGLWidget::mouseMoveEvent(event);
}

void Zview::mouseReleaseEvent(QMouseEvent *event)
{    // Handle A-B marker dragging release
    if (event->button() == Qt::LeftButton && (m_draggingAMarker || m_draggingBMarker)) {
        qDebug() << "Marker release detected. Before: expandedSeekbarDragging=" << m_expandedSeekbarDragging;
        
        // Final position update
        if (m_draggingAMarker) {
            updateMarkerTimeFromPosition(m_aMarker, event->pos(), true);
        } else if (m_draggingBMarker) {
            updateMarkerTimeFromPosition(m_bMarker, event->pos(), false);
        }        // Reset ALL dragging states
        m_draggingAMarker = false;
        m_draggingBMarker = false;
        m_expandedSeekbarDragging = false; // Force stop seekbar dragging
        
        qDebug() << "After reset: expandedSeekbarDragging=" << m_expandedSeekbarDragging;
        
        setCursor(Qt::ArrowCursor);
        
        return; // Consume the event to prevent seekbar release logic from running
    }
    
    // Handle expanded seekbar release
    if (event->button() == Qt::LeftButton && m_expandedSeekbarDragging) {
        m_expandedSeekbarDragging = false;
        
        if (ui->seekSlider && ui->seekSlider->isVisible()) {
            QRect seekbarRect = ui->seekSlider->geometry();
            
            // Convert mouse position to seekbar coordinates for final position
            QPoint relativePos = event->pos() - seekbarRect.topLeft();
            
            // Make sure the relative position is within the seekbar's horizontal bounds
            if (relativePos.x() >= 0 && relativePos.x() <= seekbarRect.width()) {
                // Simulate slider release event for proper seek behavior
                QMouseEvent seekEvent(QEvent::MouseButtonRelease, 
                                    QPoint(relativePos.x(), seekbarRect.height()/2),
                                    event->globalPosition(),
                                    Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
                QApplication::sendEvent(ui->seekSlider, &seekEvent);
            }
        }
        return; // Consume the event, don't process further
    }
    
    if (event->button() == Qt::RightButton) {
        bool wasRightDragging = m_isRightDragging;
        m_isRightDragging = false;
        setCursor(Qt::ArrowCursor);
        
        // Check if this was a single click (no significant movement)
        QPoint mouseDelta = event->pos() - m_rightClickStartPos;
        double distance = std::sqrt(mouseDelta.x() * mouseDelta.x() + mouseDelta.y() * mouseDelta.y());
        
        // Debug output
        qDebug() << "Right-click release: distance=" << distance << "hasImage=" << !m_currentImage.isNull() << "wasRightDragging=" << wasRightDragging;
        
        // If mouse didn't move much (threshold of 5 pixels), treat as single click
        if (distance <= 5.0 && !m_currentImage.isNull()) {
            // Single right-click: fit image to window
            qDebug() << "Triggering fit-to-window";
            resetZoom();
            update();
        }    } else if (event->button() == Qt::LeftButton && m_draggingHandle >= 0) {
        // Finish dragging crop handle
        m_draggingHandle = -1;        
        // Trigger mouse move to update cursor based on current position
        QMouseEvent handleMoveEvent(QEvent::MouseMove, event->pos(), event->globalPosition(), 
                                  Qt::NoButton, Qt::NoButton, Qt::NoModifier);
        mouseMoveEvent(&handleMoveEvent);
        
        return; // Consume the event
    } else if (event->button() == Qt::LeftButton && m_drawingCrop) {
        // Finish drawing crop rectangle
        m_drawingCrop = false;
        
        // Only show handles if we have a meaningful crop area
        if (m_cropRect.width() > 10 && m_cropRect.height() > 10) {
            // Show crop handles
            for (QWidget* handle : m_cropHandles) {
                handle->show();
            }            updateCropOverlay();
            
        } else {
            // Too small, hide the overlay and clear relative position data
            if (m_cropOverlay) {
                m_cropOverlay->hide();
            }
            // Clear relative position data so crop doesn't reappear during fullscreen transitions
            m_hasCropRelativePosition = false;
        }
          
        // Trigger mouse move to update cursor based on current position  
        QMouseEvent fakeMoveEvent(QEvent::MouseMove, event->pos(), event->globalPosition(), 
                                Qt::NoButton, Qt::NoButton, Qt::NoModifier);
        mouseMoveEvent(&fakeMoveEvent);
        
        return; // Consume the event    } else if (event->button() == Qt::LeftButton && m_movingCrop) {
        // Finish moving crop rectangle
        m_movingCrop = false;
        
        // Trigger mouse move to update cursor based on current position
        QMouseEvent cropMoveEvent(QEvent::MouseMove, event->pos(), event->globalPosition(), 
                                Qt::NoButton, Qt::NoButton, Qt::NoModifier);
        qDebug() << "Triggering fake mouse move event to update cursor";
        mouseMoveEvent(&cropMoveEvent);
        
        qDebug() << "Finished moving crop rectangle to:" << m_cropRect;
        return; // Consume the event
    } else if (event->button() == Qt::LeftButton && m_isLeftDragging) {
        bool wasLeftDragging = m_isLeftDragging;
        m_isLeftDragging = false;
        setCursor(Qt::ArrowCursor);
          // Only trigger play/pause if we're playing a video AND no actual dragging occurred
        if (m_isPlayingVideo && m_mpvPlayer && !m_actualDragOccurred) {
            QPoint mouseDelta = event->pos() - m_panDragStartPos;
            double distance = std::sqrt(mouseDelta.x() * mouseDelta.x() + mouseDelta.y() * mouseDelta.y());
            
            // If mouse didn't move much (threshold of 10 pixels), treat as single click to toggle play/pause
            if (distance <= 10.0) {
                if (m_mpvPlayer->isPaused()) {
                    m_mpvPlayer->play();
                    qDebug() << "Left click: Video resumed";
                } else {
                    m_mpvPlayer->pause();
                    qDebug() << "Left click: Video paused";
                }
            }
        }
    } else if (event->button() == Qt::LeftButton && m_isResizing) {
        // End resize operation
        m_isResizing = false;
        setCursor(Qt::ArrowCursor);
    }
    
    QOpenGLWidget::mouseReleaseEvent(event);
}

void Zview::mouseDoubleClickEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // Stop any left dragging in progress
        m_isLeftDragging = false;
        setCursor(Qt::ArrowCursor);
          // Toggle fullscreen mode
        if (isFullScreen()) {
            showNormal();
        } else {
            showFullScreen();
        }          // Update mask after changing fullscreen state
        setRoundedMask();
        repositionButtons();  // Reposition buttons after fullscreen toggle
        
        // Use a timer to delay crop repositioning until after the window has fully resized
        QTimer::singleShot(100, this, [this]() {
            repositionCropRegion();  // Reposition crop region after fullscreen toggle
        });
        // No need for complex video widget handling with OpenGL rendering!
        // Video will automatically render correctly in fullscreen
        
        event->accept();
    } else {
        // Call parent implementation for other mouse events
        QOpenGLWidget::mouseDoubleClickEvent(event);
    }
}

void Zview::wheelEvent(QWheelEvent *event)
{
    if (!m_imageList.isEmpty()) {
        if (event->angleDelta().y() > 0) {
            // Scroll up - previous image
            loadPreviousImage();
        } else {
            // Scroll down - next image
            loadNextImage();
        }
        event->accept();
    } else {
        QOpenGLWidget::wheelEvent(event);
    }
}

void Zview::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Escape:
        // Cancel crop mode with ESC key
        if (m_cropModeActive) {
            cancelCrop();
            event->accept();
            return;
        }
        break;
    
    case Qt::Key_Space:
        // Play/pause video with spacebar
        if (m_isPlayingVideo && m_mpvPlayer) {
            if (m_mpvPlayer->isPaused()) {
                m_mpvPlayer->play();
                qDebug() << "Spacebar: Video resumed";
            } else {
                m_mpvPlayer->pause();
                qDebug() << "Spacebar: Video paused";
            }
        }
        event->accept();
        break;    case Qt::Key_R:
        // Reset zoom with 'R' key
        if (!m_currentImage.isNull()) {
            resetZoom();
            update();
        }
        event->accept();
        break;
    case Qt::Key_L:
        // Toggle video loop with 'L' key
        toggleVideoLoop();
        event->accept();
        break;case Qt::Key_Plus:
    case Qt::Key_Equal:
        // Extend video duration with '+' key
        if (m_isPlayingVideo && m_mpvPlayer) {
            // Add 30 seconds to duration
            double currentDuration = m_mpvPlayer->getDuration();
            double newDuration = currentDuration + 30.0;
            m_mpvPlayer->adjustDuration(newDuration);
            qDebug() << "Extended duration by 30 seconds to:" << newDuration;
        }
        event->accept();
        break;
    case Qt::Key_Minus:
        // Reduce video duration with '-' key  
        if (m_isPlayingVideo && m_mpvPlayer) {
            // Subtract 30 seconds from duration (minimum 10 seconds)
            double currentDuration = m_mpvPlayer->getDuration();
            double newDuration = qMax(10.0, currentDuration - 30.0);
            m_mpvPlayer->adjustDuration(newDuration);
            qDebug() << "Reduced duration by 30 seconds to:" << newDuration;        }
        event->accept();
        break;
    case Qt::Key_Up:
        // Increase volume with Up arrow key
        if (m_isPlayingVideo && ui->volumeSlider) {
            int currentVolume = ui->volumeSlider->value();
            int newVolume = qMin(100, currentVolume + 5);
            ui->volumeSlider->setValue(newVolume);
            qDebug() << "Volume increased to:" << newVolume << "%";
        }
        event->accept();
        break;
    case Qt::Key_Down:
        // Decrease volume with Down arrow key
        if (m_isPlayingVideo && ui->volumeSlider) {
            int currentVolume = ui->volumeSlider->value();
            int newVolume = qMax(0, currentVolume - 5);
            ui->volumeSlider->setValue(newVolume);
            qDebug() << "Volume decreased to:" << newVolume << "%";
        }
        event->accept();
        break;
    default:
        QOpenGLWidget::keyPressEvent(event);
        break;
    }
}

void Zview::paintEvent(QPaintEvent *event)
{
    // First, let OpenGL render the image content
    QOpenGLWidget::paintEvent(event);
    
    // Then overlay the border and rounded corners using QPainter
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
      // Draw crop overlay if in crop mode and we have a crop region
    if (m_cropModeActive && !m_cropRect.isEmpty() && (m_drawingCrop || m_cropOverlay)) {
        // Get the actual image bounds to constrain overlay to image area only
        QRect imageBounds = getImageBounds();
        
        if (imageBounds.isValid()) {
            // Create inverted overlay: cover only the image area except the crop region
            QPainterPath fullPath;
            fullPath.addRect(imageBounds); // Only image area, not entire widget
            
            QPainterPath cropPath;
            cropPath.addRect(m_cropRect); // Crop region to exclude
            
            // Subtract crop region from image area to create inverted overlay
            QPainterPath overlayPath = fullPath.subtracted(cropPath);
            
            // Fill the overlay area with semi-transparent blue
            painter.fillPath(overlayPath, QColor(0, 120, 215, 80)); // More transparent than before
        }
        
        // Draw crop region border
        QPen cropPen(QColor(0, 120, 215, 230), 2); // Solid blue border
        painter.setPen(cropPen);
        painter.setBrush(Qt::NoBrush);
        painter.drawRect(m_cropRect);
    }
    
    // Only draw window border in windowed mode
    if (!isFullScreen()) {
        // Create path for rounded corners
        QPainterPath path;
        path.addRoundedRect(rect(), m_cornerRadius, m_cornerRadius);        // Draw dark gray border
        QPen pen(QColor(60, 60, 60, 255), 2);  // Slightly lighter gray border
        painter.setPen(pen);
        painter.setBrush(Qt::NoBrush);
        painter.drawPath(path);
    }
}

void Zview::showEvent(QShowEvent *event)
{
    QOpenGLWidget::showEvent(event);
    setRoundedMask();
    repositionButtons();  // Ensure buttons are positioned correctly when shown
}

void Zview::setRoundedMask()
{
    // Only apply rounded mask in windowed mode, not in fullscreen
    if (isFullScreen()) {
        // Clear any existing mask in fullscreen mode
        clearMask();
        return;
    }
    
    // Create a mask for rounded corners - only in windowed mode
    QBitmap mask(size());
    mask.fill(Qt::color0);
    
    QPainter painter(&mask);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setBrush(Qt::color1);
    painter.setPen(Qt::NoPen);
    
    // Draw rounded rectangle on mask - account for border width
    QRect maskRect = rect().adjusted(0, 0, 0, 0);  // Keep full size for border visibility
    painter.drawRoundedRect(maskRect, m_cornerRadius, m_cornerRadius);
    
    // Apply the mask to the widget
    setMask(mask);
}

void Zview::dragEnterEvent(QDragEnterEvent *event)
{
    // Check if the dragged data contains URLs (files)
    if (event->mimeData()->hasUrls()) {
        // Check if any of the URLs are image or video files
        QList<QUrl> urls = event->mimeData()->urls();
        for (const QUrl &url : urls) {
            if (url.isLocalFile()) {
                QString filePath = url.toLocalFile();
                QFileInfo fileInfo(filePath);
                
                // Check if it's a video file
                if (isVideoFile(filePath)) {
                    event->acceptProposedAction();
                    return;
                }
                
                // Check if it's a HEIC file
                if (HeicImageLoader::isHeicFile(filePath)) {
                    event->acceptProposedAction();
                    return;
                }
                
                // Check if it's an image file
                QStringList supportedFormats;
                for (const QByteArray &format : QImageReader::supportedImageFormats()) {
                    supportedFormats << QString::fromUtf8(format).toLower();
                }
                
                if (supportedFormats.contains(fileInfo.suffix().toLower())) {
                    event->acceptProposedAction();
                    return;
                }
            }
        }
    }
    event->ignore();
}

void Zview::dropEvent(QDropEvent *event)
{
    // Handle dropped files
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        for (const QUrl &url : urls) {
            if (url.isLocalFile()) {
                QString filePath = url.toLocalFile();
                QFileInfo fileInfo(filePath);
                
                // Check if it's a video file first
                if (isVideoFile(filePath)) {
                    loadVideo(filePath);
                    event->acceptProposedAction();
                    return;
                }
                
                // Check if it's a HEIC file
                if (HeicImageLoader::isHeicFile(filePath)) {
                    loadImage(filePath);  // loadImage now handles HEIC files
                    event->acceptProposedAction();
                    return;
                }
                
                // Check if it's an image file
                QStringList supportedFormats;
                for (const QByteArray &format : QImageReader::supportedImageFormats()) {
                    supportedFormats << QString::fromUtf8(format).toLower();
                }
                
                if (supportedFormats.contains(fileInfo.suffix().toLower())) {
                    loadImage(filePath);
                    event->acceptProposedAction();
                    return;
                }
            }
        }
    }
    event->ignore();
}

void Zview::loadImage(const QString &filePath)
{
    // Stop any video playback first
    if (m_isPlayingVideo) {
        if (m_mpvPlayer) {
            m_mpvPlayer->stop();
        }
        m_isPlayingVideo = false;        
        // Show main toolbox in image mode, hide crop toolbox unless in crop mode
        if (ui->modernToolbox) {
            ui->modernToolbox->setVisible(true);
        }
        if (ui->modernToolbox_2 && !m_cropModeActive) {
            ui->modernToolbox_2->setVisible(false);
        }
        
        // Set toolbox as visible for image mode
        m_toolboxVisible = true;
        
        // Hide seekbar when loading image
        if (ui->seekSlider) {
            ui->seekSlider->setVisible(false);
            repositionButtons(); // Reposition UI elements
        }
        
        // Hide volume slider when loading image
        if (ui->volumeSlider) {
            ui->volumeSlider->setVisible(false);
        }
          // Stop seekbar timer
        if (m_seekbarTimer) {
            m_seekbarTimer->stop();
        }
        
        // Clear crop relative position when loading new content
        m_hasCropRelativePosition = false;
    }
    
    // Try to load HEIC files first
    if (HeicImageLoader::isHeicFile(filePath)) {
        m_currentImage = HeicImageLoader::loadHeicImage(filePath);
        if (!m_currentImage.isNull()) {
            qDebug() << "Successfully loaded HEIC file:" << filePath;
        } else {
            qDebug() << "Failed to load HEIC file:" << filePath;
            return;
        }
    } else {
        // Load regular image formats
        m_currentImage.load(filePath);
    }
    
    if (!m_currentImage.isNull()) {
        m_currentImagePath = filePath;
        
        // Store the true original image data immediately after loading, before any processing
        m_trueOriginalImage = m_currentImage;
        m_trueOriginalImageData = m_currentImage.toImage();
        qDebug() << "LOAD: Stored true original - size:" << m_trueOriginalImageData.size() << "format:" << m_trueOriginalImageData.format();
        
        // Reset zoom when loading new image
        resetZoom();
        
        // Mark texture for update
        m_textureNeedsUpdate = true;
          // Build list of images in the same directory
        buildImageList(filePath);
        
        // Note: Toolbox visibility is now handled by hover behavior
        
        // Don't resize window automatically - keep current size
        // User can manually resize if needed
        
        // Trigger a repaint to show the image
        update();
    }
}

void Zview::loadVideo(const QString &filePath)
{
    // Clear any loaded image
    m_currentImage = QPixmap();
    m_textureNeedsUpdate = true;
    update();
      // Clear A-B loop when loading new video
    clearABLoop();
    
    // Clear crop relative position when loading new content
    m_hasCropRelativePosition = false;
    
    // Remove old video widget approach - we now render directly in OpenGL
    if (m_videoWidget) {
        m_videoWidget->hide();
        m_videoWidget->deleteLater();
        m_videoWidget = nullptr;
    }
      // Create mpv player if not exists
    if (!m_mpvPlayer) {
        m_mpvPlayer = new MpvWrapper(this);
          // Connect frame ready signal to trigger repaints
        connect(m_mpvPlayer, &MpvWrapper::frameReady, this, [this]() {
            update();  // Trigger paintGL()
        });
          // Connect position and duration signals for seekbar
        connect(m_mpvPlayer, &MpvWrapper::positionChanged, this, [this](double position) {
            if (!m_seekbarDragging && !m_markerSeekInProgress && !m_isSeeking && ui->seekSlider) {
                ui->seekSlider->setValue((int)(position * 1000)); // Convert to ms
            }
            // Check A-B loop position
            checkABLoopPosition(position);
        });
        
               
        connect(m_mpvPlayer, &MpvWrapper::durationChanged, this, [this](double duration) {
            if (ui->seekSlider) {
                ui->seekSlider->setMaximum((int)(duration * 1000)); // Convert to ms
                ui->seekSlider->setVisible(false); // Start hidden, will show on hover
                repositionButtons(); // Reposition UI elements
            }
            if (ui->volumeSlider) {
                ui->volumeSlider->setVisible(false); // Start hidden, will show on hover
            }
        });
        
        connect(m_mpvPlayer, &MpvWrapper::seekCompleted, this, [this]() {
            qDebug() << "Seek completed, re-enabling UI updates and starting playback.";
            m_isSeeking = false;
            if (m_mpvPlayer) {
                // Always play after seeking (this is what the user wants)
                m_mpvPlayer->play();
            }
        });
    }
    
    // Initialize render context with our OpenGL context
    if (!m_mpvPlayer->isInitialized()) {
        makeCurrent();  // Ensure our OpenGL context is current
        if (!m_mpvPlayer->initializeRenderContext(context())) {
            qDebug() << "Failed to initialize MPV render context";
            return;
        }
        doneCurrent();
    }    if (m_mpvPlayer->isInitialized()) {
        m_mpvPlayer->loadFile(filePath);
        m_mpvPlayer->setLoop(m_isLoopEnabled); // Apply current loop setting
        // Don't auto-play - let user start playback to avoid navigation conflicts
        // m_mpvPlayer->play();
          m_isPlayingVideo = true;
        m_controlsVisible = false; // Start with controls hidden
        m_currentImagePath = filePath;
        m_currentVideoFile = filePath; // Store for smart caching
          // Hide toolboxes in video mode
        if (ui->modernToolbox) {
            ui->modernToolbox->setVisible(false);
        }
        if (ui->modernToolbox_2) {
            ui->modernToolbox_2->setVisible(false);
        }
        
        // Reset toolbox visibility state and force it to stay hidden in video mode
        m_toolboxVisible = false;
        m_ignoreToolboxHover = false;  // Reset any hover ignore state
        // Initialize smart caching for this video
        if (m_smartCache) {
            m_smartCache->onVideoLoaded(filePath);            m_smartCache->preloadVideo(filePath);
        }
        
        // Show seekbar for video playback (set a default duration for now)
        if (ui->seekSlider) {
            ui->seekSlider->setMaximum(300000); // Default max (300 seconds = 5 minutes worth of milliseconds)
            ui->seekSlider->setValue(0);            ui->seekSlider->setVisible(false); // Start hidden, will show on hover
            repositionButtons(); // Reposition UI elements
        }
          // Show volume slider for video playback
        if (ui->volumeSlider) {
            ui->volumeSlider->setVisible(false); // Start hidden, will show on hover
        }
        
        // Hide A and B buttons initially - will show on hover
        if (ui->buttonA) {
            ui->buttonA->setVisible(false);
        }
        if (ui->buttonB) {
            ui->buttonB->setVisible(false);
        }
        
        // Start seekbar timer
        if (m_seekbarTimer) {
            m_seekbarTimer->start();
        }
        
        // Build list of files (images and videos) in the same directory
        buildImageList(filePath);
        
        qDebug() << "Playing video with OpenGL rendering:" << filePath;
    } else {
        qDebug() << "Failed to initialize video player";
    }
}

bool Zview::isVideoFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    
    QStringList videoFormats = {"mp4", "avi", "mov", "mkv", "webm", "flv", 
                               "wmv", "mpg", "mpeg", "3gp", "ogv", "m4v"};
    
    return videoFormats.contains(extension);
}

bool Zview::isImageFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    
    QStringList imageFormats = {"jpg", "jpeg", "png", "bmp", "gif", "tiff", "tif", 
                               "webp", "heic", "heif", "ico", "svg"};
    
    return imageFormats.contains(extension);
}

void Zview::buildImageList(const QString &currentImagePath)
{
    QFileInfo fileInfo(currentImagePath);
    QDir directory = fileInfo.dir();
    
    // Get list of supported image formats
    QStringList supportedFormats;
    for (const QByteArray &format : QImageReader::supportedImageFormats()) {
        supportedFormats << "*." + QString::fromUtf8(format).toLower();
    }
    
    // Add HEIC/HEIF formats explicitly
    supportedFormats << "*.heic" << "*.heif";
    
    // Add video formats
    QStringList videoFormats = {"*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm", "*.flv", 
                               "*.wmv", "*.mpg", "*.mpeg", "*.3gp", "*.ogv", "*.m4v"};
    supportedFormats.append(videoFormats);
    
    // Get all image and video files in the directory
    directory.setNameFilters(supportedFormats);
    directory.setSorting(QDir::Name | QDir::IgnoreCase);
    QStringList files = directory.entryList(QDir::Files);
    
    // Build full paths and find current file index
    m_imageList.clear();
    m_currentImageIndex = -1;
    
    for (int i = 0; i < files.size(); ++i) {
        QString fullPath = directory.absoluteFilePath(files[i]);
        m_imageList.append(fullPath);
        
        if (fullPath == currentImagePath) {
            m_currentImageIndex = i;
        }
    }
}

void Zview::loadNextImage()
{
    if (m_imageList.isEmpty()) return;
    
    m_currentImageIndex = (m_currentImageIndex + 1) % m_imageList.size();
    loadImageAtIndex(m_currentImageIndex);
}

void Zview::loadPreviousImage()
{
    if (m_imageList.isEmpty()) return;
    
    m_currentImageIndex = (m_currentImageIndex - 1 + m_imageList.size()) % m_imageList.size();
    loadImageAtIndex(m_currentImageIndex);
}

void Zview::loadImageAtIndex(int index)
{
    if (index < 0 || index >= m_imageList.size()) return;
    
    QString filePath = m_imageList[index];
    m_currentImageIndex = index;
    
    // Check if it's a video file or image file
    if (isVideoFile(filePath)) {
        loadVideo(filePath);
    } else {
        // Try to load as image (including HEIC)
        if (HeicImageLoader::isHeicFile(filePath)) {
            m_currentImage = HeicImageLoader::loadHeicImage(filePath);
        } else {
            m_currentImage.load(filePath);
        }
          if (!m_currentImage.isNull()) {
            // Stop any video playback first
            if (m_isPlayingVideo) {
                if (m_mpvPlayer) {
                    m_mpvPlayer->stop();
                }
                if (m_videoWidget) {
                    m_videoWidget->hide();
                }
                m_isPlayingVideo = false;
                  // Hide seekbar when switching from video to image
                if (ui->seekSlider) {
                    ui->seekSlider->setVisible(false);
                    repositionButtons(); // Reposition UI elements
                }
                
                // Hide volume slider when switching from video to image
                if (ui->volumeSlider) {
                    ui->volumeSlider->setVisible(false);
                }
                
                // Stop seekbar timer
                if (m_seekbarTimer) {
                    m_seekbarTimer->stop();
                }
            }
            
            m_currentImagePath = filePath;
            
            // Reset zoom when switching images
            resetZoom();
            
            // Mark texture for update
            m_textureNeedsUpdate = true;
            
            // Trigger a repaint to show the new image
            update();
        }
    }
}

void Zview::resetZoom()
{
    m_zoomFactor = 1.0f;
    m_imageOffset = QPointF(0, 0);
    // Reset panning tracking variables as well
    m_panStartOffset = QPointF(0, 0);
    m_panDragStartPos = QPoint(0, 0);
    m_panImagePoint = QPointF(0, 0);
}

void Zview::setupShaders()
{
    m_shaderProgram = new QOpenGLShaderProgram;
    
    // Optimized vertex shader (fallback for compatibility)
    const char *vertexSource = R"(
        #version 330 core
        layout (location = 0) in vec3 position;
        layout (location = 1) in vec2 texCoord;
        
        out vec2 TexCoord;
        
        uniform mat4 projection;
        uniform mat4 view;
        uniform mat4 model;
        
        void main()
        {
            gl_Position = projection * view * model * vec4(position, 1.0);
            TexCoord = texCoord;
        }
    )";
    
    // Optimized fragment shader with better sampling
    const char *fragmentSource = R"(
        #version 330 core
        in vec2 TexCoord;
        out vec4 FragColor;
        
        uniform sampler2D ourTexture;
        uniform float gamma = 2.2;
        uniform float exposure = 1.0;
        uniform bool enableGammaCorrection = false;
        
        void main()
        {
            vec4 color = texture(ourTexture, TexCoord);
            
            // Optional gamma correction for better color accuracy
            if (enableGammaCorrection) {
                color.rgb = pow(color.rgb * exposure, vec3(1.0 / gamma));
            }
            
            FragColor = color;
        }
    )";
    
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexSource)) {
        qDebug() << "Vertex shader compilation failed:" << m_shaderProgram->log();
    }
    
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentSource)) {
        qDebug() << "Fragment shader compilation failed:" << m_shaderProgram->log();
    }
    
    if (!m_shaderProgram->link()) {
        qDebug() << "Shader program linking failed:" << m_shaderProgram->log();
    }
}

void Zview::setupVertexData()
{
    // Quad vertices with texture coordinates
    float vertices[] = {
        // positions        // texture coords
        -1.0f, -1.0f, 0.0f,  0.0f, 1.0f,
         1.0f, -1.0f, 0.0f,  1.0f, 1.0f,
         1.0f,  1.0f, 0.0f,  1.0f, 0.0f,
        -1.0f,  1.0f, 0.0f,  0.0f, 0.0f
    };
    
    unsigned int indices[] = {
        0, 1, 2,
        2, 3, 0
    };
    
    m_vao.create();
    m_vao.bind();
    
    m_vertexBuffer.create();
    m_vertexBuffer.bind();
    m_vertexBuffer.allocate(vertices, sizeof(vertices));
    
    // Position attribute
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    
    // Texture coordinate attribute
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
      // Create and bind element buffer
    m_elementBuffer.create();
    m_elementBuffer.bind();
    m_elementBuffer.allocate(indices, sizeof(indices));
    
    m_vao.release();
}

void Zview::loadImageToTexture()
{
    if (m_currentImage.isNull()) return;
    
    // Performance optimization: avoid unnecessary texture recreation
    QSize newSize = m_currentImage.size();
    if (m_texture && m_lastTextureSize == newSize && !m_textureNeedsUpdate) {
        return; // Texture is already up to date
    }
    
    // Clean up existing texture and mipmaps
    if (m_texture) {
        delete m_texture;
        m_texture = nullptr;
    }
    cleanupMipmaps();
    
    // Convert QPixmap to QImage with optimal format
    QImage image = m_currentImage.toImage();
    optimizeTextureFormat(image);
    
    // For large images, use mipmap levels for better performance
    if (image.width() > 2048 || image.height() > 2048) {
        generateMipmapLevels(image);
        selectOptimalMipLevel();
        m_lastTextureSize = newSize;
        m_textureNeedsUpdate = false;
        return;
    }
    
    // Create texture with optimal settings
    m_texture = new QOpenGLTexture(QOpenGLTexture::Target2D);
    m_texture->setFormat(QOpenGLTexture::RGBA8_UNorm);
    m_texture->setSize(image.width(), image.height());
    m_texture->allocateStorage();
    
    // Upload image data
    m_texture->setData(QOpenGLTexture::RGBA, QOpenGLTexture::UInt8, image.constBits());
      // Set texture parameters for optimal quality and performance
    m_texture->setMinificationFilter(QOpenGLTexture::LinearMipMapLinear);
    m_texture->setMagnificationFilter(QOpenGLTexture::Linear);
    m_texture->setWrapMode(QOpenGLTexture::ClampToEdge);
    
    // Generate mipmaps for better performance when zoomed out
    m_texture->generateMipMaps();
    
    // Enable anisotropic filtering if available (using direct OpenGL calls)
    if (context()->hasExtension("GL_EXT_texture_filter_anisotropic")) {
        GLfloat maxAnisotropy;
        glGetFloatv(GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT, &maxAnisotropy);
        m_texture->bind();
        glTexParameterf(GL_TEXTURE_2D, GL_TEXTURE_MAX_ANISOTROPY_EXT, qMin(maxAnisotropy, 16.0f));
        m_texture->release();
    }
    
    m_lastTextureSize = newSize;
    m_textureNeedsUpdate = false;
}

void Zview::repositionButtons()
{
    if (!ui->toolButton || !ui->toolButton_2) return;
    
    // Get current widget size
    int w = width();
    int h = height();    // Reposition modern toolbox to center horizontally at the top
    if (ui->modernToolbox) {
        const int toolboxWidth = 180;
        const int toolboxHeight = 60;
        int toolboxX = (w - toolboxWidth) / 2;  // Center horizontally
        int toolboxY = 0;  // Touch the top edge
        ui->modernToolbox->setGeometry(toolboxX, toolboxY, toolboxWidth, toolboxHeight);
    }    // Reposition crop toolbox (modernToolbox_2) to maintain original relative position
    if (ui->modernToolbox_2) {
        const int cropToolboxWidth = 101;
        const int cropToolboxHeight = 131;
        // Original position: x=1090, y=520 in 1200×800 window
        // Distance from right edge: 1200-1090 = 110px
        // Distance from bottom edge: 800-520 = 280px
        int cropToolboxX = w - 110;  // Maintain 110px from right edge
        int cropToolboxY = h - 280;  // Maintain 280px from bottom edge
        ui->modernToolbox_2->setGeometry(cropToolboxX, cropToolboxY, cropToolboxWidth, cropToolboxHeight);
    }
    
    // Button dimensions
    const int buttonSize = 41;
    const int margin = 10;
    const int spacing = 5;
    
    // Calculate positions for bottom right corner (vertical arrangement)
    int buttonX = w - margin - buttonSize;
    int button2Y = h - margin - buttonSize;  // Bottom button
    int button1Y = button2Y - buttonSize - spacing;  // Top button (above bottom button)
    
    // Set new positions (vertical stack)
    ui->toolButton->setGeometry(buttonX, button1Y, buttonSize, buttonSize);
    ui->toolButton_2->setGeometry(buttonX, button2Y, buttonSize, buttonSize);    // Position seekbar higher up when visible
    if (ui->seekSlider && ui->seekSlider->isVisible()) {
        const int seekbarHeight = 40;
        const int seekbarMargin = 20;
        int seekbarY = h - 85; // Position seekbar 85 pixels from bottom (moved down from 100)
        int seekbarWidth = w - (2 * seekbarMargin) - (buttonSize + margin + 10); // Leave space for buttons (slightly increased margin)
        ui->seekSlider->setGeometry(seekbarMargin, seekbarY, seekbarWidth, seekbarHeight);
    }
    
    // Position A and B buttons below seekbar when visible
    if (ui->buttonA && ui->buttonA->isVisible()) {
        const int buttonWidth = 30;
        const int buttonHeight = 25;
        const int buttonMargin = 20;
        int buttonY = h - 40; // Position A and B buttons 40 pixels from bottom
        ui->buttonA->setGeometry(buttonMargin, buttonY, buttonWidth, buttonHeight);
    }
    
    if (ui->buttonB && ui->buttonB->isVisible()) {
        const int buttonWidth = 30;
        const int buttonHeight = 25;
        const int buttonMargin = 20;
        const int buttonSpacing = 5;
        int buttonY = h - 40; // Position A and B buttons 40 pixels from bottom
        int buttonX = buttonMargin + buttonWidth + buttonSpacing; // Position B button next to A
        ui->buttonB->setGeometry(buttonX, buttonY, buttonWidth, buttonHeight);
    }    // Position volume slider below seekbar when visible
    if (ui->volumeSlider && ui->volumeSlider->isVisible()) {
        const int volumeHeight = 30;
        const int volumeWidth = 120;
        int volumeX = w - volumeWidth - (buttonSize + margin + 30); // Leave space for buttons
        int volumeY = h - 45; // Position volume slider 45 pixels from bottom (moved down from 60)
        ui->volumeSlider->setGeometry(volumeX, volumeY, volumeWidth, volumeHeight);
    }
    
    // Update A-B markers after repositioning
    updateSeekbarABMarkers();
}

// Window resize helper methods
int Zview::getResizeEdge(const QPoint &pos)
{
    int edge = 0;
    const int border = RESIZE_BORDER_WIDTH;
    
    if (pos.x() <= border) edge |= 1; // Left edge
    if (pos.x() >= width() - border) edge |= 2; // Right edge
    if (pos.y() <= border) edge |= 4; // Top edge
    if (pos.y() >= height() - border) edge |= 8; // Bottom edge
    
    return edge;
}

void Zview::updateCursorForResize(const QPoint &pos)
{
    // Don't change cursor if we're already dragging something
    if (m_isLeftDragging || m_isRightDragging || m_isResizing) {
        return;
    }
    
    int edge = getResizeEdge(pos);
    
    if (edge == 0) {
        setCursor(Qt::ArrowCursor);
    } else if (edge == 1 || edge == 2) { // Left or right
        setCursor(Qt::SizeHorCursor);
    } else if (edge == 4 || edge == 8) { // Top or bottom
        setCursor(Qt::SizeVerCursor);
    } else if (edge == 5 || edge == 10) { // Top-left or bottom-right
        setCursor(Qt::SizeFDiagCursor);
    } else if (edge == 6 || edge == 9) { // Top-right or bottom-left
        setCursor(Qt::SizeBDiagCursor);
    }
}

void Zview::resizeEvent(QResizeEvent *event)
{
    QOpenGLWidget::resizeEvent(event);
    
    // Reposition UI elements on resize
    repositionButtons();
    repositionCropRegion();
    repositionImageEditorToolbox();
    
    // Video rendering is handled automatically by OpenGL
    // No need for manual video widget resizing!
}

void Zview::showControls()
{
    if (m_isPlayingVideo && !m_controlsVisible) {
        m_controlsVisible = true;
        if (ui->seekSlider) {
            ui->seekSlider->setVisible(true);
        }
        if (ui->volumeSlider) {
            ui->volumeSlider->setVisible(true);
        }
        if (ui->buttonA) {
            ui->buttonA->setVisible(true);
        }        if (ui->buttonB) {
            ui->buttonB->setVisible(true);
        }
        // Note: modernToolbox visibility is now handled by toolbox hover logic
        repositionButtons(); // Ensure proper positioning
        updateSeekbarABMarkers(); // Update A-B markers visibility
    }
}

void Zview::hideControls()
{
    if (m_isPlayingVideo && m_controlsVisible && !m_seekbarDragging && !m_expandedSeekbarDragging) {
        m_controlsVisible = false;
        if (ui->seekSlider) {
            ui->seekSlider->setVisible(false);
        }
        if (ui->volumeSlider) {
            ui->volumeSlider->setVisible(false);
        }
        if (ui->buttonA) {
            ui->buttonA->setVisible(false);
        }        if (ui->buttonB) {
            ui->buttonB->setVisible(false);
        }
        // Note: modernToolbox visibility is now handled by toolbox hover logic
        updateSeekbarABMarkers(); // Update A-B markers visibility
    }
}

void Zview::ensureControlHideTimer()
{
    if (!m_controlHideTimer) {
        m_controlHideTimer = new QTimer(this);
        m_controlHideTimer->setSingleShot(true);
        m_controlHideTimer->setInterval(1500); // Hide after  1.5 seconds of no mouse movement
        connect(m_controlHideTimer, &QTimer::timeout, this, &Zview::hideControls);
    }
}

bool Zview::isMouseInControlArea(const QPoint &pos)
{
    if (!m_isPlayingVideo) return false;
    
    // Define control area as bottom 120 pixels of the window
    int controlAreaHeight = 120;
    int controlAreaY = height() - controlAreaHeight;
    
    return pos.y() >= controlAreaY;
}

void Zview::toggleVideoLoop()
{
    if (m_isPlayingVideo && m_mpvPlayer) {
        m_isLoopEnabled = !m_isLoopEnabled;
        m_mpvPlayer->setLoop(m_isLoopEnabled);
        qDebug() << "Video loop toggled:" << (m_isLoopEnabled ? "ON" : "OFF");
    }
}

// Toolbox hover functionality
void Zview::showToolbox()
{
    // Don't show toolbox in video mode
    if (m_isPlayingVideo) {
        return;
    }
    
    if (!m_toolboxVisible) {
        m_toolboxVisible = true;
        if (ui->modernToolbox) {
            ui->modernToolbox->setVisible(true);
        }
        qDebug() << "Toolbox shown";
    }
}

void Zview::hideToolbox(bool force)
{
    qDebug() << "hideToolbox called - m_toolboxVisible:" << m_toolboxVisible << "force:" << force << "m_cropModeActive:" << m_cropModeActive;
    if (m_toolboxVisible && (force || !m_cropModeActive)) {
        m_toolboxVisible = false;
        if (ui->modernToolbox) {
            ui->modernToolbox->setVisible(false);
        }
        qDebug() << "Toolbox hidden" << (force ? "(forced)" : "(normal)");
        
        // If this is a forced hide (from button click), ignore hover for a short time
        if (force) {
            m_ignoreToolboxHover = true;
            QTimer::singleShot(500, this, [this]() {
                m_ignoreToolboxHover = false;
                qDebug() << "Toolbox hover re-enabled";
            });
        }
    } else {
        qDebug() << "Toolbox hide blocked - conditions not met";
    }
}

bool Zview::isMouseInToolboxArea(const QPoint &pos)
{
    if (!ui->modernToolbox) return false;
    
    // Get the toolbox geometry
    QRect toolboxRect = ui->modernToolbox->geometry();
    
    // Expand the detection area slightly for better user experience
    int expandPixels = 20;
    toolboxRect.adjust(-expandPixels, -expandPixels, expandPixels, expandPixels);
    
    return toolboxRect.contains(pos);
}

void Zview::ensureToolboxHideTimer()
{
    if (!m_toolboxHideTimer) {        m_toolboxHideTimer = new QTimer(this);
        m_toolboxHideTimer->setSingleShot(true);
        m_toolboxHideTimer->setInterval(800); // Hide after 0.8 seconds of no mouse movement
        connect(m_toolboxHideTimer, &QTimer::timeout, this, [this]() {
            hideToolbox(); // Call without force parameter
        });
    }
}
    
// A-B loop functionality
void Zview::setABLoopPoint(bool isAPoint)
{
    if (!m_mpvPlayer || !m_isPlayingVideo) return;
    
    // Ensure audio is not muted when setting A-B points
    m_mpvPlayer->restoreAudio();
    
    // If already in A-B loop mode, clicking A or B will turn off the loop
    if (m_abLoopEnabled) {
        clearABLoop();
        return;
    }
    
    double currentTime = m_mpvPlayer->getPosition();
    
    if (isAPoint) {
        // Set A point
        m_abLoopStartTime = currentTime;
        m_hasAPoint = true;
        qDebug() << "A-B Loop: Set A point at" << currentTime << "seconds";
        
        // If B point was already set and A > B, clear B point
        if (m_hasBPoint && m_abLoopStartTime >= m_abLoopEndTime) {
            m_hasBPoint = false;
            m_abLoopEndTime = -1.0;
            qDebug() << "A-B Loop: Cleared B point (A >= B)";
        }
    } else {
        // Set B point
        m_abLoopEndTime = currentTime;
        m_hasBPoint = true;
        qDebug() << "A-B Loop: Set B point at" << currentTime << "seconds";
        
        // If A point was already set and B < A, clear A point
        if (m_hasAPoint && m_abLoopEndTime <= m_abLoopStartTime) {
            m_hasAPoint = false;
            m_abLoopStartTime = -1.0;
            qDebug() << "A-B Loop: Cleared A point (B <= A)";
        }
    }
      // Enable A-B loop if both points are set
    if (m_hasAPoint && m_hasBPoint) {
        m_abLoopEnabled = true;
        qDebug() << "A-B Loop: Enabled from" << m_abLoopStartTime << "to" << m_abLoopEndTime << "seconds";
    }
    
    updateABButtonStates();
    updateSeekbarABMarkers();
}

void Zview::clearABLoop()
{
    // Ensure audio is restored when clearing A-B loop
    if (m_mpvPlayer) {
        m_mpvPlayer->restoreAudio();
    }
    
    m_abLoopEnabled = false;
    m_hasAPoint = false;
    m_hasBPoint = false;
    m_abLoopStartTime = -1.0;
    m_abLoopEndTime = -1.0;
    updateABButtonStates();
    updateSeekbarABMarkers();
    qDebug() << "A-B Loop: Disabled with audio restored";
}

void Zview::checkABLoopPosition(double currentTime)
{
    // Don't perform loop check during marker dragging to avoid interference
    if (!m_abLoopEnabled || !m_mpvPlayer || m_draggingAMarker || m_draggingBMarker || m_markerSeekInProgress) return;
    
    // If current position exceeds B point, loop back to A point
    if (currentTime >= m_abLoopEndTime) {
        m_mpvPlayer->seek(m_abLoopStartTime);
        qDebug() << "A-B Loop: Looping back to A point at" << m_abLoopStartTime << "seconds";
    }
}

void Zview::updateABButtonStates()
{
    if (!ui->buttonA || !ui->buttonB) return;
    
    // Define styles
    QString normalStyle = R"(
        QPushButton {
            background-color: rgb(35, 35, 38);
            color: white;
            border: 1px solid #313334;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #395880;
        }
        QPushButton:pressed {
            background-color: #0078d4;
        }
    )";
    
    QString activeStyle = R"(
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: 1px solid #313334;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
        QPushButton:pressed {
            background-color: #0078d4;
        }
    )";
    
    // Apply styles based on state
    ui->buttonA->setStyleSheet(m_hasAPoint ? activeStyle : normalStyle);
    ui->buttonB->setStyleSheet(m_hasBPoint ? activeStyle : normalStyle);
}

void Zview::updateSeekbarABMarkers()
{
    if (!ui->seekSlider) return;    // Create marker widgets if they don't exist
    if (!m_aMarker) {
        m_aMarker = new QWidget(this);
        m_aMarker->setFixedSize(16, 24);  // Smaller size: 16px wide, 24px tall
        m_aMarker->setStyleSheet(R"(
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #4488ff, stop: 1 #2266cc);
                border: 2px solid #ffffff;
                border-radius: 6px;
                box-shadow: 0px 2px 6px rgba(68, 136, 255, 0.5);
            }
            QWidget:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #66aaff, stop: 1 #4488dd);
                border: 2px solid #ffff00;
                box-shadow: 0px 3px 8px rgba(68, 136, 255, 0.7);
            }
        )");
        m_aMarker->hide();
        m_aMarker->setCursor(Qt::PointingHandCursor);
        
        // Add "A" label to the marker
        QLabel *aLabel = new QLabel("A", m_aMarker);
        aLabel->setAlignment(Qt::AlignCenter);
        aLabel->setGeometry(0, 0, 16, 24);
        aLabel->setStyleSheet("background: transparent; color: white; font-weight: bold; font-size: 12px;");
        aLabel->setAttribute(Qt::WA_TransparentForMouseEvents); // Allow clicks to pass through
    }
    
    if (!m_bMarker) {
        m_bMarker = new QWidget(this);
        m_bMarker->setFixedSize(16, 24);  // Smaller size: 16px wide, 24px tall
        m_bMarker->setStyleSheet(R"(
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #4488ff, stop: 1 #2266cc);
                border: 2px solid #ffffff;
                border-radius: 6px;
                box-shadow: 0px 2px 6px rgba(68, 136, 255, 0.5);
            }
            QWidget:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #66aaff, stop: 1 #4488dd);
                border: 2px solid #ffff00;
                box-shadow: 0px 3px 8px rgba(68, 136, 255, 0.7);
            }
        )");
        m_bMarker->hide();
        m_bMarker->setCursor(Qt::PointingHandCursor);
        
        // Add "B" label to the marker
        QLabel *bLabel = new QLabel("B", m_bMarker);
        bLabel->setAlignment(Qt::AlignCenter);
        bLabel->setGeometry(0, 0, 16, 24);
        bLabel->setStyleSheet("background: transparent; color: white; font-weight: bold; font-size: 12px;");
        bLabel->setAttribute(Qt::WA_TransparentForMouseEvents); // Allow clicks to pass through
    }
      // Position and show/hide markers based on A-B points and control visibility
    if ((m_hasAPoint || m_hasBPoint) && m_controlsVisible) {
        int maxValue = ui->seekSlider->maximum();
        
        if (maxValue > 0 && ui->seekSlider->isVisible()) {
            QRect seekbarRect = ui->seekSlider->geometry();            // Show A marker if A point is set
            if (m_hasAPoint) {
                double aPercent = qBound(0.0, (m_abLoopStartTime * 1000.0) / maxValue, 1.0);
                
                // Calculate position more precisely to match Qt's slider handle positioning
                int sliderRange = seekbarRect.width();
                int handleWidth = 20; // Approximate handle width
                int usableWidth = sliderRange - handleWidth;
                int aX = seekbarRect.x() + (handleWidth / 2) + (int)(aPercent * usableWidth) - 8; // Center the 16px wide marker
                int aY = seekbarRect.y() + (seekbarRect.height() / 2) - 20; // Position above seekbar (24px tall marker)
                
                m_aMarker->move(aX, aY);
                m_aMarker->show();
                m_aMarker->raise(); // Bring to front
            } else {
                m_aMarker->hide();
            }
            
            // Show B marker if B point is set
            if (m_hasBPoint) {
                double bPercent = qBound(0.0, (m_abLoopEndTime * 1000.0) / maxValue, 1.0);
                
                // Calculate position more precisely to match Qt's slider handle positioning
                int sliderRange = seekbarRect.width();
                int handleWidth = 20; // Approximate handle width
                int usableWidth = sliderRange - handleWidth;
                int bX = seekbarRect.x() + (handleWidth / 2) + (int)(bPercent * usableWidth) - 8; // Center the 16px wide marker
                int bY = seekbarRect.y() + (seekbarRect.height() / 2) - 20; // Position above seekbar (24px tall marker)
                
                m_bMarker->move(bX, bY);
                m_bMarker->show();
                m_bMarker->raise(); // Bring to front
            } else {
                m_bMarker->hide();
            }
        } else {
            m_aMarker->hide();
            m_bMarker->hide();
        }
    } else {
        // Hide markers when no points are set or controls are hidden
        if (m_aMarker) m_aMarker->hide();
        if (m_bMarker) m_bMarker->hide();
    }
    
    // Update seekbar styling for loop region
    QString baseStyle = R"(
QSlider {
    background: transparent;
}

QSlider::groove:horizontal {
    background: rgba(255, 255, 255, 0.2);
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background: #0078d4;
    border: 2px solid #0078d4;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background: #106ebe;
    border: 2px solid #106ebe;
}

QSlider::sub-page:horizontal {
    background: #0078d4;
    border-radius: 2px;
})";

    if (m_hasAPoint && m_hasBPoint && m_abLoopEnabled) {
        // Add loop region highlighting
        int maxValue = ui->seekSlider->maximum();
        if (maxValue > 0) {
            double aPercent = qBound(0.0, (m_abLoopStartTime * 1000.0) / maxValue, 1.0);
            double bPercent = qBound(0.0, (m_abLoopEndTime * 1000.0) / maxValue, 1.0);
            
            QString loopStyle = QString(R"(
QSlider {
    background: transparent;
}

QSlider::groove:horizontal {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 rgba(255, 255, 255, 0.2),
        stop: %1 rgba(255, 255, 255, 0.2),
        stop: %1 rgba(0, 150, 255, 0.5),
        stop: %2 rgba(0, 150, 255, 0.5),
        stop: %2 rgba(255, 255, 255, 0.2),
        stop: 1 rgba(255, 255, 255, 0.2));
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background: #0078d4;
    border: 2px solid #0078d4;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background: #106ebe;
    border: 2px solid #106ebe;
}

QSlider::sub-page:horizontal {
    background: #0078d4;
    border-radius: 2px;
}
)").arg(aPercent, 0, 'f', 4).arg(bPercent, 0, 'f', 4);
            
            ui->seekSlider->setStyleSheet(loopStyle);
        }
    } else {
        ui->seekSlider->setStyleSheet(baseStyle);
    }
}

bool Zview::isPointOnMarker(const QPoint &pos, QWidget *marker)
{
    if (!marker || !marker->isVisible()) return false;
    
    // Create slightly larger hit area for easier clicking with bigger markers
    // Markers are now 20x30, so we add some padding for easy interaction
    QRect hitRect = marker->geometry().adjusted(-5, -5, 5, 5);
    return hitRect.contains(pos);
}

void Zview::updateMarkerTimeFromPosition(QWidget *marker, const QPoint &pos, bool isAMarker)
{
    if (!ui->seekSlider || !ui->seekSlider->isVisible()) return;
    
    // Set flag to prevent seekbar interference
    m_markerSeekInProgress = true;
    
    QRect seekbarRect = ui->seekSlider->geometry();
    int maxValue = ui->seekSlider->maximum();
    
    if (maxValue <= 0 || seekbarRect.width() <= 0) {
        m_markerSeekInProgress = false;
        return;
    }
    
    // Calculate relative position within seekbar
    int relativeX = pos.x() - seekbarRect.x();
    relativeX = qBound(0, relativeX, seekbarRect.width());      // Calculate time based on position
    double timePercent = (double)relativeX / (double)seekbarRect.width();
    double newTime = (timePercent * maxValue) / 1000.0; // Convert back from milliseconds
    
    // Update the appropriate loop point
    if (isAMarker) {
        m_abLoopStartTime = newTime;
        // Ensure A is before B
        if (m_hasBPoint && m_abLoopStartTime >= m_abLoopEndTime) {
            m_abLoopStartTime = m_abLoopEndTime - 0.1; // Keep A slightly before B
        }
    } else {
        m_abLoopEndTime = newTime;
        // Ensure B is after A
        if (m_hasAPoint && m_abLoopEndTime <= m_abLoopStartTime) {
            m_abLoopEndTime = m_abLoopStartTime + 0.1; // Keep B slightly after A
        }
    }      // Update markers visually
    updateSeekbarABMarkers();
    
    // Always perform seeking during marker drag for real-time feedback
    if (m_mpvPlayer && m_isPlayingVideo) {
        double seekTime = isAMarker ? m_abLoopStartTime : m_abLoopEndTime;
        
        qDebug() << "Seeking - isAMarker:" << isAMarker << "seekTime:" << seekTime 
                 << "A time:" << m_abLoopStartTime << "B time:" << m_abLoopEndTime;
        
        // Update seekbar position immediately to show correct position
        if (ui->seekSlider) {
            int seekbarValue = (int)(seekTime * 1000); // Convert to milliseconds
            ui->seekSlider->setValue(seekbarValue);
            qDebug() << "Set seekbar value to:" << seekbarValue;
        }
        
        // Use gentler seeking - only update position, don't force immediate frame update
        m_mpvPlayer->seek(seekTime);
        
        // Single update instead of multiple aggressive updates
        update();
    }
    
    // Clear the flag after a short delay
    QTimer::singleShot(10, this, [this]() {
        m_markerSeekInProgress = false;
    });
}

// Performance optimization methods
void Zview::detectOpenGLCapabilities()
{
    // Get maximum texture size supported by GPU
    glGetIntegerv(GL_MAX_TEXTURE_SIZE, &m_maxTextureSize);
    qDebug() << "Max texture size:" << m_maxTextureSize;
    
    // Check for non-power-of-two texture support
    m_supportsNPOT = context()->hasExtension("GL_ARB_texture_non_power_of_two") ||
                     context()->format().majorVersion() >= 3;
    
    // Check for texture compression support
    m_supportsCompression = context()->hasExtension("GL_ARB_texture_compression") ||
                           context()->hasExtension("GL_EXT_texture_compression_s3tc");
    
    // Determine optimal texture format based on GPU capabilities
    if (context()->hasExtension("GL_ARB_texture_float")) {
        m_optimalFormat = QImage::Format_RGBA8888;
    } else {
        m_optimalFormat = QImage::Format_RGB888;
    }
    
    qDebug() << "OpenGL capabilities - NPOT:" << m_supportsNPOT 
             << "Compression:" << m_supportsCompression;
}

void Zview::setupUniformBuffer()
{
    // Create simple buffer for future use (simplified for compatibility)
    glGenBuffers(1, &m_uniformBuffer);
    glBindBuffer(GL_ARRAY_BUFFER, m_uniformBuffer);
    glBufferData(GL_ARRAY_BUFFER, sizeof(GLfloat) * 16 * 3, nullptr, GL_DYNAMIC_DRAW);
    glBindBuffer(GL_ARRAY_BUFFER, 0);
}

void Zview::setupFramebuffer()
{
    // Create framebuffer for post-processing effects if needed
    glGenFramebuffers(1, &m_framebuffer);
    glGenTextures(1, &m_colorTexture);
    
    // We'll configure these when we know the actual size
}

void Zview::optimizeTextureFormat(QImage &image)
{
    // Skip format conversion if bypassing texture optimization (e.g., during reset)
    if (m_bypassTextureOptimization) {
        return;
    }
    
    // Convert to optimal format for GPU upload
    if (image.format() != m_optimalFormat) {
        image = image.convertToFormat(m_optimalFormat);
    }
    
    // Ensure power-of-two dimensions if not supported
    if (!m_supportsNPOT) {
        int width = image.width();
        int height = image.height();
        
        // Find next power of two
        int potWidth = 1;
        int potHeight = 1;
        while (potWidth < width) potWidth *= 2;
        while (potHeight < height) potHeight *= 2;
        
        if (potWidth != width || potHeight != height) {
            image = image.scaled(potWidth, potHeight, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
        }
    }
    
    // Clamp to maximum texture size
    if (image.width() > m_maxTextureSize || image.height() > m_maxTextureSize) {
        image = image.scaled(m_maxTextureSize, m_maxTextureSize, 
                            Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
}

void Zview::generateMipmapLevels(const QImage &image)
{
    // Clear existing mipmaps
    cleanupMipmaps();
    
    QImage currentImage = image;
    float scale = 1.0f;
    
    // Generate multiple resolution levels for performance
    while (currentImage.width() >= 64 && currentImage.height() >= 64) {
        TextureLevel level;
        level.size = currentImage.size();
        level.scale = scale;
        
        // Create texture for this level
        level.texture = new QOpenGLTexture(QOpenGLTexture::Target2D);
        level.texture->setFormat(QOpenGLTexture::RGBA8_UNorm);
        level.texture->setSize(currentImage.width(), currentImage.height());
        level.texture->allocateStorage();
        level.texture->setData(QOpenGLTexture::RGBA, QOpenGLTexture::UInt8, currentImage.constBits());
        
        // Set optimal texture parameters
        level.texture->setMinificationFilter(QOpenGLTexture::LinearMipMapLinear);
        level.texture->setMagnificationFilter(QOpenGLTexture::Linear);
        level.texture->setWrapMode(QOpenGLTexture::ClampToEdge);
        level.texture->generateMipMaps();
        
        m_mipmapLevels.append(level);
        
        // Generate next level (half resolution)
        currentImage = currentImage.scaled(currentImage.width() / 2, currentImage.height() / 2,
                                          Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
        scale *= 0.5f;
    }
    
    qDebug() << "Generated" << m_mipmapLevels.size() << "mipmap levels";
}

void Zview::selectOptimalMipLevel()
{
    if (m_mipmapLevels.isEmpty()) return;    
    // Select mipmap level based on current zoom
    float targetScale = m_zoomFactor;
    int bestLevel = 0;
    
    if (m_mipmapLevels.empty()) {
        return; // No mipmaps available
    }
    
    float bestDiff = qAbs(m_mipmapLevels[0].scale - targetScale);
    
    for (int i = 1; i < m_mipmapLevels.size(); ++i) {
        float diff = qAbs(m_mipmapLevels[i].scale - targetScale);
        if (diff < bestDiff) {
            bestDiff = diff;
            bestLevel = i;
        }
    }
    if (bestLevel != m_currentMipLevel) {
        m_currentMipLevel = bestLevel;
        // Note: Don't delete m_texture here or reassign it
        // The current texture will remain m_texture, and we'll use
        // the mipmap level texture only for rendering binding
    }
}

void Zview::updateMatrixUniforms(const QMatrix4x4 &model)
{
    // Use regular uniforms for better compatibility
    m_shaderProgram->setUniformValue("projection", m_projectionMatrix);
    m_shaderProgram->setUniformValue("view", m_viewMatrix);
    m_shaderProgram->setUniformValue("model", model);
    m_shaderProgram->setUniformValue("ourTexture", 0);
}

void Zview::preloadNextTexture()
{
    // Background loading of next/previous images for smoother navigation
    // This would be implemented with a worker thread for production use
    if (m_currentImageIndex >= 0 && m_currentImageIndex < m_imageList.size() - 1) {
        // Preload next image in background thread
        // Implementation would go here
    }
}

void Zview::cleanupMipmaps()
{
    for (auto &level : m_mipmapLevels) {
        if (level.texture) {
            delete level.texture;
            level.texture = nullptr;
        }
    }
    m_mipmapLevels.clear();
    m_currentMipLevel = 0;
    
    // Reset m_texture to nullptr since it may have been pointing to a mipmap level
    m_texture = nullptr;
}

bool Zview::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == ui->seekSlider) {
        if (event->type() == QEvent::MouseButtonPress) {
            // Capture the actual click position for accurate seeking
            QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
            
            if (m_mpvPlayer && m_isPlayingVideo && mouseEvent->button() == Qt::LeftButton) {
                QSlider *slider = ui->seekSlider;
                int sliderWidth = slider->width();
                int mouseX = mouseEvent->pos().x();
                
                double ratio = qBound(0.0, (double)mouseX / sliderWidth, 1.0);
                m_lastClickPosition = ratio * m_mpvPlayer->getDuration();
                
                qDebug() << "Captured click at position:" << m_lastClickPosition << "seconds (ratio:" << ratio << ")";
            }
        }
        else if (event->type() == QEvent::MouseMove && m_seekbarDragging) {
            // Ultra-fast mouse move handling for scrubbing
            QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
            
            if (m_mpvPlayer && m_isPlayingVideo) {
                // Calculate position from mouse coordinates for ultra-precise scrubbing
                QSlider *slider = ui->seekSlider;
                int sliderWidth = slider->width();
                int mouseX = mouseEvent->pos().x();
                
                double ratio = qBound(0.0, (double)mouseX / sliderWidth, 1.0);
                double position = ratio * m_mpvPlayer->getDuration();
                
                // Ultra-fast position update
                m_mpvPlayer->setScrubbingPosition(position);
                  // Update cache immediately
                if (m_smartCache) {
                    m_smartCache->setScrubPosition(position);
                }
            }
        }
    }
    return QOpenGLWidget::eventFilter(watched, event);
}

// Professional Free-Draw Crop Tool Implementation
void Zview::showCropTool()
{
    if (m_cropModeActive) return;    m_cropModeActive = true;
      // Show the crop toolbox with Apply, Undo, Exit buttons
    if (ui->modernToolbox_2) {
        ui->modernToolbox_2->setVisible(true);
        // Make individual crop buttons visible
        if (ui->toolButton_5) ui->toolButton_5->setVisible(true);  // Apply Crop
        if (ui->toolButton_4) ui->toolButton_4->setVisible(true);  // Undo
        if (ui->toolButton_3) ui->toolButton_3->setVisible(true);  // Exit Crop
    }
    
    // Enable forced crop cursor mode to override all other cursor logic
    m_forceCropCursor = true;
    
    // Start aggressive cursor monitoring for crop mode
    if (m_cursorUpdateTimer) {
        m_cursorUpdateTimer->start();
    }
      ui->cropTool->setText("Crop");ui->cropTool->setStyleSheet(
        "QToolButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 120, 215, 0.9),"
        "                               stop:1 rgba(0, 100, 180, 0.9));"
        "    color: white;"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "    border-radius: 8px;"
        "    padding: 4px;"
        "    font-size: 10px;"
        "    font-weight: 600;"
        "    box-shadow: 0 4px 16px rgba(0, 120, 215, 0.3);"
        "}"
        "QToolButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 140, 235, 1.0),"
        "                               stop:1 rgba(0, 120, 200, 1.0));"
        "    box-shadow: 0 6px 20px rgba(0, 120, 215, 0.4);"
        "}");
      // Don't set a fixed cursor here - let the mouseMoveEvent handle dynamic cursor changes
    
    // Clean up any existing overlay/handles
    if (m_cropOverlay) {
        delete m_cropOverlay;
        m_cropOverlay = nullptr;
    }
    
    for (QWidget* handle : m_cropHandles) {
        delete handle;
    }
    m_cropHandles.clear();
    
    // Trigger repaint to remove any existing overlay
    update();
    
    qDebug() << "Free-draw crop mode activated - click and drag to select area";
}

void Zview::hideCropTool()
{
    if (!m_cropModeActive) return;
      m_cropModeActive = false;
      // Hide the crop toolbox with Apply, Undo, Exit buttons
    if (ui->modernToolbox_2) {
        ui->modernToolbox_2->setVisible(false);
        // Hide individual crop buttons
        if (ui->toolButton_5) ui->toolButton_5->setVisible(false);  // Apply Crop
        if (ui->toolButton_4) ui->toolButton_4->setVisible(false);  // Undo
        if (ui->toolButton_3) ui->toolButton_3->setVisible(false);  // Exit Crop
    }
    
    // Disable forced crop cursor mode
    m_forceCropCursor = false;
      // Stop aggressive cursor monitoring when leaving crop mode
    if (m_cursorUpdateTimer) {
        m_cursorUpdateTimer->stop();
    }
    
    // Reset cursor to default when leaving crop mode
    setCursor(Qt::ArrowCursor);
    m_currentCursorShape = Qt::ArrowCursor;
    
    ui->cropTool->setText("Crop");
    ui->cropTool->setStyleSheet(
        "QToolButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(70, 70, 75, 0.8),"
        "                               stop:1 rgba(50, 50, 55, 0.8));"
        "    color: #E8E8E8;"
        "    border: 1px solid rgba(255, 255, 255, 0.1);"
        "    border-radius: 8px;"
        "    padding: 4px;"
        "    font-size: 10px;"
        "    font-weight: 500;"
        "}"
        "QToolButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 120, 215, 0.9),"
        "                               stop:1 rgba(0, 100, 180, 0.9));"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "    color: white;"
        "    box-shadow: 0 4px 16px rgba(0, 120, 215, 0.3);"
        "}"
    );
      // Hide crop overlay and handles
    if (m_cropOverlay) {
        m_cropOverlay->hide();
    }
    
    for (QWidget* handle : m_cropHandles) {
        handle->hide();
    }
    
    // Hide the toolbox when crop mode ends
    hideToolbox();
    
    // Trigger repaint to clear any existing overlay
    update();
    
    qDebug() << "Professional crop mode deactivated";
}

void Zview::createCropHandles()
{
    // Clean up existing handles
    for (QWidget* handle : m_cropHandles) {
        delete handle;
    }
    m_cropHandles.clear();
    
    // Create 8 resize handles (corners + edges)
    for (int i = 0; i < 8; i++) {
        QWidget* handle = new QWidget(this);
        handle->setStyleSheet(
            "background: rgba(0, 120, 215, 0.9);"
            "border: 2px solid white;"
            "border-radius: 6px;"
        );
        handle->setFixedSize(12, 12);
        handle->setAttribute(Qt::WA_TransparentForMouseEvents, false); // Ensure handles can receive mouse events
        handle->hide(); // Always start hidden
        m_cropHandles.append(handle);
    }
}

void Zview::updateCropOverlay()
{
    if (!m_cropOverlay || m_cropHandles.size() != 8) return;
    
    QRect rect = m_cropOverlay->geometry();
    
    // Don't show or position handles during drawing process
    if (m_drawingCrop) {
        for (QWidget* handle : m_cropHandles) {
            handle->hide();
        }
        return;
    }
    
    // Only position handles if the crop area is large enough
    if (rect.width() < 10 || rect.height() < 10) {
        // Hide handles for small crop areas
        for (QWidget* handle : m_cropHandles) {
            handle->hide();
        }
        return;
    }    // Position handles around the crop area
    // Corners - using explicit width/height calculations
    m_cropHandles[0]->move(rect.left() - 6, rect.top() - 6);                                    // Top-left
    m_cropHandles[1]->move(rect.left() + rect.width() - 6, rect.top() - 6);                     // Top-right
    m_cropHandles[2]->move(rect.left() - 6, rect.top() + rect.height() - 6);                    // Bottom-left
    m_cropHandles[3]->move(rect.left() + rect.width() - 6, rect.top() + rect.height() - 6);     // Bottom-right
    
    // Edges
    m_cropHandles[4]->move(rect.left() + rect.width()/2 - 6, rect.top() - 6);                    // Top
    m_cropHandles[5]->move(rect.left() + rect.width()/2 - 6, rect.top() + rect.height() - 6);   // Bottom
    m_cropHandles[6]->move(rect.left() - 6, rect.top() + rect.height()/2 - 6);                  // Left
    m_cropHandles[7]->move(rect.left() + rect.width() - 6, rect.top() + rect.height()/2 - 6);   // Right
      // Make sure handles are visible and on top
    for (QWidget* handle : m_cropHandles) {
        handle->show();
        handle->raise(); // Bring to front
    }
      // Debug output to verify positioning
    // qDebug() << "Crop rect:" << rect << "Handle 3 (bottom-right) at:" << (rect.left() + rect.width() - 6) << "," << (rect.top() + rect.height() - 6);
}

void Zview::applyCrop()
{
    if (!m_cropModeActive || !m_cropOverlay || m_currentImage.isNull()) {
        qDebug() << "Cannot apply crop - missing prerequisites";
        return;
    }
    
    QRect cropRect = m_cropOverlay->geometry();
    qDebug() << "Applying crop with rectangle:" << cropRect;
    
    // Calculate crop rectangle relative to the actual image
    QRect imageBounds = getImageBounds();
    if (!imageBounds.isValid()) {
        qDebug() << "Cannot apply crop - invalid image bounds";
        return;
    }
    
    // Convert crop rectangle from widget coordinates to image coordinates
    QRect relativeCropRect = cropRect.translated(-imageBounds.topLeft());
    
    // Scale the crop rectangle to match the original image size
    float scaleX = (float)m_currentImage.width() / imageBounds.width();
    float scaleY = (float)m_currentImage.height() / imageBounds.height();
    
    QRect imageCropRect(
        (int)(relativeCropRect.x() * scaleX),
        (int)(relativeCropRect.y() * scaleY),
        (int)(relativeCropRect.width() * scaleX),
        (int)(relativeCropRect.height() * scaleY)
    );
    
    // Ensure minimum size
    imageCropRect = imageCropRect.adjusted(0, 0, 1, 1); // Ensure at least 1x1 pixel
    
    // Ensure crop rectangle is within image bounds
    imageCropRect = imageCropRect.intersected(QRect(0, 0, m_currentImage.width(), m_currentImage.height()));
    
    if (imageCropRect.width() < 1 || imageCropRect.height() < 1) {
        qDebug() << "Cannot apply crop - resulting crop area too small";
        return;
    }
    
    qDebug() << "Original image size:" << m_currentImage.size() 
             << "Widget crop rect:" << cropRect 
             << "Image crop rect:" << imageCropRect;
      // Create the cropped image
    QImage currentImageAsImage = m_currentImage.toImage();
    QImage croppedImage = currentImageAsImage.copy(imageCropRect);
    
    if (croppedImage.isNull()) {
        qDebug() << "Failed to create cropped image";
        return;
    }
    
    // Replace the current image with the cropped version
    m_currentImage = QPixmap::fromImage(croppedImage);
    
    // Reset zoom and offset since we have a new image
    m_zoomFactor = 1.0f;
    m_imageOffset = QPointF(0, 0);
    
    // Update OpenGL texture with the new image
    loadImageToTexture();
    
    // Hide crop tool and update display
    hideCropTool();
    
    // Trigger repaint to show the new cropped image
    update();
    
    qDebug() << "Crop applied successfully. New image size:" << m_currentImage.size();
}

void Zview::showCropPreview()
{
    // For now, just log that preview would be shown
    // In a full implementation, this could show a preview window
    qDebug() << "Crop preview functionality - showing preview of cropped result";
}

void Zview::cancelCrop()
{
    hideCropTool();
}

// Crop tool helper functions
int Zview::getCropHandleAtPosition(const QPoint &pos)
{
    if (!m_cropOverlay || !m_cropOverlay->isVisible()) return -1;
    
    QRect cropRect = m_cropOverlay->geometry();
    
    for (int i = 0; i < m_cropHandles.size(); i++) {
        QWidget* handle = m_cropHandles[i];
        if (!handle->isVisible()) continue;
        
        QRect handleGeom = handle->geometry();
        
        // Smart expansion: expand outward from crop area, but not inward
        QRect expandedGeom;
        
        // Corner handles (0=TL, 1=TR, 2=BL, 3=BR)
        if (i == 0) { // Top-left corner
            expandedGeom = handleGeom.adjusted(-4, -4, 1, 1);
        } else if (i == 1) { // Top-right corner
            expandedGeom = handleGeom.adjusted(-1, -4, 4, 1);
        } else if (i == 2) { // Bottom-left corner
            expandedGeom = handleGeom.adjusted(-4, -1, 1, 4);
        } else if (i == 3) { // Bottom-right corner
            expandedGeom = handleGeom.adjusted(-1, -1, 4, 4);
        }
        // Edge handles (4=Top, 5=Bottom, 6=Left, 7=Right)
        else if (i == 4) { // Top edge
            expandedGeom = handleGeom.adjusted(-2, -4, 2, 1);
        } else if (i == 5) { // Bottom edge
            expandedGeom = handleGeom.adjusted(-2, -1, 2, 4);
        } else if (i == 6) { // Left edge
            expandedGeom = handleGeom.adjusted(-4, -2, 1, 2);
        } else if (i == 7) { // Right edge
            expandedGeom = handleGeom.adjusted(-1, -2, 4, 2);
        }
        
        if (expandedGeom.contains(pos)) {
            return i;
        }
    }
    
    return -1; // No handle at position
}

void Zview::updateCropRectFromHandle(int handleIndex, const QPoint &newPos)
{
    if (!m_cropOverlay || handleIndex < 0 || handleIndex >= 8) return;
    
    QRect rect = m_cropOverlay->geometry();
    
    // Constrain new position to image bounds
    QRect imageBounds = getImageBounds();
    QPoint constrainedPos = newPos;
    if (imageBounds.isValid()) {
        constrainedPos.setX(qMax(imageBounds.left(), qMin(newPos.x(), imageBounds.right())));
        constrainedPos.setY(qMax(imageBounds.top(), qMin(newPos.y(), imageBounds.bottom())));
    }
    
    // Update rectangle based on which handle is being dragged
    switch (handleIndex) {
        case 0: // Top-left
            rect.setTopLeft(constrainedPos);
            break;
        case 1: // Top-right
            rect.setTopRight(constrainedPos);
            break;
        case 2: // Bottom-left
            rect.setBottomLeft(constrainedPos);
            break;
        case 3: // Bottom-right
            rect.setBottomRight(constrainedPos);
            break;
        case 4: // Top edge
            rect.setTop(constrainedPos.y());
            break;
        case 5: // Bottom edge
            rect.setBottom(constrainedPos.y());
            break;
        case 6: // Left edge
            rect.setLeft(constrainedPos.x());
            break;
        case 7: // Right edge
            rect.setRight(constrainedPos.x());
            break;    }

    // Ensure minimum size (only during handle dragging, not initial drawing)
    const int minSize = 20;
    if (!m_drawingCrop) {
        // Ensure minimum size while preserving the direction of the drag
        if (rect.width() < minSize) {
            if (handleIndex == 0 || handleIndex == 2 || handleIndex == 6) {
                // Dragging left edge or left side - adjust left
                rect.setLeft(rect.right() - minSize);
            } else {
                // Dragging right edge or right side - adjust right
                rect.setRight(rect.left() + minSize);
            }
        }
        if (rect.height() < minSize) {
            if (handleIndex == 0 || handleIndex == 1 || handleIndex == 4) {
                // Dragging top edge or top side - adjust top
                rect.setTop(rect.bottom() - minSize);
            } else {
                // Dragging bottom edge or bottom side - adjust bottom
                rect.setBottom(rect.top() + minSize);
            }
        }
    }
    
    // Ensure crop stays within image bounds (not window bounds)
    if (imageBounds.isValid()) {
        rect = rect.intersected(imageBounds);
        qDebug() << "Crop handle dragging constrained to image bounds:" << rect << "Image bounds:" << imageBounds;
    }

    // Update overlay and handles
    m_cropOverlay->setGeometry(rect);
    m_cropRect = rect;
    updateCropOverlay();
    storeCropRelativePosition(); // Store updated position
    
    // Trigger repaint to show the updated inverted overlay
    update();
}

void Zview::repositionCropRegion()
{
    // Only reposition if we have an active crop mode AND a visible crop overlay AND stored relative position
    if (!m_cropModeActive || !m_cropOverlay || !m_hasCropRelativePosition) {
        return;
    }
    
    // Also check if the crop overlay is currently visible - if it was hidden (e.g., by clicking outside),
    // don't bring it back during fullscreen transitions
    if (!m_cropOverlay->isVisible()) {
        return;
    }
    
    // Only apply relative positioning if we already have stored relative coordinates
    if (m_hasCropRelativePosition) {
        QRect imageBounds = getImageBounds();
        if (!imageBounds.isValid() || imageBounds.width() <= 0 || imageBounds.height() <= 0) {
            return;
        }
        
        // Calculate new position relative to the current image bounds
        int newX = imageBounds.x() + (int)(m_cropRelativeX * imageBounds.width());
        int newY = imageBounds.y() + (int)(m_cropRelativeY * imageBounds.height());
        int newWidth = (int)(m_cropRelativeWidth * imageBounds.width());
        int newHeight = (int)(m_cropRelativeHeight * imageBounds.height());
        
        // Ensure minimum size
        newWidth = qMax(newWidth, 20);
        newHeight = qMax(newHeight, 20);
        
        // Ensure crop rectangle is within image bounds
        newX = qMax(imageBounds.x(), qMin(newX, imageBounds.right() - newWidth));
        newY = qMax(imageBounds.y(), qMin(newY, imageBounds.bottom() - newHeight));
        newWidth = qMin(newWidth, imageBounds.right() - newX);
        newHeight = qMin(newHeight, imageBounds.bottom() - newY);
        
        QRect newGeometry(newX, newY, newWidth, newHeight);
        
        m_cropOverlay->setGeometry(newGeometry);
        m_cropRect = newGeometry;
          // Update crop handles
        updateCropOverlay();
        // Note: Don't call storeCropRelativePosition() here as we're restoring from relative position
    }
}

// Store current crop position as relative coordinates to the image bounds
void Zview::storeCropRelativePosition()
{
    if (!m_cropModeActive || m_cropRect.width() <= 0 || m_cropRect.height() <= 0) {
        return;
    }
    
    QRect imageBounds = getImageBounds();
    if (!imageBounds.isValid() || imageBounds.width() <= 0 || imageBounds.height() <= 0) {
        return;
    }
    
    // Store crop position relative to the image bounds, not window bounds
    m_cropRelativeX = (float)(m_cropRect.x() - imageBounds.x()) / imageBounds.width();
    m_cropRelativeY = (float)(m_cropRect.y() - imageBounds.y()) / imageBounds.height();
    m_cropRelativeWidth = (float)m_cropRect.width() / imageBounds.width();
    m_cropRelativeHeight = (float)m_cropRect.height() / imageBounds.height();    m_hasCropRelativePosition = true;
}

// Get the actual image area bounds in window coordinates
QRect Zview::getImageBounds()
{
    if (m_currentImage.isNull()) {
        return QRect(); // Return empty rect if no image
    }
    
    int windowWidth = width();
    int windowHeight = height();
    
    if (windowWidth <= 0 || windowHeight <= 0) {
        return QRect();
    }
    
    // Calculate the same scaling as used in paintGL
    float imageAspect = (float)m_currentImage.width() / m_currentImage.height();
    float widgetAspect = (float)width() / height();
    
    // Calculate base scale (same logic as in paintGL)
    float baseScaleX, baseScaleY;
    if (imageAspect > widgetAspect) {
        baseScaleX = 1.0f;
        baseScaleY = widgetAspect / imageAspect;
    } else {
        baseScaleX = imageAspect / widgetAspect;
        baseScaleY = 1.0f;
    }
    
    // Apply zoom
    baseScaleX *= m_zoomFactor;
    baseScaleY *= m_zoomFactor;
    
    // Calculate the image size in window coordinates
    int imageWidthInWindow = (int)(width() * baseScaleX);
    int imageHeightInWindow = (int)(height() * baseScaleY);
    
    // Calculate the image position (center + offset)
    int imageCenterX = width() / 2 + (int)m_imageOffset.x();
    int imageCenterY = height() / 2 + (int)m_imageOffset.y();
    
    int imageLeft = imageCenterX - imageWidthInWindow / 2;
    int imageTop = imageCenterY - imageHeightInWindow / 2;
    
    QRect imageBounds(imageLeft, imageTop, imageWidthInWindow, imageHeightInWindow);
    
    qDebug() << "Image bounds calculated:" << imageBounds << "Window size:" << windowWidth << "x" << windowHeight << "Zoom:" << m_zoomFactor;
    
    return imageBounds;
}

// ===============================================
// PROFESSIONAL NOISE REDUCTION & ARTIFACT REMOVAL
// ===============================================

// Advanced Median Filter for removing salt-and-pepper noise and color artifacts
QImage Zview::applyMedianFilter(const QImage &image, int kernelSize) {
    if (kernelSize % 2 == 0) kernelSize++; // Ensure odd kernel size
    if (kernelSize < 3) kernelSize = 3;
    if (kernelSize > 15) kernelSize = 15; // Performance limit
    
    QImage result(image.size(), image.format());
    int radius = kernelSize / 2;
    
    // Process image in parallel for better performance
    #pragma omp parallel for
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QVector<int> rValues, gValues, bValues;
            
            // Collect pixel values in the kernel
            for (int ky = -radius; ky <= radius; ++ky) {
                for (int kx = -radius; kx <= radius; ++kx) {
                    int px = qBound(0, x + kx, image.width() - 1);
                    int py = qBound(0, y + ky, image.height() - 1);
                    
                    QRgb pixel = image.pixel(px, py);
                    rValues.append(qRed(pixel));
                    gValues.append(qGreen(pixel));
                    bValues.append(qBlue(pixel));
                }
            }
            
            // Sort and find median
            std::sort(rValues.begin(), rValues.end());
            std::sort(gValues.begin(), gValues.end());
            std::sort(bValues.begin(), bValues.end());
            
            int medianIndex = rValues.size() / 2;
            int r = rValues[medianIndex];
            int g = gValues[medianIndex];
            int b = bValues[medianIndex];
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

// Gaussian-based noise reduction with edge preservation
QImage Zview::applyGaussianNoiseReduction(const QImage &image, double sigma, int kernelSize) {
    if (kernelSize % 2 == 0) kernelSize++;
    if (kernelSize < 3) kernelSize = 3;
    if (kernelSize > 25) kernelSize = 25;
    
    // Generate Gaussian kernel
    QVector<QVector<double>> kernel(kernelSize, QVector<double>(kernelSize));
    double sum = 0.0;
    int radius = kernelSize / 2;
    
    for (int y = -radius; y <= radius; ++y) {
        for (int x = -radius; x <= radius; ++x) {
            double value = exp(-(x*x + y*y) / (2.0 * sigma * sigma));
            kernel[y + radius][x + radius] = value;
            sum += value;
        }
    }
    
    // Normalize kernel
    for (int y = 0; y < kernelSize; ++y) {
        for (int x = 0; x < kernelSize; ++x) {
            kernel[y][x] /= sum;
        }
    }
    
    QImage result(image.size(), image.format());
    
    #pragma omp parallel for
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            double r = 0, g = 0, b = 0;
            
            for (int ky = -radius; ky <= radius; ++ky) {
                for (int kx = -radius; kx <= radius; ++kx) {
                    int px = qBound(0, x + kx, image.width() - 1);
                    int py = qBound(0, y + ky, image.height() - 1);
                    
                    QRgb pixel = image.pixel(px, py);
                    double weight = kernel[ky + radius][kx + radius];
                    
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                }
            }
            
            result.setPixel(x, y, qRgb(
                qBound(0, (int)round(r), 255),
                qBound(0, (int)round(g), 255),
                qBound(0, (int)round(b), 255)
            ));
        }
    }
    
    return result;
}

// Remove color artifacts and digital noise
QImage Zview::removeColorArtifacts(const QImage &image, int threshold) {
    QImage result = image.copy();
    
    #pragma omp parallel for
    for (int y = 1; y < image.height() - 1; ++y) {
        for (int x = 1; x < image.width() - 1; ++x) {
            QRgb center = image.pixel(x, y);
            
            // Analyze surrounding pixels
            QVector<QRgb> neighbors;
            for (int dy = -1; dy <= 1; ++dy) {
                for (int dx = -1; dx <= 1; ++dx) {
                    if (dx != 0 || dy != 0) {
                        neighbors.append(image.pixel(x + dx, y + dy));
                    }
                }
            }
            
            // Calculate average of neighbors
            double avgR = 0, avgG = 0, avgB = 0;
            for (const QRgb &neighbor : neighbors) {
                avgR += qRed(neighbor);
                avgG += qGreen(neighbor);
                avgB += qBlue(neighbor);
            }
            avgR /= neighbors.size();
            avgG /= neighbors.size();
            avgB /= neighbors.size();
            
            // Check if current pixel is an outlier
            double diffR = abs(qRed(center) - avgR);
            double diffG = abs(qGreen(center) - avgG);
            double diffB = abs(qBlue(center) - avgB);
            
            if (diffR > threshold || diffG > threshold || diffB > threshold) {
                // Replace with median of neighbors for better preservation
                QVector<int> rVals, gVals, bVals;
                for (const QRgb &neighbor : neighbors) {
                    rVals.append(qRed(neighbor));
                    gVals.append(qGreen(neighbor));
                    bVals.append(qBlue(neighbor));
                }
                
                std::sort(rVals.begin(), rVals.end());
                std::sort(gVals.begin(), gVals.end());
                std::sort(bVals.begin(), bVals.end());
                
                int medianIdx = rVals.size() / 2;
                result.setPixel(x, y, qRgb(rVals[medianIdx], gVals[medianIdx], bVals[medianIdx]));
            }
        }
    }
    
    return result;
}

// Advanced bilateral filter for edge-preserving smoothing
QImage Zview::applyBilateralFilter(const QImage &image, int d, double sigmaColor, double sigmaSpace) {
    QImage result(image.size(), image.format());
    int radius = d / 2;
    
    #pragma omp parallel for
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            double totalWeight = 0;
            double newR = 0, newG = 0, newB = 0;
            QRgb centerPixel = image.pixel(x, y);
            
            for (int ky = -radius; ky <= radius; ++ky) {
                for (int kx = -radius; kx <= radius; ++kx) {
                    int px = qBound(0, x + kx, image.width() - 1);
                    int py = qBound(0, y + ky, image.height() - 1);
                    QRgb neighborPixel = image.pixel(px, py);
                    
                    // Spatial weight
                    double spatialDist = sqrt(kx*kx + ky*ky);
                    double spatialWeight = exp(-(spatialDist * spatialDist) / (2.0 * sigmaSpace * sigmaSpace));
                    
                    // Color weight
                    double colorDist = sqrt(
                        pow(qRed(centerPixel) - qRed(neighborPixel), 2) +
                        pow(qGreen(centerPixel) - qGreen(neighborPixel), 2) +
                        pow(qBlue(centerPixel) - qBlue(neighborPixel), 2)
                    );
                    double colorWeight = exp(-(colorDist * colorDist) / (2.0 * sigmaColor * sigmaColor));
                    
                    double weight = spatialWeight * colorWeight;
                    totalWeight += weight;
                    
                    newR += qRed(neighborPixel) * weight;
                    newG += qGreen(neighborPixel) * weight;
                    newB += qBlue(neighborPixel) * weight;
                }
            }
            
            if (totalWeight > 0) {
                newR /= totalWeight;
                newG /= totalWeight;
                newB /= totalWeight;
            }
            
            result.setPixel(x, y, qRgb(
                qBound(0, (int)round(newR), 255),
                qBound(0, (int)round(newG), 255),
                qBound(0, (int)round(newB), 255)
            ));
        }
    }
    
    return result;
}

// Comprehensive advanced noise reduction
QImage Zview::applyAdvancedNoiseReduction(const QImage &image, int strength) {
    if (strength <= 0) return image;
    
    QImage result = image;
    
    // Stage 1: Remove obvious color artifacts
    if (strength >= 25) {
        result = removeColorArtifacts(result, 30 + (strength - 25));
    }
    
    // Stage 2: Apply median filter for impulse noise
    if (strength >= 40) {
        int kernelSize = 3 + (strength - 40) / 20;
        result = applyMedianFilter(result, qMin(kernelSize, 7));
    }
    
    // Stage 3: Bilateral filtering for edge-preserving smoothing
    if (strength >= 60) {
        double sigmaColor = 50 + (strength - 60) * 2;
        double sigmaSpace = 50 + (strength - 60) * 1.5;
        result = applyBilateralFilter(result, 9, sigmaColor, sigmaSpace);
    }
    
    // Stage 4: Final Gaussian smoothing for remaining noise
    if (strength >= 80) {
        double sigma = 0.5 + (strength - 80) * 0.05;
        result = applyGaussianNoiseReduction(result, sigma, 5);
    }
    
    return result;
}

// Remove JPEG compression artifacts
QImage Zview::removeCompressionArtifacts(const QImage &image) {
    // Use a combination of bilateral filtering and median filtering
    QImage result = applyBilateralFilter(image, 5, 30, 30);
    
    // Apply gentle median filter to remove block artifacts
    result = applyMedianFilter(result, 3);
    
    // Final smoothing
    result = applyGaussianNoiseReduction(result, 0.8, 3);
    
    return result;
}

// Enhance overall image quality
QImage Zview::enhanceImageQuality(const QImage &image) {
    QImage result = image;
    
    // Step 1: Remove artifacts and noise
    result = applyAdvancedNoiseReduction(result, 40);
    
    // Step 2: Correct color casting
    result = correctColorCasting(result);
    
    // Step 3: Apply adaptive sharpening
    result = applyAdaptiveSharpening(result, 0.3);
    
    return result;
}

// Correct color casting issues
QImage Zview::correctColorCasting(const QImage &image) {
    QImage result(image.size(), image.format());
    
    // Calculate average color values
    long long totalR = 0, totalG = 0, totalB = 0;
    int pixelCount = image.width() * image.height();
    
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);
            totalR += qRed(pixel);
            totalG += qGreen(pixel);
            totalB += qBlue(pixel);
        }
    }
    
    double avgR = (double)totalR / pixelCount;
    double avgG = (double)totalG / pixelCount;
    double avgB = (double)totalB / pixelCount;
    
    // Calculate gray point (should be around 128 for balanced image)
    double grayPoint = (avgR + avgG + avgB) / 3.0;
    
    // Calculate color cast correction factors
    double factorR = grayPoint / avgR;
    double factorG = grayPoint / avgG;
    double factorB = grayPoint / avgB;
    
    // Apply gentle correction (don't overcorrect)
    factorR = 1.0 + (factorR - 1.0) * 0.3;
    factorG = 1.0 + (factorG - 1.0) * 0.3;
    factorB = 1.0 + (factorB - 1.0) * 0.3;
    
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);
            
            int r = qBound(0, (int)(qRed(pixel) * factorR), 255);
            int g = qBound(0, (int)(qGreen(pixel) * factorG), 255);
            int b = qBound(0, (int)(qBlue(pixel) * factorB), 255);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

// Adaptive sharpening that preserves edges
QImage Zview::applyAdaptiveSharpening(const QImage &image, double amount) {
    QImage result(image.size(), image.format());
    
    // Create unsharp mask
    QImage blurred = applyGaussianNoiseReduction(image, 1.0, 5);
    
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb original = image.pixel(x, y);
            QRgb blur = blurred.pixel(x, y);
            
            // Calculate edge strength
            double edgeStrength = 0;
            if (x > 0 && x < image.width() - 1 && y > 0 && y < image.height() - 1) {
                QRgb left = image.pixel(x-1, y);
                QRgb right = image.pixel(x+1, y);
                QRgb top = image.pixel(x, y-1);
                QRgb bottom = image.pixel(x, y+1);
                
                double gradX = abs(qRed(right) - qRed(left)) + abs(qGreen(right) - qGreen(left)) + abs(qBlue(right) - qBlue(left));
                double gradY = abs(qRed(bottom) - qRed(top)) + abs(qGreen(bottom) - qGreen(top)) + abs(qBlue(bottom) - qBlue(top));
                edgeStrength = sqrt(gradX*gradX + gradY*gradY) / (3.0 * 255.0);
            }
            
            // Adaptive sharpening based on edge strength
            double adaptiveAmount = amount * edgeStrength;
            
            int r = qBound(0, (int)(qRed(original) + adaptiveAmount * (qRed(original) - qRed(blur))), 255);
            int g = qBound(0, (int)(qGreen(original) + adaptiveAmount * (qGreen(original) - qGreen(blur))), 255);
            int b = qBound(0, (int)(qBlue(original) + adaptiveAmount * (qBlue(original) - qBlue(blur))), 255);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

// ===============================================
// PROFESSIONAL PERFORMANCE OPTIMIZATION IMPLEMENTATION
// Based on Photoshop, DaVinci Resolve, and Affinity Photo techniques
// ===============================================

void Zview::initializePerformanceEngine() {
    // Initialize performance metrics timer
    if (!m_metricsTimer) {
        m_metricsTimer = new QTimer(this);
        connect(m_metricsTimer, &QTimer::timeout, this, &Zview::updatePerformanceMetrics);
        m_metricsTimer->start(1000); // Update every second
    }
    
    // Initialize smart cache with reasonable limits
    m_performanceCache.clear();
    m_imageCache.isValid = false;
    
    // Pre-allocate pyramid levels
    m_imagePyramid.clear();
    m_imagePyramid.reserve(8); // Up to 8 levels like Photoshop
    
    qDebug() << "Performance Engine initialized with professional-grade optimizations";
}

void Zview::optimizeForRealTimeEditing() {
    // Enable GPU acceleration if available
    if (isGPUAccelerationAvailable()) {
        enableGPUAcceleration(true);
    }
    
    // Pre-generate image pyramid for current image
    if (!m_currentImage.isNull()) {
        createImagePyramid(m_currentImage.toImage());
    }
    
    // Preload common filters for instant application
    preloadCommonFilters();
    
    // Optimize memory usage
    optimizeMemoryUsage();
}

QImage Zview::getOptimizedPreview(float zoomLevel) {
    // Use pyramid level optimization like Photoshop
    if (!m_imagePyramid.isEmpty()) {
        return getImageAtZoomLevel(zoomLevel);
    }
    
    // Fallback to adaptive quality preview
    return getAdaptiveQualityPreview(zoomLevel);
}

void Zview::cacheCurrentState(const QString& key) {
    if (!m_currentImage.isNull()) {
        QImage currentImage = m_currentImage.toImage();
        m_performanceCache[key] = currentImage;
        
        // Update cache entry timestamp
        m_imageCache.lastAccess.start();
        m_imageCache.isValid = true;
        
        // Limit cache size to prevent memory overflow
        if (m_performanceCache.size() > 20) { // Keep last 20 states
            // Remove oldest entries (simple LRU approximation)
            auto it = m_performanceCache.begin();
            for (int i = 0; i < 5 && it != m_performanceCache.end(); ++i) {
                it = m_performanceCache.erase(it);
            }
        }
    }
}

bool Zview::getCachedResult(const QString& key, QImage& result) {
    if (m_performanceCache.contains(key)) {
        result = m_performanceCache[key];
        return true;
    }
    return false;
}

void Zview::clearPerformanceCache() {
    m_performanceCache.clear();
    m_imageCache.isValid = false;
    m_imageCache.processedCache.clear();
    
    // Clear pyramid cache
    for (auto& level : m_imagePyramid) {
        level.isGenerated = false;
        level.image = QImage();
    }
    m_imagePyramid.clear();
}

void Zview::updatePerformanceMetrics() {
    // Calculate frame rate based on render time
    if (m_renderTimer.isValid()) {
        float renderTime = m_renderTimer.elapsed();
        m_performanceMetrics.renderTime = renderTime;
        m_performanceMetrics.frameRate = renderTime > 0 ? 1000.0f / renderTime : 60.0f;
    }
    
    // Update active filter count
    m_performanceMetrics.activeFilters = 0;
    if (m_brightnessValue != 0) m_performanceMetrics.activeFilters++;
    if (m_contrastValue != 0) m_performanceMetrics.activeFilters++;
    if (m_saturationValue != 0) m_performanceMetrics.activeFilters++;
    if (m_hueValue != 0) m_performanceMetrics.activeFilters++;
    if (m_blurValue != 0) m_performanceMetrics.activeFilters++;
    if (m_sharpenValue != 0) m_performanceMetrics.activeFilters++;
    if (m_noiseReductionValue != 0) m_performanceMetrics.activeFilters++;
    
    // Estimate memory usage
    m_performanceMetrics.memoryUsage = getCurrentMemoryUsage() / (1024.0f * 1024.0f); // MB
    
    // Simple CPU usage estimation based on active filters
    m_performanceMetrics.cpuUsage = qMin(m_performanceMetrics.activeFilters * 5.0f, 100.0f);
}

QString Zview::getPerformanceReport() const {
    return QString("Performance Report:\n"
                  "Frame Rate: %1 FPS\n"
                  "CPU Usage: %2%\n"
                  "Memory Usage: %3 MB\n"
                  "Active Filters: %4\n"
                  "Render Time: %5 ms\n"
                  "Cache Entries: %6")
           .arg(QString::number(m_performanceMetrics.frameRate, 'f', 1))
           .arg(QString::number(m_performanceMetrics.cpuUsage, 'f', 1))
           .arg(QString::number(m_performanceMetrics.memoryUsage, 'f', 1))
           .arg(m_performanceMetrics.activeFilters)
           .arg(QString::number(m_performanceMetrics.renderTime, 'f', 1))
           .arg(m_performanceCache.size());
}

void Zview::saveEditingState() {
    QString stateKey = QString("state_%1_%2_%3_%4_%5_%6")
                      .arg(m_brightnessValue)
                      .arg(m_contrastValue)
                      .arg(m_saturationValue)
                      .arg(m_hueValue)
                      .arg(m_blurValue)
                      .arg(m_sharpenValue);
    
    cacheCurrentState(stateKey);
}

bool Zview::restoreEditingState() {
    // This would restore from a saved state
    // Implementation depends on specific requirements
    return m_imageCache.isValid;
}

void Zview::createImagePyramid(const QImage& image) {
    if (image.isNull()) return;
    
    m_imagePyramid.clear();
    
    // Create multiple resolution levels like Photoshop
    QImage currentLevel = image;
    float currentScale = 1.0f;
    
    for (int level = 0; level < 8 && currentLevel.width() > 64 && currentLevel.height() > 64; ++level) {
        PyramidLevel pyramidLevel;
        pyramidLevel.image = currentLevel;
        pyramidLevel.scaleFactor = currentScale;
        pyramidLevel.isGenerated = true;
        
        m_imagePyramid.append(pyramidLevel);
        
        // Generate next level (half size)
        int newWidth = currentLevel.width() / 2;
        int newHeight = currentLevel.height() / 2;
        currentLevel = currentLevel.scaled(newWidth, newHeight, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        currentScale *= 0.5f;
    }
    
    qDebug() << "Created image pyramid with" << m_imagePyramid.size() << "levels";
}

QImage Zview::getImageAtZoomLevel(float zoomLevel) {
    if (m_imagePyramid.isEmpty()) {
        return QImage();
    }
    
    // Find optimal pyramid level for zoom (like Photoshop's zoom optimization)
    int optimalLevel = 0;
    for (int i = 0; i < m_imagePyramid.size(); ++i) {
        if (m_imagePyramid[i].scaleFactor >= zoomLevel) {
            optimalLevel = i;
            break;
        }
    }
    
    if (optimalLevel < m_imagePyramid.size()) {
        return m_imagePyramid[optimalLevel].image;
    }
    
    return m_imagePyramid.first().image;
}

bool Zview::isGPUAccelerationAvailable() const {
    // Simplified GPU detection
    QOpenGLContext* context = QOpenGLContext::currentContext();
    return context != nullptr;
}

void Zview::enableGPUAcceleration(bool enable) {
    if (enable && isGPUAccelerationAvailable()) {
        qDebug() << "GPU acceleration enabled for professional performance";
        // GPU acceleration would be implemented here using OpenGL compute shaders
        // This is a placeholder for the actual GPU implementation
    } else {
        qDebug() << "Using CPU-based processing (GPU acceleration not available or disabled)";
    }
}

void Zview::preloadCommonFilters() {
    // Pre-compute common filter kernels for instant application
    // This is similar to how DaVinci Resolve preloads effects
    
    if (m_currentImage.isNull()) return;
    
    QImage baseImage = m_currentImage.toImage();
    
    // Pre-cache common adjustments for instant preview
    QStringList commonFilters = {"brightness_25", "brightness_-25", "contrast_25", "contrast_-25",
                                "saturation_25", "saturation_-25", "blur_2", "sharpen_2"};
    
    for (const QString& filter : commonFilters) {
        if (!m_performanceCache.contains(filter)) {
            // This would pre-compute the filter result
            // For now, we just mark it as preloaded
            m_performanceCache[filter + "_preloaded"] = baseImage;
        }
    }
}

void Zview::generateOptimizedPreview() {
    if (m_currentImage.isNull()) return;
    
    m_renderTimer.start();
    
    // Generate preview using current zoom level
    float currentZoom = m_zoomFactor;
    QImage preview = getOptimizedPreview(currentZoom);
    
    if (!preview.isNull()) {
        m_imageCache.previewImage = preview;
        m_imageCache.isValid = true;
    }
    
    // Record render time for performance metrics
    m_performanceMetrics.renderTime = m_renderTimer.elapsed();
}

void Zview::updatePreviewInBackground() {
    // This would update preview in a background thread for smooth UI
    // Similar to DaVinci Resolve's background rendering
    generateOptimizedPreview();
}

QImage Zview::getAdaptiveQualityPreview(float zoomLevel) {
    if (m_currentImage.isNull()) return QImage();
    
    QImage baseImage = m_currentImage.toImage();
    
    // PERFORMANCE-OPTIMIZED QUALITY SCALING
    if (zoomLevel < 0.25f) {
        // Very zoomed out - use fast smooth scaling
        int targetWidth = baseImage.width() * 0.5f;  // Increased from 0.25f to reduce pixelation
        int targetHeight = baseImage.height() * 0.5f;
        QSize targetSize(targetWidth, targetHeight);
        return getOptimizedScaling(baseImage, targetSize, true); // Always interactive for zoom
    } else if (zoomLevel < 0.5f) {
        // Moderately zoomed out - use optimized scaling
        int targetWidth = baseImage.width() * 0.75f;  // Increased resolution for better quality
        int targetHeight = baseImage.height() * 0.75f;
        QSize targetSize(targetWidth, targetHeight);
        return getOptimizedScaling(baseImage, targetSize, true); // Always interactive for zoom
    } else {
        // Zoomed in or normal view - return original for best performance
        return baseImage;
    }
}

void Zview::optimizeMemoryUsage() {
    // Clear unused cache entries
    clearUnusedCache();
    
    // Optimize image pyramid based on current zoom level
    if (!m_imagePyramid.isEmpty()) {
        float currentZoom = m_zoomFactor;
        
        // Keep only relevant pyramid levels
        for (int i = 0; i < m_imagePyramid.size(); ++i) {
            if (abs(m_imagePyramid[i].scaleFactor - currentZoom) > 2.0f) {
                // Clear levels that are far from current zoom
                m_imagePyramid[i].image = QImage();
                m_imagePyramid[i].isGenerated = false;
            }
        }
    }
}

void Zview::clearUnusedCache() {
    // Remove cache entries that haven't been accessed recently
    const qint64 maxAge = 300000; // 5 minutes
    
    QStringList keysToRemove;
    for (auto it = m_performanceCache.constBegin(); it != m_performanceCache.constEnd(); ++it) {
        // This is a simplified version - in a real implementation,
        // we'd track access times for each cache entry
        if (m_imageCache.lastAccess.hasExpired(maxAge)) {
            keysToRemove.append(it.key());
        }
    }
    
    for (const QString& key : keysToRemove) {
        m_performanceCache.remove(key);
    }
    
    qDebug() << "Cleared" << keysToRemove.size() << "unused cache entries";
}

size_t Zview::getCurrentMemoryUsage() const {
    size_t totalUsage = 0;
    
    // Calculate cache memory usage
    for (auto it = m_performanceCache.constBegin(); it != m_performanceCache.constEnd(); ++it) {
        const QImage& img = it.value();
        totalUsage += img.sizeInBytes();
    }
    
    // Add pyramid memory usage
    for (const auto& level : m_imagePyramid) {
        if (level.isGenerated) {
            totalUsage += level.image.sizeInBytes();
        }
    }
    
    // Add current image memory usage
    if (!m_currentImage.isNull()) {
        totalUsage += m_currentImage.toImage().sizeInBytes();
    }
    
    return totalUsage;
}

void Zview::updateCursorForPosition()
{
    if (!m_cropModeActive || m_drawingCrop || m_movingCrop || m_draggingHandle >= 0 || 
        m_draggingAMarker || m_draggingBMarker) {
        return; // Don't update cursor during active operations
    }
    
    // Always use the actual current cursor position for maximum accuracy
    QPoint globalCursorPos = QCursor::pos();
    QPoint currentPos = mapFromGlobal(globalCursorPos);
    
    // Get current cursor state based on position
    Qt::CursorShape targetCursor = Qt::CrossCursor; // Default to crosshair
    
    if (m_cropModeActive) {
        int handleIndex = getCropHandleAtPosition(currentPos);
        
        // Check if we're over the crop area
        bool inCropArea = false;
        if (m_cropOverlay && m_cropOverlay->isVisible()) {
            inCropArea = m_cropOverlay->geometry().contains(currentPos);
        }
        
        // Use generous tolerance for aggressive updates to ensure reliable detection
        QRect expandedCropRect = m_cropRect.adjusted(-4, -4, 4, 4);
        bool finalInCropArea = inCropArea || expandedCropRect.contains(currentPos);
        
        if (handleIndex >= 0) {
            // Over a handle - set appropriate resize cursor
            if (handleIndex == 0 || handleIndex == 3) {
                targetCursor = Qt::SizeFDiagCursor;      // Top-left, Bottom-right (NW-SE)
            } else if (handleIndex == 1 || handleIndex == 2) {
                targetCursor = Qt::SizeBDiagCursor;      // Top-right, Bottom-left (NE-SW)
            } else if (handleIndex == 4 || handleIndex == 5) {
                targetCursor = Qt::SizeVerCursor;        // Top, Bottom (N-S)
            } else if (handleIndex == 6 || handleIndex == 7) {
                targetCursor = Qt::SizeHorCursor;        // Left, Right (E-W)
            }
        } else if (finalInCropArea) {
            // Inside crop area - show move cursor
            targetCursor = Qt::SizeAllCursor;
        } else {
            // Outside crop area - show crosshair
            targetCursor = Qt::CrossCursor;
        }
    }
    
    // Always update cursor, even if it seems the same, to force Qt to refresh
    setCursor(targetCursor);
    m_currentCursorShape = targetCursor;
}

// ===== IMAGE EDITOR IMPLEMENTATION =====

void Zview::showImageEditor()
{
    // Only activate for images, not videos
    if (m_isPlayingVideo || m_currentImage.isNull()) {
        qDebug() << "Image editor is only available for images";
        return;
    }
    
    if (m_imageEditorActive) return;
    
    m_imageEditorActive = true;
    
    // Store working original image as backup (true original is already stored during image loading)
    qDebug() << "IMAGE EDITOR: Using true original - size:" << m_trueOriginalImageData.size() << "format:" << m_trueOriginalImageData.format();
    qDebug() << "IMAGE EDITOR: Current image size:" << m_currentImage.size() << "isNull:" << m_currentImage.isNull();
    
    // Don't overwrite the true original - it was stored during image loading
    // Just set the working images
    m_originalImage = m_currentImage;
    m_previewImage = m_currentImage;
    
    // Setup the image editor toolbox
    setupImageEditorToolbox();
    
    // Update button state
    ui->editTool->setStyleSheet(
        "QToolButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 120, 215, 0.9),"
        "                               stop:1 rgba(0, 100, 180, 0.9));"
        "    color: white;"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "    border-radius: 6px;"
        "    padding: 6px;"
        "    font-size: 11px;"
        "    font-weight: 600;"
        "    box-shadow: 0 4px 16px rgba(0, 120, 215, 0.3);"
        "}"
        "QToolButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 140, 235, 1.0),"
        "                               stop:1 rgba(0, 120, 200, 1.0));"
        "    box-shadow: 0 6px 20px rgba(0, 120, 215, 0.4);"
        "}");
    
    qDebug() << "Image editor activated";
}

void Zview::hideImageEditor()
{
    if (!m_imageEditorActive) return;
    
    m_imageEditorActive = false;
    
    // Hide the toolboxes
    if (m_imageEditorToolbox) {
        m_imageEditorToolbox->setVisible(false);
    }
    if (m_leftImageEditorToolbox) {
        m_leftImageEditorToolbox->setVisible(false);
    }
    
    // Reset button state
    ui->editTool->setStyleSheet(
        "QToolButton {"
        "    background: rgba(60, 60, 65, 0.95);"
        "    color: #FFFFFF;"
        "    border: none;"
        "    border-radius: 6px;"
        "    padding: 6px;"
        "    font-size: 11px;"
        "    font-weight: 600;"
        "    font-family: 'Segoe UI', system-ui, sans-serif;"
        "}"
        "QToolButton:hover {"
        "    background: rgba(0, 120, 212, 1.0);"
        "    color: #FFFFFF;"
        "}"
        "QToolButton:pressed {"
        "    background: rgba(0, 90, 158, 1.0);"
        "    transform: scale(0.96);"
        "}"
        "QToolButton:checked {"
        "    background: rgba(0, 120, 212, 1.0);"
        "    color: #FFFFFF;"
        "}");
    
    // Reset to original image if not applied
    m_currentImage = m_originalImage;
    m_textureNeedsUpdate = true;
    update();
    
    qDebug() << "Image editor deactivated";
}

void Zview::setupImageEditorToolbox()
{
    if (m_imageEditorToolbox) {
        delete m_imageEditorToolbox;
    }
    
    // Create the main toolbox widget
    m_imageEditorToolbox = new QWidget(this);
    m_imageEditorToolbox->setObjectName("imageEditorToolbox");
    
    // Ultra-modern sleek design styling with glassmorphism and enhanced visuals
    m_imageEditorToolbox->setStyleSheet(
        "QWidget#imageEditorToolbox {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(28, 28, 32, 0.98),"
        "                               stop:0.5 rgba(32, 32, 38, 0.96),"
        "                               stop:1 rgba(24, 24, 28, 0.98));"
        "    border: 2px solid qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                                     stop:0 rgba(100, 100, 110, 0.4),"
        "                                     stop:1 rgba(60, 60, 70, 0.6));"
        "    border-radius: 16px;"
        "    padding: 0px;"
        "    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);"
        "}"
        
        "QLabel {"
        "    color: #F5F5F7;"
        "    font-size: 13px;"
        "    font-weight: 600;"
        "    font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;"
        "    letter-spacing: 0.3px;"
        "    padding: 1px 0px;"
        "    margin: 0px 0px;"
        "}"
        
        "QLabel[accessibleName=\"sectionTitle\"] {"
        "    color: #007AFF;"
        "    font-size: 14px;"
        "    font-weight: 700;"
        "    padding: 8px 0px 4px 0px;"
        "    border-bottom: 1px solid rgba(0, 122, 255, 0.3);"
        "    margin-bottom: 6px;"
        "}"
        
        "QLabel[accessibleName=\"mainTitle\"] {"
        "    color: #FFFFFF;"
        "    font-size: 13px;"
        "    font-weight: 700;"
        "    padding: 6px 0px 4px 0px;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
        "                               stop:0 rgba(0, 122, 255, 0.1),"
        "                               stop:1 rgba(88, 86, 214, 0.1));"
        "    border-radius: 6px;"
        "    margin-bottom: 4px;"
        "}"
        
        "QSlider {"
        "    height: 16px;"
        "    margin: 0px 0px;"
        "}"
        
        "QSlider::groove:horizontal {"
        "    border: none;"
        "    height: 8px;"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(50, 50, 55, 0.8),"
        "                               stop:1 rgba(35, 35, 40, 0.9));"
        "    border-radius: 4px;"
        "    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);"
        "}"
        
        "QSlider::handle:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 122, 255, 1.0),"
        "                               stop:0.5 rgba(30, 144, 255, 1.0),"
        "                               stop:1 rgba(0, 100, 210, 1.0));"
        "    border: 1px solid rgba(255, 255, 255, 0.8);"
        "    width: 14px;"
        "    height: 14px;"
        "    margin: -5px 0;"
        "    border-radius: 8px;"
        "    box-shadow: 0 2px 6px rgba(0, 122, 255, 0.4);"
        "}"
        
        "QSlider::handle:horizontal:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(30, 144, 255, 1.0),"
        "                               stop:0.5 rgba(64, 175, 255, 1.0),"
        "                               stop:1 rgba(0, 122, 255, 1.0));"
        "    border: 2px solid rgba(255, 255, 255, 0.9);"
        "    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.6);"
        "    transform: scale(1.05);"
        "}"
        
        "QSlider::handle:horizontal:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 100, 210, 1.0),"
        "                               stop:1 rgba(0, 80, 170, 1.0));"
        "    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.8);"
        "    transform: scale(0.95);"
        "}"
        
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "    border-radius: 4px;"
        "    padding: 4px 6px;"
        "    font-size: 11px;"
        "    font-weight: 600;"
        "    font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;"
        "    min-height: 22px;"
        "    max-height: 26px;"
        "    letter-spacing: 0.1px;"
        "}"
        
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(80, 80, 80, 1.0),"
        "                               stop:1 rgba(60, 60, 60, 1.0));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(120, 120, 120, 0.8);"
        "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(50, 50, 50, 1.0),"
        "                               stop:1 rgba(30, 30, 30, 1.0));"
        "    transform: translateY(0px) scale(0.98);"
        "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);"
        "}"
        
        "QPushButton:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 122, 255, 0.9),"
        "                               stop:1 rgba(0, 100, 210, 0.95));"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    color: #FFFFFF;"
        "    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton:checked:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(30, 144, 255, 1.0),"
        "                               stop:1 rgba(0, 122, 255, 1.0));"
        "    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.5);"
        "}"
        
        "QPushButton[accessibleName=\"actionButton\"] {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "    font-weight: 700;"
        "}"
        
        "QPushButton[accessibleName=\"actionButton\"]:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(52, 199, 89, 0.9),"
        "                               stop:1 rgba(48, 176, 80, 0.95));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    box-shadow: 0 4px 16px rgba(52, 199, 89, 0.4);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton[accessibleName=\"actionButton\"]:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(52, 199, 89, 0.9),"
        "                               stop:1 rgba(48, 176, 80, 0.95));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    box-shadow: 0 4px 16px rgba(52, 199, 89, 0.4);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton[accessibleName=\"resetButton\"] {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "}"
        
        "QPushButton[accessibleName=\"resetButton\"]:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(255, 69, 58, 0.9),"
        "                               stop:1 rgba(215, 58, 48, 0.95));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    box-shadow: 0 4px 16px rgba(255, 69, 58, 0.4);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton[accessibleName=\"cancelButton\"] {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "}"
        
        "QPushButton[accessibleName=\"cancelButton\"]:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(255, 159, 10, 0.9),"
        "                               stop:1 rgba(230, 140, 8, 0.95));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    box-shadow: 0 4px 16px rgba(255, 159, 10, 0.4);"
        "    transform: translateY(-1px);"
        "}");
    
    // Create layout with ultra-compact spacing and structure
    QVBoxLayout *mainLayout = new QVBoxLayout(m_imageEditorToolbox);
    mainLayout->setSpacing(3);  // Ultra-compact spacing
    mainLayout->setContentsMargins(8, 6, 8, 6);  // Minimal margins
    
    // Main title with modern styling
    QLabel *titleLabel = new QLabel("✨ Image Editor");
    titleLabel->setAccessibleName("mainTitle");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);
    
    // Add minimal separator
    mainLayout->addSpacing(2);
    
    // Adjustments section with compact styling
    QLabel *adjustLabel = new QLabel("🎨 Adjustments");
    adjustLabel->setAccessibleName("sectionTitle");
    adjustLabel->setAlignment(Qt::AlignLeft);
    mainLayout->addWidget(adjustLabel);
    
    // Brightness slider
    QLabel *brightnessLabel = new QLabel("Brightness");
    m_brightnessSlider = new QSlider(Qt::Horizontal);
    m_brightnessSlider->setRange(-100, 100);
    m_brightnessSlider->setValue(0);
    connect(m_brightnessSlider, &QSlider::valueChanged, this, [this](int value) {
        m_brightnessValue = value;
        scheduleUpdateDebounced();
    });
    mainLayout->addWidget(brightnessLabel);
    mainLayout->addWidget(m_brightnessSlider);
    
    // Contrast slider
    QLabel *contrastLabel = new QLabel("Contrast");
    m_contrastSlider = new QSlider(Qt::Horizontal);
    m_contrastSlider->setRange(-100, 100);
    m_contrastSlider->setValue(0);
    connect(m_contrastSlider, &QSlider::valueChanged, this, [this](int value) {
        m_contrastValue = value;
        scheduleUpdateDebounced();
    });
    mainLayout->addWidget(contrastLabel);
    mainLayout->addWidget(m_contrastSlider);
    
    // Saturation slider
    QLabel *saturationLabel = new QLabel("Saturation");
    m_saturationSlider = new QSlider(Qt::Horizontal);
    m_saturationSlider->setRange(-100, 100);
    m_saturationSlider->setValue(0);
    connect(m_saturationSlider, &QSlider::valueChanged, this, [this](int value) {
        m_saturationValue = value;
        scheduleUpdateDebounced();
    });
    mainLayout->addWidget(saturationLabel);
    mainLayout->addWidget(m_saturationSlider);
    
    // Hue slider
    QLabel *hueLabel = new QLabel("Hue");
    m_hueSlider = new QSlider(Qt::Horizontal);
    m_hueSlider->setRange(-180, 180);
    m_hueSlider->setValue(0);
    connect(m_hueSlider, &QSlider::valueChanged, this, [this](int value) {
        m_hueValue = value;
        scheduleUpdateDebounced();
    });
    mainLayout->addWidget(hueLabel);
    mainLayout->addWidget(m_hueSlider);
    
    // Effects section with ultra-compact styling
    mainLayout->addSpacing(2); // Ultra-compact spacing between sections
    QLabel *effectsLabel = new QLabel("✨ Effects");
    effectsLabel->setAccessibleName("sectionTitle");
    effectsLabel->setAlignment(Qt::AlignLeft);
    mainLayout->addWidget(effectsLabel);
    
    // Blur slider with performance optimization
    QLabel *blurLabel = new QLabel("Blur");
    m_blurSlider = new QSlider(Qt::Horizontal);
    m_blurSlider->setRange(0, 10);  // Reduced max range for performance
    m_blurSlider->setValue(0);
    
    // Use timer-based debouncing for blur to prevent lag during dragging
    if (!m_blurTimer) {
        m_blurTimer = new QTimer(this);
        m_blurTimer->setSingleShot(true);
        m_blurTimer->setInterval(150); // 150ms delay
        connect(m_blurTimer, &QTimer::timeout, this, [this]() {
            applyBlur(m_blurSlider->value());
        });
    }
    
    connect(m_blurSlider, &QSlider::valueChanged, this, [this](int value) {
        m_blurValue = value;
        m_blurTimer->start(); // Restart timer on each change
    });
    
    mainLayout->addWidget(blurLabel);
    mainLayout->addWidget(m_blurSlider);
    
    // Sharpen slider
    QLabel *sharpenLabel = new QLabel("Sharpen");
    m_sharpenSlider = new QSlider(Qt::Horizontal);
    m_sharpenSlider->setRange(0, 100);
    m_sharpenSlider->setValue(0);
    connect(m_sharpenSlider, &QSlider::valueChanged, this, [this](int value) {
        m_sharpenValue = value;
        scheduleUpdateDebounced();
    });
    mainLayout->addWidget(sharpenLabel);
    mainLayout->addWidget(m_sharpenSlider);
    
    // Transform section with ultra-compact styling
    mainLayout->addSpacing(2); // Ultra-compact spacing between sections
    QLabel *transformLabel = new QLabel("🔄 Transform");
    transformLabel->setAccessibleName("sectionTitle");
    transformLabel->setAlignment(Qt::AlignLeft);
    mainLayout->addWidget(transformLabel);
    
    // Rotation buttons with ultra-compact icons
    QHBoxLayout *rotateLayout = new QHBoxLayout();
    rotateLayout->setSpacing(2); // Ultra-compact spacing
    m_rotateLeftBtn = new QPushButton("↶ Left");
    m_rotateRightBtn = new QPushButton("↷ Right");
    m_rotateLeftBtn->setCheckable(true);
    m_rotateRightBtn->setCheckable(true);
    connect(m_rotateLeftBtn, &QPushButton::clicked, this, [this]() { rotateImage(-90); });
    connect(m_rotateRightBtn, &QPushButton::clicked, this, [this]() { rotateImage(90); });
    rotateLayout->addWidget(m_rotateLeftBtn);
    rotateLayout->addWidget(m_rotateRightBtn);
    mainLayout->addLayout(rotateLayout);
    
    // Flip buttons with ultra-compact icons
    QHBoxLayout *flipLayout = new QHBoxLayout();
    flipLayout->setSpacing(2); // Ultra-compact spacing
    m_flipHorizontalBtn = new QPushButton("⬌ H");
    m_flipVerticalBtn = new QPushButton("⬍ V");
    m_flipHorizontalBtn->setCheckable(true);
    m_flipVerticalBtn->setCheckable(true);
    connect(m_flipHorizontalBtn, &QPushButton::clicked, this, &Zview::flipImageHorizontal);
    connect(m_flipVerticalBtn, &QPushButton::clicked, this, &Zview::flipImageVertical);
    flipLayout->addWidget(m_flipHorizontalBtn);
    flipLayout->addWidget(m_flipVerticalBtn);
    mainLayout->addLayout(flipLayout);
    
    // Filters section with ultra-compact styling
    mainLayout->addSpacing(2); // Ultra-compact spacing between sections
    QLabel *filtersLabel = new QLabel("🎭 Filters");
    filtersLabel->setAccessibleName("sectionTitle");
    filtersLabel->setAlignment(Qt::AlignLeft);
    mainLayout->addWidget(filtersLabel);
    
    // Filter buttons with ultra-compact icons
    QHBoxLayout *filterLayout1 = new QHBoxLayout();
    filterLayout1->setSpacing(2); // Ultra-compact spacing
    m_grayscaleBtn = new QPushButton("⚫ Gray");
    m_sepiaBtn = new QPushButton("🟤 Sepia");
    m_grayscaleBtn->setCheckable(true);
    m_sepiaBtn->setCheckable(true);
    connect(m_grayscaleBtn, &QPushButton::clicked, this, &Zview::applyGrayscale);
    connect(m_sepiaBtn, &QPushButton::clicked, this, &Zview::applySepia);
    filterLayout1->addWidget(m_grayscaleBtn);
    filterLayout1->addWidget(m_sepiaBtn);
    mainLayout->addLayout(filterLayout1);
    
    QHBoxLayout *filterLayout2 = new QHBoxLayout();
    filterLayout2->setSpacing(2); // Ultra-compact spacing
    m_embossBtn = new QPushButton("🔨 Emboss");
    m_embossBtn->setCheckable(true);
    connect(m_embossBtn, &QPushButton::clicked, this, &Zview::applyEmboss);
    filterLayout2->addWidget(m_embossBtn);
    filterLayout2->addStretch();
    mainLayout->addLayout(filterLayout2);
    
    // Action buttons section with ultra-compact styling and icons
    mainLayout->addSpacing(4); // Minimal spacing before action buttons
    
    QHBoxLayout *actionLayout1 = new QHBoxLayout();
    actionLayout1->setSpacing(2); // Ultra-compact button spacing
    m_resetEditsBtn = new QPushButton("🔄 Reset");
    m_saveImageBtn = new QPushButton("💾 Save");
    m_resetEditsBtn->setAccessibleName("resetButton");
    m_saveImageBtn->setAccessibleName("actionButton");
    connect(m_resetEditsBtn, &QPushButton::clicked, this, [this]() {
        qDebug() << "RESET BUTTON: Button clicked signal received";
        
        // Ensure the main window has focus to prevent curve adjustor from receiving events
        this->setFocus();
        this->activateWindow();
        
        // Close curve adjustor if it's open to prevent interference
        if (m_curveAdjustorDialog) {
            m_curveAdjustorDialog->close();
            m_curveAdjustorDialog = nullptr;
        }
        
        resetImageEdits();
    });
    connect(m_saveImageBtn, &QPushButton::clicked, this, &Zview::saveEditedImage);
    actionLayout1->addWidget(m_resetEditsBtn);
    actionLayout1->addWidget(m_saveImageBtn);
    mainLayout->addLayout(actionLayout1);
    
    mainLayout->addSpacing(2); // Minimal spacing between action button rows
    
    QHBoxLayout *actionLayout2 = new QHBoxLayout();
    actionLayout2->setSpacing(2); // Ultra-compact button spacing
    m_applyEditsBtn = new QPushButton("✅ Apply");
    m_cancelEditsBtn = new QPushButton("❌ Cancel");
    m_applyEditsBtn->setAccessibleName("actionButton");
    m_cancelEditsBtn->setAccessibleName("cancelButton");
    connect(m_applyEditsBtn, &QPushButton::clicked, this, &Zview::applyImageEdits);
    connect(m_cancelEditsBtn, &QPushButton::clicked, this, &Zview::cancelImageEdits);
    actionLayout2->addWidget(m_applyEditsBtn);
    actionLayout2->addWidget(m_cancelEditsBtn);
    mainLayout->addLayout(actionLayout2);
    
    // Add minimal final bottom spacing
    mainLayout->addSpacing(4);
    
    // Position and show the toolbox
    repositionImageEditorToolbox();
    m_imageEditorToolbox->setVisible(true);
    
    // Setup and show the left advanced toolbox
    setupLeftImageEditorToolbox();
}

void Zview::repositionImageEditorToolbox()
{
    if (!m_imageEditorToolbox) return;
    
    const int toolboxWidth = 220;  // Reduced width for compact design
    const int toolboxHeight = 920; // Extended height with some extra vertical space
    const int margin = 20;         // Standard margin for compact design
    
    // Position on the right side with modern spacing
    int toolboxX = width() - toolboxWidth - margin;
    int toolboxY = margin;
    
    m_imageEditorToolbox->setGeometry(toolboxX, toolboxY, toolboxWidth, toolboxHeight);
}

void Zview::setupLeftImageEditorToolbox()
{
    if (m_leftImageEditorToolbox) {
        delete m_leftImageEditorToolbox;
    }
    
    // Create the left advanced toolbox widget
    m_leftImageEditorToolbox = new QWidget(this);
    m_leftImageEditorToolbox->setObjectName("leftImageEditorToolbox");
    
    // Apply enhanced modern styling for professional tools
    m_leftImageEditorToolbox->setStyleSheet(
        "QWidget#leftImageEditorToolbox {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(28, 28, 32, 0.98),"
        "                               stop:0.5 rgba(32, 32, 38, 0.96),"
        "                               stop:1 rgba(24, 24, 28, 0.98));"
        "    border: 2px solid qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                                     stop:0 rgba(100, 100, 110, 0.4),"
        "                                     stop:1 rgba(60, 60, 70, 0.6));"
        "    border-radius: 16px;"
        "    padding: 0px;"
        "    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);"
        "}"
        
        "QLabel {"
        "    color: #F5F5F7;"
        "    font-size: 11px;"
        "    font-weight: 600;"
        "    font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;"
        "    letter-spacing: 0.3px;"
        "    padding: 1px 0px;"
        "    margin: 0px 0px;"
        "}"
        
        "QLabel[accessibleName=\"sectionTitle\"] {"
        "    color: #FF6B35;"
        "    font-size: 12px;"
        "    font-weight: 700;"
        "    padding: 6px 0px 3px 0px;"
        "    border-bottom: 1px solid rgba(255, 107, 53, 0.3);"
        "    margin-bottom: 4px;"
        "}"
        
        "QLabel[accessibleName=\"mainTitle\"] {"
        "    color: #FFFFFF;"
        "    font-size: 13px;"
        "    font-weight: 700;"
        "    padding: 6px 0px 4px 0px;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
        "                               stop:0 rgba(255, 107, 53, 0.1),"
        "                               stop:1 rgba(255, 159, 10, 0.1));"
        "    border-radius: 6px;"
        "    margin-bottom: 4px;"
        "}"
        
        "QSlider {"
        "    height: 14px;"
        "    margin: 0px 0px;"
        "}"
        
        "QSlider::groove:horizontal {"
        "    border: none;"
        "    height: 6px;"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(50, 50, 55, 0.8),"
        "                               stop:1 rgba(35, 35, 40, 0.9));"
        "    border-radius: 3px;"
        "    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);"
        "}"
        
        "QSlider::handle:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(255, 107, 53, 1.0),"
        "                               stop:0.5 rgba(255, 140, 30, 1.0),"
        "                               stop:1 rgba(255, 107, 53, 1.0));"
        "    border: 1px solid rgba(255, 255, 255, 0.8);"
        "    width: 12px;"
        "    height: 12px;"
        "    margin: -4px 0;"
        "    border-radius: 6px;"
        "    box-shadow: 0 2px 4px rgba(255, 107, 53, 0.4);"
        "}"
        
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "    border-radius: 4px;"
        "    padding: 3px 5px;"
        "    font-size: 10px;"
        "    font-weight: 600;"
        "    font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;"
        "    min-height: 20px;"
        "    max-height: 24px;"
        "    letter-spacing: 0.1px;"
        "}"
        
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(80, 80, 80, 1.0),"
        "                               stop:1 rgba(60, 60, 60, 1.0));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(120, 120, 120, 0.8);"
        "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(0, 122, 255, 0.9),"
        "                               stop:1 rgba(0, 100, 210, 0.95));"
        "    border: 1px solid rgba(255, 255, 255, 0.4);"
        "    color: #FFFFFF;"
        "    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton:checked:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(30, 144, 255, 1.0),"
        "                               stop:1 rgba(0, 122, 255, 1.0));"
        "    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.5);"
        "}"
        
        "QPushButton[accessibleName=\"proButton\"] {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 60, 0.9),"
        "                               stop:1 rgba(40, 40, 40, 0.95));"
        "    color: #CCCCCC;"
        "    border: 1px solid rgba(80, 80, 80, 0.6);"
        "    border-radius: 6px;"
        "    padding: 6px 10px;"
        "    font-size: 11px;"
        "    font-weight: 700;"
        "    min-height: 28px;"
        "    letter-spacing: 0.2px;"
        "}"
        
        "QPushButton[accessibleName=\"proButton\"]:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(80, 80, 80, 1.0),"
        "                               stop:1 rgba(60, 60, 60, 1.0));"
        "    color: #FFFFFF;"
        "    border: 1px solid rgba(120, 120, 120, 0.8);"
        "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);"
        "    transform: translateY(-1px);"
        "}"
        
        "QPushButton[accessibleName=\"proButton\"]:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(255, 127, 73, 1.0),"
        "                               stop:1 rgba(255, 107, 53, 1.0));"
        "    color: #FFFFFF;"
        "    border: 2px solid rgba(255, 255, 255, 0.6);"
        "    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4);"
        "    transform: translateY(-2px);"
        "}"
        
        "QPushButton[accessibleName=\"proButton\"]:checked:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(255, 140, 90, 1.0),"
        "                               stop:1 rgba(255, 120, 70, 1.0));"
        "    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.5);"
        "}");
    
    // Create layout with ultra-compact spacing for professional tools
    QVBoxLayout *mainLayout = new QVBoxLayout(m_leftImageEditorToolbox);
    mainLayout->setSpacing(2);
    mainLayout->setContentsMargins(6, 5, 6, 5);
    
    // Tab buttons at the top
    QHBoxLayout *tabLayout = new QHBoxLayout();
    tabLayout->setSpacing(2);
    m_leftTab1Btn = new QPushButton("🎯 Basic Tools");
    m_leftTab2Btn = new QPushButton("✨ Advanced");
    m_leftTab1Btn->setCheckable(true);
    m_leftTab2Btn->setCheckable(true);
    m_leftTab1Btn->setChecked(true);
    m_leftTab1Btn->setAccessibleName("proButton");
    m_leftTab2Btn->setAccessibleName("proButton");
    connect(m_leftTab1Btn, &QPushButton::clicked, this, [this]() { switchLeftToolboxTab(1); });
    connect(m_leftTab2Btn, &QPushButton::clicked, this, [this]() { switchLeftToolboxTab(2); });
    tabLayout->addWidget(m_leftTab1Btn);
    tabLayout->addWidget(m_leftTab2Btn);
    mainLayout->addLayout(tabLayout);
    mainLayout->addSpacing(2);
    
    // Create Tab 1 - Basic Tools
    m_leftToolboxTab1 = new QWidget();
    QVBoxLayout *tab1Layout = new QVBoxLayout(m_leftToolboxTab1);
    tab1Layout->setSpacing(2);
    tab1Layout->setContentsMargins(0, 0, 0, 0);
    
    // Create Tab 2 - Advanced Tools
    m_leftToolboxTab2 = new QWidget();
    QVBoxLayout *tab2Layout = new QVBoxLayout(m_leftToolboxTab2);
    tab2Layout->setSpacing(2);
    tab2Layout->setContentsMargins(0, 0, 0, 0);
    
    // Add tabs to main layout
    mainLayout->addWidget(m_leftToolboxTab1);
    mainLayout->addWidget(m_leftToolboxTab2);
    
    // Initially show only Tab 1
    m_leftToolboxTab1->setVisible(true);
    m_leftToolboxTab2->setVisible(false);
    
    // === TAB 1 CONTENT - BASIC TOOLS ===
    
    // Curve Adjustor button (keeping existing)
    m_curveAdjustorBtn = new QPushButton("📈 Curve Adjustor");
    m_curveAdjustorBtn->setAccessibleName("proButton");
    connect(m_curveAdjustorBtn, &QPushButton::clicked, this, &Zview::showCurveAdjustor);
    tab1Layout->addWidget(m_curveAdjustorBtn);
    tab1Layout->addSpacing(1);
    
    // === PROFESSIONAL HSL COLOR EDITOR (Lightroom-style) ===
    QLabel *hslLabel = new QLabel("🌈 HSL Color Editor");
    hslLabel->setAccessibleName("sectionTitle");
    tab1Layout->addWidget(hslLabel);
    
    // HSL Channel selector buttons
    QHBoxLayout *hslChannelLayout = new QHBoxLayout();
    hslChannelLayout->setSpacing(1);
    m_hslAllBtn = new QPushButton("All");
    m_hslRedBtn = new QPushButton("R");
    m_hslOrangeBtn = new QPushButton("O");
    m_hslYellowBtn = new QPushButton("Y");
    m_hslGreenBtn = new QPushButton("G");
    m_hslAquaBtn = new QPushButton("A");
    m_hslBlueBtn = new QPushButton("B");
    m_hslPurpleBtn = new QPushButton("P");
    m_hslMagentaBtn = new QPushButton("M");
    
    // Make HSL buttons checkable and set All as default
    m_hslAllBtn->setCheckable(true);
    m_hslRedBtn->setCheckable(true);
    m_hslOrangeBtn->setCheckable(true);
    m_hslYellowBtn->setCheckable(true);
    m_hslGreenBtn->setCheckable(true);
    m_hslAquaBtn->setCheckable(true);
    m_hslBlueBtn->setCheckable(true);
    m_hslPurpleBtn->setCheckable(true);
    m_hslMagentaBtn->setCheckable(true);
    m_hslAllBtn->setChecked(true);
    
    // Connect HSL channel buttons
    connect(m_hslAllBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(0); });
    connect(m_hslRedBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(1); });
    connect(m_hslOrangeBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(2); });
    connect(m_hslYellowBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(3); });
    connect(m_hslGreenBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(4); });
    connect(m_hslAquaBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(5); });
    connect(m_hslBlueBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(6); });
    connect(m_hslPurpleBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(7); });
    connect(m_hslMagentaBtn, &QPushButton::clicked, this, [this]() { setHSLChannel(8); });
    
    hslChannelLayout->addWidget(m_hslAllBtn);
    hslChannelLayout->addWidget(m_hslRedBtn);
    hslChannelLayout->addWidget(m_hslOrangeBtn);
    hslChannelLayout->addWidget(m_hslYellowBtn);
    hslChannelLayout->addWidget(m_hslGreenBtn);
    hslChannelLayout->addWidget(m_hslAquaBtn);
    hslChannelLayout->addWidget(m_hslBlueBtn);
    hslChannelLayout->addWidget(m_hslPurpleBtn);
    hslChannelLayout->addWidget(m_hslMagentaBtn);
    tab1Layout->addLayout(hslChannelLayout);
    
    // HSL Hue slider
    QLabel *hslHueLabel = new QLabel("Hue");
    m_hslHueSlider = new QSlider(Qt::Horizontal);
    m_hslHueSlider->setRange(-180, 180);
    m_hslHueSlider->setValue(0);
    connect(m_hslHueSlider, &QSlider::valueChanged, this, [this](int value) {
        m_hslHueValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(hslHueLabel);
    tab1Layout->addWidget(m_hslHueSlider);
    
    // HSL Saturation slider
    QLabel *hslSatLabel = new QLabel("Saturation");
    m_hslSaturationSlider = new QSlider(Qt::Horizontal);
    m_hslSaturationSlider->setRange(-100, 100);
    m_hslSaturationSlider->setValue(0);
    connect(m_hslSaturationSlider, &QSlider::valueChanged, this, [this](int value) {
        m_hslSaturationValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(hslSatLabel);
    tab1Layout->addWidget(m_hslSaturationSlider);
    
    // HSL Luminance slider
    QLabel *hslLumLabel = new QLabel("Luminance");
    m_hslLuminanceSlider = new QSlider(Qt::Horizontal);
    m_hslLuminanceSlider->setRange(-100, 100);
    m_hslLuminanceSlider->setValue(0);
    connect(m_hslLuminanceSlider, &QSlider::valueChanged, this, [this](int value) {
        m_hslLuminanceValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(hslLumLabel);
    tab1Layout->addWidget(m_hslLuminanceSlider);
    
    // === COLOR BALANCE WHEELS (DaVinci Resolve-style) ===
    tab1Layout->addSpacing(1);
    QLabel *colorBalanceLabel = new QLabel("⚖️ Color Balance");
    colorBalanceLabel->setAccessibleName("sectionTitle");
    tab1Layout->addWidget(colorBalanceLabel);
    
    // Shadow/Midtone/Highlight color balance
    QHBoxLayout *colorWheelLayout = new QHBoxLayout();
    colorWheelLayout->setSpacing(2);
    m_shadowColorBtn = new QPushButton("🌑 Shadows");
    m_midtoneColorBtn = new QPushButton("🌗 Midtones");
    m_highlightColorBtn = new QPushButton("🌕 Highlights");
    m_shadowColorBtn->setCheckable(true);
    m_midtoneColorBtn->setCheckable(true);
    m_highlightColorBtn->setCheckable(true);
    connect(m_shadowColorBtn, &QPushButton::clicked, this, &Zview::showShadowColorWheel);
    connect(m_midtoneColorBtn, &QPushButton::clicked, this, &Zview::showMidtoneColorWheel);
    connect(m_highlightColorBtn, &QPushButton::clicked, this, &Zview::showHighlightColorWheel);
    colorWheelLayout->addWidget(m_shadowColorBtn);
    colorWheelLayout->addWidget(m_midtoneColorBtn);
    colorWheelLayout->addWidget(m_highlightColorBtn);
    tab1Layout->addLayout(colorWheelLayout);
    
    // === TAB 2 CONTENT - ADVANCED TOOLS ===
    
    // === SPLIT TONING (Lightroom-style) ===
    tab2Layout->addSpacing(1);
    QLabel *splitToneLabel = new QLabel("🎨 Split Toning");
    splitToneLabel->setAccessibleName("sectionTitle");
    tab2Layout->addWidget(splitToneLabel);
    
    // Highlight Tint
    QLabel *highlightTintLabel = new QLabel("Highlight Tint");
    m_highlightTintSlider = new QSlider(Qt::Horizontal);
    m_highlightTintSlider->setRange(0, 360);
    m_highlightTintSlider->setValue(30); // Default orange tint
    connect(m_highlightTintSlider, &QSlider::valueChanged, this, &Zview::adjustHighlightTint);
    tab2Layout->addWidget(highlightTintLabel);
    tab2Layout->addWidget(m_highlightTintSlider);
    
    // Shadow Tint
    QLabel *shadowTintLabel = new QLabel("Shadow Tint");
    m_shadowTintSlider = new QSlider(Qt::Horizontal);
    m_shadowTintSlider->setRange(0, 360);
    m_shadowTintSlider->setValue(240); // Default blue tint
    connect(m_shadowTintSlider, &QSlider::valueChanged, this, &Zview::adjustShadowTint);
    tab2Layout->addWidget(shadowTintLabel);
    tab2Layout->addWidget(m_shadowTintSlider);
    
    // Split Tone Balance
    QLabel *splitBalanceLabel = new QLabel("Balance");
    m_splitToneBalanceSlider = new QSlider(Qt::Horizontal);
    m_splitToneBalanceSlider->setRange(-100, 100);
    m_splitToneBalanceSlider->setValue(0);
    connect(m_splitToneBalanceSlider, &QSlider::valueChanged, this, &Zview::adjustSplitToneBalance);
    tab2Layout->addWidget(splitBalanceLabel);
    tab2Layout->addWidget(m_splitToneBalanceSlider);
    
    // === PROFESSIONAL ADJUSTMENTS ===
    tab1Layout->addSpacing(1);
    QLabel *proAdjustLabel = new QLabel("⚙️ Pro Adjustments");
    proAdjustLabel->setAccessibleName("sectionTitle");
    tab1Layout->addWidget(proAdjustLabel);
    
    // Gamma (keeping existing)
    QLabel *gammaLabel = new QLabel("Gamma");
    m_gammaSlider = new QSlider(Qt::Horizontal);
    m_gammaSlider->setRange(-100, 100);
    m_gammaSlider->setValue(0);
    connect(m_gammaSlider, &QSlider::valueChanged, this, [this](int value) {
        m_gammaValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(gammaLabel);
    tab1Layout->addWidget(m_gammaSlider);
    
    // Exposure (keeping existing)
    QLabel *exposureLabel = new QLabel("Exposure");
    m_exposureSlider = new QSlider(Qt::Horizontal);
    m_exposureSlider->setRange(-100, 100);
    m_exposureSlider->setValue(0);
    connect(m_exposureSlider, &QSlider::valueChanged, this, [this](int value) {
        m_exposureValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(exposureLabel);
    tab1Layout->addWidget(m_exposureSlider);
    
    // Highlights (keeping existing)
    QLabel *highlightsLabel = new QLabel("Highlights");
    m_highlightsSlider = new QSlider(Qt::Horizontal);
    m_highlightsSlider->setRange(-100, 100);
    m_highlightsSlider->setValue(0);
    connect(m_highlightsSlider, &QSlider::valueChanged, this, [this](int value) {
        m_highlightsValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(highlightsLabel);
    tab1Layout->addWidget(m_highlightsSlider);
    
    // Shadows (keeping existing)
    QLabel *shadowsLabel = new QLabel("Shadows");
    m_shadowsSlider = new QSlider(Qt::Horizontal);
    m_shadowsSlider->setRange(-100, 100);
    m_shadowsSlider->setValue(0);
    connect(m_shadowsSlider, &QSlider::valueChanged, this, [this](int value) {
        m_shadowsValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(shadowsLabel);
    tab1Layout->addWidget(m_shadowsSlider);
    
    // Vibrance (enhanced)
    QLabel *vibranceLabel = new QLabel("Vibrance");
    m_vibranceSlider = new QSlider(Qt::Horizontal);
    m_vibranceSlider->setRange(-100, 100);
    m_vibranceSlider->setValue(0);
    connect(m_vibranceSlider, &QSlider::valueChanged, this, [this](int value) {
        m_vibranceValue = value;
        scheduleUpdateDebounced();
    });
    tab1Layout->addWidget(vibranceLabel);
    tab1Layout->addWidget(m_vibranceSlider);
    
    // Clarity (new professional tool)
    QLabel *clarityLabel = new QLabel("Clarity");
    m_claritySlider = new QSlider(Qt::Horizontal);
    m_claritySlider->setRange(-100, 100);
    m_claritySlider->setValue(0);
    connect(m_claritySlider, &QSlider::valueChanged, this, &Zview::adjustClarity);
    tab1Layout->addWidget(clarityLabel);
    tab1Layout->addWidget(m_claritySlider);
    
    // Dehaze (new professional tool)
    QLabel *dehazeLabel = new QLabel("Dehaze");
    m_dehazeSlider = new QSlider(Qt::Horizontal);
    m_dehazeSlider->setRange(-100, 100);
    m_dehazeSlider->setValue(0);
    connect(m_dehazeSlider, &QSlider::valueChanged, this, &Zview::adjustDehaze);
    tab1Layout->addWidget(dehazeLabel);
    tab1Layout->addWidget(m_dehazeSlider);
    
    // === WHITE BALANCE ===
    tab1Layout->addSpacing(1);
    QLabel *tempLabel = new QLabel("🌡️ White Balance");
    tempLabel->setAccessibleName("sectionTitle");
    tab1Layout->addWidget(tempLabel);
    
    // Temperature (keeping existing)
    QLabel *temperatureLabel = new QLabel("Temperature");
    m_temperatureSlider = new QSlider(Qt::Horizontal);
    m_temperatureSlider->setRange(-100, 100);
    m_temperatureSlider->setValue(0);
    connect(m_temperatureSlider, &QSlider::valueChanged, this, &Zview::adjustTemperature);
    tab1Layout->addWidget(temperatureLabel);
    tab1Layout->addWidget(m_temperatureSlider);
    
    // Tint (keeping existing)
    QLabel *tintLabel = new QLabel("Tint");
    m_tintSlider = new QSlider(Qt::Horizontal);
    m_tintSlider->setRange(-100, 100);
    m_tintSlider->setValue(0);
    connect(m_tintSlider, &QSlider::valueChanged, this, &Zview::adjustTint);
    tab1Layout->addWidget(tintLabel);
    tab1Layout->addWidget(m_tintSlider);
    
    // Auto White Balance button
    m_autoWBBtn = new QPushButton("🎯 Auto WB");
    m_autoWBBtn->setCheckable(true);
    connect(m_autoWBBtn, &QPushButton::clicked, this, &Zview::autoWhiteBalance);
    tab1Layout->addWidget(m_autoWBBtn);
    
    // === CREATIVE GRADING ===
    tab2Layout->addSpacing(1);
    QLabel *creativeLabel = new QLabel("✨ Creative Grading");
    creativeLabel->setAccessibleName("sectionTitle");
    tab2Layout->addWidget(creativeLabel);
    
    // Film emulation buttons
    QHBoxLayout *filmLayout1 = new QHBoxLayout();
    filmLayout1->setSpacing(1);
    m_filmKodakBtn = new QPushButton("Kodak");
    m_filmFujiBtn = new QPushButton("Fuji");
    m_filmIlfordBtn = new QPushButton("Ilford");
    m_filmKodakBtn->setCheckable(true);
    m_filmFujiBtn->setCheckable(true);
    m_filmIlfordBtn->setCheckable(true);
    connect(m_filmKodakBtn, &QPushButton::clicked, this, &Zview::applyKodakEmulation);
    connect(m_filmFujiBtn, &QPushButton::clicked, this, &Zview::applyFujiEmulation);
    connect(m_filmIlfordBtn, &QPushButton::clicked, this, &Zview::applyIlfordEmulation);
    filmLayout1->addWidget(m_filmKodakBtn);
    filmLayout1->addWidget(m_filmFujiBtn);
    filmLayout1->addWidget(m_filmIlfordBtn);
    tab2Layout->addLayout(filmLayout1);
    
    // Cinema grading buttons
    QHBoxLayout *cinemaLayout = new QHBoxLayout();
    cinemaLayout->setSpacing(1);
    m_cinemaLogBtn = new QPushButton("Log");
    m_cinemaRec709Btn = new QPushButton("Rec.709");
    m_cinemaBleachBtn = new QPushButton("Bleach");
    m_cinemaLogBtn->setCheckable(true);
    m_cinemaRec709Btn->setCheckable(true);
    m_cinemaBleachBtn->setCheckable(true);
    connect(m_cinemaLogBtn, &QPushButton::clicked, this, &Zview::applyCinemaLog);
    connect(m_cinemaRec709Btn, &QPushButton::clicked, this, &Zview::applyCinemaRec709);
    connect(m_cinemaBleachBtn, &QPushButton::clicked, this, &Zview::applyBleachBypass);
    cinemaLayout->addWidget(m_cinemaLogBtn);
    cinemaLayout->addWidget(m_cinemaRec709Btn);
    cinemaLayout->addWidget(m_cinemaBleachBtn);
    tab2Layout->addLayout(cinemaLayout);
    
    // === LEGACY TOOLS (keeping some existing) ===
    tab2Layout->addSpacing(1);
    QLabel *legacyLabel = new QLabel("🔧 Legacy Tools");
    legacyLabel->setAccessibleName("sectionTitle");
    tab2Layout->addWidget(legacyLabel);
    
    // Keep some existing creative filters in compact form
    QHBoxLayout *legacyLayout1 = new QHBoxLayout();
    legacyLayout1->setSpacing(1);
    m_vintageBtn = new QPushButton("📷 Vintage");
    m_lomographyBtn = new QPushButton("🎞️ Lomo");
    m_vintageBtn->setCheckable(true);
    m_lomographyBtn->setCheckable(true);
    connect(m_vintageBtn, &QPushButton::clicked, this, &Zview::applyVintage);
    connect(m_lomographyBtn, &QPushButton::clicked, this, &Zview::applyLomography);
    legacyLayout1->addWidget(m_vintageBtn);
    legacyLayout1->addWidget(m_lomographyBtn);
    tab2Layout->addLayout(legacyLayout1);
    
    QHBoxLayout *legacyLayout2 = new QHBoxLayout();
    legacyLayout2->setSpacing(1);
    m_polaroidBtn = new QPushButton("📸 Polaroid");
    m_crossProcessBtn = new QPushButton("🌈 Cross");
    m_polaroidBtn->setCheckable(true);
    m_crossProcessBtn->setCheckable(true);
    connect(m_polaroidBtn, &QPushButton::clicked, this, &Zview::applyPolaroid);
    connect(m_crossProcessBtn, &QPushButton::clicked, this, &Zview::applyCrossProcess);
    legacyLayout2->addWidget(m_polaroidBtn);
    legacyLayout2->addWidget(m_crossProcessBtn);
    tab2Layout->addLayout(legacyLayout2);
    
    // === PROFESSIONAL MASKING ===
    tab2Layout->addSpacing(1);
    QLabel *maskingLabel = new QLabel("🎭 Pro Masking");
    maskingLabel->setAccessibleName("sectionTitle");
    tab2Layout->addWidget(maskingLabel);
    
    QHBoxLayout *maskingLayout = new QHBoxLayout();
    maskingLayout->setSpacing(1);
    m_luminosityMaskBtn = new QPushButton("💡 Luma");
    m_colorMaskBtn = new QPushButton("🌈 Color");
    m_rangeMaskBtn = new QPushButton("📊 Range");
    m_luminosityMaskBtn->setCheckable(true);
    m_colorMaskBtn->setCheckable(true);
    m_rangeMaskBtn->setCheckable(true);
    connect(m_luminosityMaskBtn, &QPushButton::clicked, this, &Zview::createLuminosityMask);
    connect(m_colorMaskBtn, &QPushButton::clicked, this, &Zview::createColorMask);
    connect(m_rangeMaskBtn, &QPushButton::clicked, this, &Zview::createRangeMask);
    maskingLayout->addWidget(m_luminosityMaskBtn);
    maskingLayout->addWidget(m_colorMaskBtn);
    maskingLayout->addWidget(m_rangeMaskBtn);
    tab2Layout->addLayout(maskingLayout);
    
    tab2Layout->addSpacing(2);
    
    // Position and show the left toolbox
    repositionLeftImageEditorToolbox();
    m_leftImageEditorToolbox->setVisible(true);
}

void Zview::switchLeftToolboxTab(int tabNumber)
{
    m_currentLeftTab = tabNumber;
    
    // Update tab button states
    m_leftTab1Btn->setChecked(tabNumber == 1);
    m_leftTab2Btn->setChecked(tabNumber == 2);
    
    // Show/hide appropriate tab content
    if (m_leftToolboxTab1) m_leftToolboxTab1->setVisible(tabNumber == 1);
    if (m_leftToolboxTab2) m_leftToolboxTab2->setVisible(tabNumber == 2);
}

void Zview::repositionLeftImageEditorToolbox()
{
    if (!m_leftImageEditorToolbox) return;
    
    const int toolboxWidth = 220;  // Same width as right toolbox
    const int toolboxHeight = 920; // Extended height for more professional tools
    const int margin = 20;         // Standard margin
    
    // Position on the left side
    int toolboxX = margin;
    int toolboxY = margin;
    
    m_leftImageEditorToolbox->setGeometry(toolboxX, toolboxY, toolboxWidth, toolboxHeight);
}

// Image editing operations
void Zview::adjustBrightness(int value)
{
    m_brightnessValue = value;
    updateImagePreview();
}

void Zview::adjustContrast(int value)
{
    m_contrastValue = value;
    updateImagePreview();
}

void Zview::adjustSaturation(int value)
{
    m_saturationValue = value;
    updateImagePreview();
}

void Zview::adjustHue(int value)
{
    m_hueValue = value;
    updateImagePreview();
}

void Zview::rotateImage(int degrees)
{
    m_rotationAngle = (m_rotationAngle + degrees) % 360;
    updateImagePreview();
}

void Zview::flipImageHorizontal()
{
    m_isFlippedHorizontal = !m_isFlippedHorizontal;
    updateImagePreview();
}

void Zview::flipImageVertical()
{
    m_isFlippedVertical = !m_isFlippedVertical;
    updateImagePreview();
}

void Zview::applyBlur(int radius)
{
    m_blurValue = radius;
    updateImagePreview();
}

void Zview::applySharpen(int strength)
{
    m_sharpenValue = strength;
    updateImagePreview();
}

void Zview::applyEmboss()
{
    m_embossApplied = !m_embossApplied;
    updateImagePreview();
}

void Zview::applyGrayscale()
{
    m_grayscaleApplied = !m_grayscaleApplied;
    updateImagePreview();
}

void Zview::applySepia()
{
    m_sepiaApplied = !m_sepiaApplied;
    updateImagePreview();
}

void Zview::resetImageEdits()
{
    qDebug() << "=== RESET BUTTON CLICKED ===";
    // Reset all right toolbox values
    m_brightnessValue = 0;
    m_contrastValue = 0;
    m_saturationValue = 0;
    m_hueValue = 0;
    m_blurValue = 0;
    m_sharpenValue = 0;
    m_rotationAngle = 0;
    m_isFlippedHorizontal = false;
    m_isFlippedVertical = false;
    m_grayscaleApplied = false;
    m_sepiaApplied = false;
    m_embossApplied = false;
    
    // Reset all left toolbox values
    m_gammaValue = 0;
    m_exposureValue = 0;
    m_highlightsValue = 0;
    m_shadowsValue = 0;
    m_vibranceValue = 0;
    m_temperatureValue = 0;
    m_tintValue = 0;
    m_noiseReductionValue = 0;
    
    // Reset HSL Color Editor values
    m_hslHueValue = 0;
    m_hslSaturationValue = 0;
    m_hslLuminanceValue = 0;
    m_currentHSLChannel = 0; // Reset to "All" channel
    
    // Reset Color Balance values
    m_shadowColorBalance = QColor(128, 128, 128);
    m_midtoneColorBalance = QColor(128, 128, 128);
    m_highlightColorBalance = QColor(128, 128, 128);
    
    // Reset Split Toning values
    m_highlightTintValue = 0;
    m_shadowTintValue = 0;
    m_splitToneBalanceValue = 0;
    
    // Reset Professional Enhancement values
    m_clarityValue = 0;
    m_dehazeValue = 0;
    
    // Reset all left toolbox applied filters
    m_vintageApplied = false;
    m_blackWhiteApplied = false;
    m_crossProcessApplied = false;
    m_lomographyApplied = false;
    m_polaroidApplied = false;
    m_invertApplied = false;
    m_posterizeApplied = false;
    m_sobelEdgeApplied = false;
    
    // Reset Film Emulation filters
    m_kodakEmulationApplied = false;
    m_fujiEmulationApplied = false;
    m_ilfordEmulationApplied = false;
    
    // Reset Cinema Grading filters
    m_cinemaLogApplied = false;
    m_cinemaRec709Applied = false;
    m_bleachBypassApplied = false;
    
    // Reset Professional Masking filters
    m_luminosityMaskActive = false;
    m_colorMaskActive = false;
    m_rangeMaskActive = false;
    
    // Reset right toolbox sliders
    if (m_brightnessSlider) m_brightnessSlider->setValue(0);
    if (m_contrastSlider) m_contrastSlider->setValue(0);
    if (m_saturationSlider) m_saturationSlider->setValue(0);
    if (m_hueSlider) m_hueSlider->setValue(0);
    if (m_blurSlider) m_blurSlider->setValue(0);
    if (m_sharpenSlider) m_sharpenSlider->setValue(0);
    
    // Reset left toolbox sliders
    if (m_gammaSlider) m_gammaSlider->setValue(0);
    if (m_exposureSlider) m_exposureSlider->setValue(0);
    if (m_highlightsSlider) m_highlightsSlider->setValue(0);
    if (m_shadowsSlider) m_shadowsSlider->setValue(0);
    if (m_vibranceSlider) m_vibranceSlider->setValue(0);
    if (m_temperatureSlider) m_temperatureSlider->setValue(0);
    if (m_tintSlider) m_tintSlider->setValue(0);
    if (m_noiseReductionSlider) m_noiseReductionSlider->setValue(0);
    
    // Reset HSL Color Editor sliders
    if (m_hslHueSlider) m_hslHueSlider->setValue(0);
    if (m_hslSaturationSlider) m_hslSaturationSlider->setValue(0);
    if (m_hslLuminanceSlider) m_hslLuminanceSlider->setValue(0);
    
    // Reset HSL channel buttons to "All"
    if (m_hslAllBtn) m_hslAllBtn->setChecked(true);
    if (m_hslRedBtn) m_hslRedBtn->setChecked(false);
    if (m_hslOrangeBtn) m_hslOrangeBtn->setChecked(false);
    if (m_hslYellowBtn) m_hslYellowBtn->setChecked(false);
    if (m_hslGreenBtn) m_hslGreenBtn->setChecked(false);
    if (m_hslAquaBtn) m_hslAquaBtn->setChecked(false);
    if (m_hslBlueBtn) m_hslBlueBtn->setChecked(false);
    if (m_hslPurpleBtn) m_hslPurpleBtn->setChecked(false);
    if (m_hslMagentaBtn) m_hslMagentaBtn->setChecked(false);
    
    // Reset Split Toning sliders
    if (m_highlightTintSlider) m_highlightTintSlider->setValue(0);
    if (m_shadowTintSlider) m_shadowTintSlider->setValue(0);
    if (m_splitToneBalanceSlider) m_splitToneBalanceSlider->setValue(0);
    
    // Reset Professional Enhancement sliders
    if (m_claritySlider) m_claritySlider->setValue(0);
    if (m_dehazeSlider) m_dehazeSlider->setValue(0);
    
    // Reset left toolbox buttons (uncheck them)
    if (m_vintageBtn) m_vintageBtn->setChecked(false);
    if (m_blackWhiteBtn) m_blackWhiteBtn->setChecked(false);
    if (m_crossProcessBtn) m_crossProcessBtn->setChecked(false);
    if (m_lomographyBtn) m_lomographyBtn->setChecked(false);
    if (m_polaroidBtn) m_polaroidBtn->setChecked(false);
    if (m_invertBtn) m_invertBtn->setChecked(false);
    if (m_posterizeBtn) m_posterizeBtn->setChecked(false);
    if (m_sobelEdgeBtn) m_sobelEdgeBtn->setChecked(false);
    
    // Reset Film Emulation buttons
    if (m_filmKodakBtn) m_filmKodakBtn->setChecked(false);
    if (m_filmFujiBtn) m_filmFujiBtn->setChecked(false);
    if (m_filmIlfordBtn) m_filmIlfordBtn->setChecked(false);
    
    // Reset Cinema Grading buttons
    if (m_cinemaLogBtn) m_cinemaLogBtn->setChecked(false);
    if (m_cinemaRec709Btn) m_cinemaRec709Btn->setChecked(false);
    if (m_cinemaBleachBtn) m_cinemaBleachBtn->setChecked(false);
    
    // Reset Professional Masking buttons
    if (m_luminosityMaskBtn) m_luminosityMaskBtn->setChecked(false);
    if (m_colorMaskBtn) m_colorMaskBtn->setChecked(false);
    if (m_rangeMaskBtn) m_rangeMaskBtn->setChecked(false);
    
    // Reset right toolbox buttons (uncheck them)
    if (m_grayscaleBtn) m_grayscaleBtn->setChecked(false);
    if (m_sepiaBtn) m_sepiaBtn->setChecked(false);
    if (m_embossBtn) m_embossBtn->setChecked(false);
    
    // Directly restore true original image without any processing
    if (!m_trueOriginalImageData.isNull()) {
        qDebug() << "RESET: Restoring from raw image data - size:" << m_trueOriginalImageData.size() << "format:" << m_trueOriginalImageData.format();
        
        // Create fresh pixmap from raw image data to avoid any cached processing
        QPixmap freshOriginal = QPixmap::fromImage(m_trueOriginalImageData);
        qDebug() << "RESET: Created fresh pixmap - size:" << freshOriginal.size() << "isNull:" << freshOriginal.isNull();
        
        m_previewImage = freshOriginal;
        m_currentImage = freshOriginal;
        m_originalImage = freshOriginal; // Reset working original too
        m_trueOriginalImage = freshOriginal; // Update pixmap version too
        
        // Force complete texture recreation without any optimization
        if (m_texture) {
            delete m_texture;
            m_texture = nullptr;
        }
        m_textureNeedsUpdate = true;
        
        qDebug() << "RESET: Texture deleted, triggering update";
        update(); // Trigger repaint
    } else {
        qDebug() << "RESET: ERROR - m_trueOriginalImageData is null!";
    }
}

void Zview::applyImageEdits()
{
    // Apply current preview to the main image
    m_currentImage = m_previewImage;
    m_originalImage = m_previewImage;
    m_textureNeedsUpdate = true;
    
    // Reset edit values since they're now applied
    resetImageEdits();
    
    update();
    qDebug() << "Image edits applied";
}

void Zview::cancelImageEdits()
{
    hideImageEditor();
}

void Zview::scheduleUpdateDebounced()
{
    // ULTRA-FAST REAL-TIME: Immediate update without debouncing for live preview
    updateImagePreview();
}

void Zview::updateImagePreview()
{
    if (m_originalImage.isNull()) return;
    
    // Start performance timing for professional metrics
    m_renderTimer.start();
    
    // Professional Performance Optimization: Check cache first (like Photoshop)
    // Include ALL adjustment parameters in cache key including ALL TOOLS AND FILTERS for live updates
    QString cacheKey = QString("preview_%1_%2_%3_%4_%5_%6_%7_%8_%9_%10_%11_%12_%13_%14_%15_%16_%17_%18_%19_%20_%21_%22_%23_%24_%25_%26_%27_%28_%29_%30_%31_%32_%33_%34_%35_%36_%37_%38_%39_%40_%41_%42_%43_%44")
                      .arg(m_brightnessValue)
                      .arg(m_contrastValue)
                      .arg(m_saturationValue)
                      .arg(m_hueValue)
                      .arg(m_blurValue)
                      .arg(m_sharpenValue)
                      .arg(m_noiseReductionValue)
                      .arg(m_gammaValue)
                      .arg(m_exposureValue)
                      .arg(m_highlightsValue)
                      .arg(m_shadowsValue)
                      .arg(m_vibranceValue)
                      .arg(m_temperatureValue)
                      .arg(m_tintValue)
                      .arg(m_hslHueValue)
                      .arg(m_hslSaturationValue)
                      .arg(m_hslLuminanceValue)
                      .arg(m_currentHSLChannel)
                      .arg(QString("%1_%2_%3").arg(m_shadowColorBalance.red()).arg(m_shadowColorBalance.green()).arg(m_shadowColorBalance.blue()))
                      .arg(QString("%1_%2_%3").arg(m_midtoneColorBalance.red()).arg(m_midtoneColorBalance.green()).arg(m_midtoneColorBalance.blue()))
                      .arg(QString("%1_%2_%3").arg(m_highlightColorBalance.red()).arg(m_highlightColorBalance.green()).arg(m_highlightColorBalance.blue()))
                      .arg(m_clarityValue)
                      .arg(m_dehazeValue)
                      .arg(m_highlightTintValue)
                      .arg(m_shadowTintValue)
                      .arg(m_splitToneBalanceValue)
                      .arg(m_kodakEmulationApplied ? 1 : 0)
                      .arg(m_fujiEmulationApplied ? 1 : 0)
                      .arg(m_ilfordEmulationApplied ? 1 : 0)
                      .arg(m_cinemaLogApplied ? 1 : 0)
                      .arg(m_cinemaRec709Applied ? 1 : 0)
                      .arg(m_bleachBypassApplied ? 1 : 0)
                      .arg(m_luminosityMaskActive ? 1 : 0)
                      .arg(m_colorMaskActive ? 1 : 0)
                      .arg(m_rangeMaskActive ? 1 : 0)
                      .arg(m_grayscaleApplied ? 1 : 0)
                      .arg(m_sepiaApplied ? 1 : 0)
                      .arg(m_embossApplied ? 1 : 0)
                      .arg(m_vintageApplied ? 1 : 0)
                      .arg(m_blackWhiteApplied ? 1 : 0)
                      .arg(m_crossProcessApplied ? 1 : 0)
                      .arg(m_lomographyApplied ? 1 : 0)
                      .arg(m_polaroidApplied ? 1 : 0)
                      .arg(m_invertApplied ? 1 : 0)
                      .arg(m_posterizeApplied ? 1 : 0)
                      .arg(m_sobelEdgeApplied ? 1 : 0);
    
    QImage cachedResult;
    if (getCachedResult(cacheKey, cachedResult)) {
        // Use cached result for instant preview (professional performance)
        m_previewImage = QPixmap::fromImage(cachedResult);
        m_currentImage = m_previewImage;
        
        // CRITICAL FIX: Force texture update for OpenGL rendering
        m_textureNeedsUpdate = true;
        
        // Force immediate display update
        update();
        
        // Update performance metrics
        m_performanceMetrics.renderTime = m_renderTimer.elapsed();
        return;
    }
    
    // HIGH-QUALITY REAL-TIME: Use optimized preview size with smooth scaling
    QSize previewSize = m_originalImage.size();
    
    // Scale down large images for real-time performance while maintaining quality
    if (previewSize.width() > 1920 || previewSize.height() > 1080) {
        previewSize.scale(1920, 1080, Qt::KeepAspectRatio);  // Increased from 1280x720 for better quality
    }
    
    // Use performance-optimized scaling for real-time updates
    QImage workingImage;
    if (previewSize != m_originalImage.size()) {
        // Use fast scaling for interactive curve editing
        workingImage = getOptimizedScaling(m_originalImage.toImage(), previewSize, true);
    } else {
        workingImage = m_originalImage.toImage();
    }
    
    // Apply brightness
    if (m_brightnessValue != 0) {
        workingImage = applyImageFilter(workingImage, "brightness", m_brightnessValue);
    }
    
    // Apply contrast
    if (m_contrastValue != 0) {
        workingImage = applyImageFilter(workingImage, "contrast", m_contrastValue);
    }
    
    // Apply saturation
    if (m_saturationValue != 0) {
        workingImage = applyImageFilter(workingImage, "saturation", m_saturationValue);
    }
    
    // Apply hue
    if (m_hueValue != 0) {
        workingImage = applyImageFilter(workingImage, "hue", m_hueValue);
    }
    
    // Apply blur
    if (m_blurValue > 0) {
        workingImage = applyImageFilter(workingImage, "blur", m_blurValue);
    }
    
    // Apply sharpen
    if (m_sharpenValue > 0) {
        workingImage = applyImageFilter(workingImage, "sharpen", m_sharpenValue);
    }
    
    // Apply advanced adjustments (left toolbox)
    if (m_gammaValue != 0) {
        workingImage = applyImageFilter(workingImage, "gamma", m_gammaValue);
    }
    
    if (m_exposureValue != 0) {
        workingImage = applyImageFilter(workingImage, "exposure", m_exposureValue);
    }
    
    if (m_highlightsValue != 0) {
        workingImage = applyImageFilter(workingImage, "highlights", m_highlightsValue);
    }
    
    if (m_shadowsValue != 0) {
        workingImage = applyImageFilter(workingImage, "shadows", m_shadowsValue);
    }
    
    if (m_vibranceValue != 0) {
        workingImage = applyImageFilter(workingImage, "vibrance", m_vibranceValue);
    }
    
    if (m_temperatureValue != 0) {
        workingImage = applyImageFilter(workingImage, "temperature", m_temperatureValue);
    }
    
    if (m_tintValue != 0) {
        workingImage = applyImageFilter(workingImage, "tint", m_tintValue);
    }
    
    // Apply professional noise reduction
    if (m_noiseReductionValue > 0) {
        workingImage = applyImageFilter(workingImage, "noisereduction", m_noiseReductionValue);
    }
    
    // Apply basic filters
    if (m_grayscaleApplied) {
        workingImage = applyImageFilter(workingImage, "grayscale");
    }
    
    if (m_sepiaApplied) {
        workingImage = applyImageFilter(workingImage, "sepia");
    }
    
    if (m_embossApplied) {
        workingImage = applyImageFilter(workingImage, "emboss");
    }
    
    // Apply advanced creative filters
    if (m_vintageApplied) {
        workingImage = applyImageFilter(workingImage, "vintage");
    }
    
    if (m_blackWhiteApplied) {
        workingImage = applyImageFilter(workingImage, "blackwhite");
    }
    
    if (m_crossProcessApplied) {
        workingImage = applyImageFilter(workingImage, "crossprocess");
    }
    
    if (m_lomographyApplied) {
        workingImage = applyImageFilter(workingImage, "lomography");
    }
    
    if (m_polaroidApplied) {
        workingImage = applyImageFilter(workingImage, "polaroid");
    }
    
    if (m_invertApplied) {
        workingImage = applyImageFilter(workingImage, "invert");
    }
    
    if (m_posterizeApplied) {
        workingImage = applyImageFilter(workingImage, "posterize");
    }
    
    if (m_sobelEdgeApplied) {
        workingImage = applyImageFilter(workingImage, "sobeledge");
    }
    
    // === APPLY PROFESSIONAL EFFECTS ===
    
    // Apply HSL adjustments
    if (m_hslHueValue != 0 || m_hslSaturationValue != 0 || m_hslLuminanceValue != 0) {
        workingImage = applyImageFilter(workingImage, "hsl", QVariantList() << m_hslHueValue << m_hslSaturationValue << m_hslLuminanceValue << m_currentHSLChannel);
    }
    
    // Apply Color Balance
    if (m_shadowColorBalance != QColor(128, 128, 128) || 
        m_midtoneColorBalance != QColor(128, 128, 128) || 
        m_highlightColorBalance != QColor(128, 128, 128)) {
        workingImage = applyImageFilter(workingImage, "colorbalance", QVariantList() << m_shadowColorBalance << m_midtoneColorBalance << m_highlightColorBalance);
    }
    
    // Apply Split Toning
    if (m_highlightTintValue != 0 || m_shadowTintValue != 0 || m_splitToneBalanceValue != 0) {
        workingImage = applyImageFilter(workingImage, "splittoning", QVariantList() << m_highlightTintValue << m_shadowTintValue << m_splitToneBalanceValue);
    }
    
    // Apply Professional Enhancements
    if (m_clarityValue != 0) {
        workingImage = applyImageFilter(workingImage, "clarity", m_clarityValue);
    }
    
    if (m_dehazeValue != 0) {
        workingImage = applyImageFilter(workingImage, "dehaze", m_dehazeValue);
    }
    
    // Apply Film Emulations
    if (m_kodakEmulationApplied) {
        workingImage = applyImageFilter(workingImage, "kodak");
    }
    
    if (m_fujiEmulationApplied) {
        workingImage = applyImageFilter(workingImage, "fuji");
    }
    
    if (m_ilfordEmulationApplied) {
        workingImage = applyImageFilter(workingImage, "ilford");
    }
    
    // Apply Cinema Grades
    if (m_cinemaLogApplied) {
        workingImage = applyImageFilter(workingImage, "cinemalog");
    }
    
    if (m_cinemaRec709Applied) {
        workingImage = applyImageFilter(workingImage, "cinemar709");
    }
    
    if (m_bleachBypassApplied) {
        workingImage = applyImageFilter(workingImage, "bleachbypass");
    }
    
    // Apply Professional Masking Effects
    if (m_luminosityMaskActive) {
        workingImage = applyImageFilter(workingImage, "luminositymask");
    }
    
    if (m_colorMaskActive) {
        workingImage = applyImageFilter(workingImage, "colormask");
    }
    
    if (m_rangeMaskActive) {
        workingImage = applyImageFilter(workingImage, "rangemask");
    }
    
    // Apply transformations
    if (m_rotationAngle != 0) {
        QTransform transform;
        transform.rotate(m_rotationAngle);
        workingImage = workingImage.transformed(transform, Qt::SmoothTransformation);
    }
    
    if (m_isFlippedHorizontal) {
        workingImage = workingImage.mirrored(true, false);
    }
    
    if (m_isFlippedVertical) {
        workingImage = workingImage.mirrored(false, true);
    }
    
    m_previewImage = QPixmap::fromImage(workingImage);
    m_currentImage = m_previewImage;
    
    // CRITICAL FIX: Force texture update for OpenGL rendering
    m_textureNeedsUpdate = true;
    
    // Professional Performance: Cache result for instant future access (like Photoshop)
    m_performanceCache[cacheKey] = workingImage;
    
    // Update performance metrics
    m_performanceMetrics.renderTime = m_renderTimer.elapsed();
    
    // Force immediate display update
    update();
}

QImage Zview::applyImageFilter(const QImage &image, const QString &filterType, const QVariant &parameter)
{
    QImage result = image;
    
    if (filterType == "brightness") {
        int brightness = parameter.toInt();
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qBound(0, qRed(pixel) + brightness, 255);
                int g = qBound(0, qGreen(pixel) + brightness, 255);
                int b = qBound(0, qBlue(pixel) + brightness, 255);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "contrast") {
        double contrast = 1.0 + (parameter.toInt() / 100.0);
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qBound(0, (int)((qRed(pixel) - 128) * contrast + 128), 255);
                int g = qBound(0, (int)((qGreen(pixel) - 128) * contrast + 128), 255);
                int b = qBound(0, (int)((qBlue(pixel) - 128) * contrast + 128), 255);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "saturation") {
        double saturation = 1.0 + (parameter.toInt() / 100.0);
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                s = qBound(0, (int)(s * saturation), 255);
                color.setHsv(h, s, v);
                result.setPixel(x, y, color.rgb());
            }
        }
    }
    else if (filterType == "hue") {
        int hueShift = parameter.toInt();
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                if (h >= 0) { // -1 means achromatic
                    h = (h + hueShift) % 360;
                    if (h < 0) h += 360;
                    color.setHsv(h, s, v);
                    result.setPixel(x, y, color.rgb());
                }
            }
        }
    }
    else if (filterType == "blur") {
        int radius = parameter.toInt();
        if (radius > 0) {
            // Use professional Gaussian noise reduction for better quality
            result = applyGaussianNoiseReduction(result, radius * 0.5, radius * 2 + 1);
        }
    }
    else if (filterType == "noisereduction" || filterType == "denoise") {
        int strength = parameter.toInt();
        result = applyAdvancedNoiseReduction(result, strength);
    }
    else if (filterType == "removeartifacts") {
        int threshold = parameter.toInt();
        if (threshold <= 0) threshold = 30;
        result = removeColorArtifacts(result, threshold);
    }
    else if (filterType == "medianfilter") {
        int kernelSize = parameter.toInt();
        if (kernelSize <= 0) kernelSize = 3;
        result = applyMedianFilter(result, kernelSize);
    }
    else if (filterType == "bilateral") {
        QVariantList params = parameter.toList();
        int d = params.size() > 0 ? params[0].toInt() : 9;
        double sigmaColor = params.size() > 1 ? params[1].toDouble() : 75.0;
        double sigmaSpace = params.size() > 2 ? params[2].toDouble() : 75.0;
        result = applyBilateralFilter(result, d, sigmaColor, sigmaSpace);
    }
    else if (filterType == "enhancequality") {
        result = enhanceImageQuality(result);
    }
    else if (filterType == "correctcolorcasting") {
        result = correctColorCasting(result);
    }
    else if (filterType == "removecompression") {
        result = removeCompressionArtifacts(result);
    }
    else if (filterType == "sharpen") {
        int strength = parameter.toInt();
        if (strength > 0) {
            double amount = strength / 100.0; // Convert to 0-1 range
            result = applyAdaptiveSharpening(result, amount);
        }
    }
    else if (filterType == "grayscale") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int gray = qGray(pixel);
                result.setPixel(x, y, qRgb(gray, gray, gray));
            }
        }
    }
    else if (filterType == "sepia") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                int tr = qMin(255, (int)(0.393 * r + 0.769 * g + 0.189 * b));
                int tg = qMin(255, (int)(0.349 * r + 0.686 * g + 0.168 * b));
                int tb = qMin(255, (int)(0.272 * r + 0.534 * g + 0.131 * b));
                
                result.setPixel(x, y, qRgb(tr, tg, tb));
            }
        }
    }
    else if (filterType == "emboss") {
        QImage embossed(result.size(), result.format());
        for (int y = 1; y < result.height() - 1; ++y) {
            for (int x = 1; x < result.width() - 1; ++x) {
                QRgb pixel1 = result.pixel(x - 1, y - 1);
                QRgb pixel2 = result.pixel(x + 1, y + 1);
                
                int r = qBound(0, qRed(pixel2) - qRed(pixel1) + 128, 255);
                int g = qBound(0, qGreen(pixel2) - qGreen(pixel1) + 128, 255);
                int b = qBound(0, qBlue(pixel2) - qBlue(pixel1) + 128, 255);
                
                embossed.setPixel(x, y, qRgb(r, g, b));
            }
        }
        result = embossed;
    }
    else if (filterType == "gamma") {
        double gamma = 1.0 + (parameter.toInt() / 100.0);
        gamma = qBound(0.1, gamma, 3.0); // Reasonable gamma range
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qBound(0, (int)(255 * pow(qRed(pixel) / 255.0, 1.0 / gamma)), 255);
                int g = qBound(0, (int)(255 * pow(qGreen(pixel) / 255.0, 1.0 / gamma)), 255);
                int b = qBound(0, (int)(255 * pow(qBlue(pixel) / 255.0, 1.0 / gamma)), 255);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "exposure") {
        double exposure = parameter.toInt() / 50.0; // Convert to stops
        double factor = pow(2.0, exposure);
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qBound(0, (int)(qRed(pixel) * factor), 255);
                int g = qBound(0, (int)(qGreen(pixel) * factor), 255);
                int b = qBound(0, (int)(qBlue(pixel) * factor), 255);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "highlights") {
        double factor = 1.0 + (parameter.toInt() / 100.0);
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Apply to bright areas (luminance > 128)
                int luminance = (r + g + b) / 3;
                if (luminance > 128) {
                    double strength = (luminance - 128) / 127.0;
                    r = qBound(0, (int)(r + (255 - r) * strength * (factor - 1.0)), 255);
                    g = qBound(0, (int)(g + (255 - g) * strength * (factor - 1.0)), 255);
                    b = qBound(0, (int)(b + (255 - b) * strength * (factor - 1.0)), 255);
                }
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "shadows") {
        double factor = 1.0 + (parameter.toInt() / 100.0);
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Apply to dark areas (luminance < 128)
                int luminance = (r + g + b) / 3;
                if (luminance < 128) {
                    double strength = (128 - luminance) / 128.0;
                    r = qBound(0, (int)(r * (1.0 + strength * (factor - 1.0))), 255);
                    g = qBound(0, (int)(g * (1.0 + strength * (factor - 1.0))), 255);
                    b = qBound(0, (int)(b * (1.0 + strength * (factor - 1.0))), 255);
                }
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "vibrance") {
        double vibrance = parameter.toInt() / 100.0;
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                
                if (h >= 0 && s > 0) {
                    // Smooth vibrance with progressive saturation enhancement
                    double currentSat = s / 255.0;
                    double saturationWeight = 1.0 - (currentSat * currentSat); // Quadratic falloff
                    double adjustment = vibrance * saturationWeight * 0.8; // Gentler adjustment
                    
                    double newSat = currentSat + (currentSat * adjustment);
                    int newS = qBound(0, (int)(newSat * 255.0 + 0.5), 255);
                    
                    color.setHsv(h, newS, v);
                    result.setPixel(x, y, color.rgb());
                }
            }
        }
    }
    else if (filterType == "temperature") {
        double temp = parameter.toInt() / 100.0; // -1 to 1
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                double r = qRed(pixel);
                double g = qGreen(pixel);
                double b = qBlue(pixel);
                
                // Smooth temperature adjustment with proper color theory
                if (temp > 0) { // Warmer - enhance reds, reduce blues
                    r = r * (1.0 + temp * 0.15) + temp * 8.0;
                    g = g * (1.0 + temp * 0.05);
                    b = b * (1.0 - temp * 0.12);
                } else { // Cooler - enhance blues, reduce reds
                    r = r * (1.0 + temp * 0.12);
                    g = g * (1.0 - temp * 0.02);
                    b = b * (1.0 - temp * 0.15) - temp * 8.0;
                }
                
                result.setPixel(x, y, qRgb(
                    qBound(0, (int)(r + 0.5), 255),
                    qBound(0, (int)(g + 0.5), 255),
                    qBound(0, (int)(b + 0.5), 255)
                ));
            }
        }
    }
    else if (filterType == "tint") {
        double tint = parameter.toInt() / 100.0; // -1 to 1
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                double r = qRed(pixel);
                double g = qGreen(pixel);
                double b = qBlue(pixel);
                
                // Smooth tint adjustment with proper magenta-green balance
                if (tint > 0) { // More magenta - enhance red/blue, reduce green
                    r = r * (1.0 + tint * 0.08) + tint * 5.0;
                    g = g * (1.0 - tint * 0.06);
                    b = b * (1.0 + tint * 0.08) + tint * 5.0;
                } else { // More green - enhance green, reduce red/blue
                    r = r * (1.0 + tint * 0.06);
                    g = g * (1.0 - tint * 0.08) - tint * 5.0;
                    b = b * (1.0 + tint * 0.06);
                }
                
                result.setPixel(x, y, qRgb(
                    qBound(0, (int)(r + 0.5), 255),
                    qBound(0, (int)(g + 0.5), 255),
                    qBound(0, (int)(b + 0.5), 255)
                ));
            }
        }
    }
    else if (filterType == "vintage") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Vintage color grading: warm highlights, cool shadows, reduced contrast
                int luminance = (r + g + b) / 3;
                double factor = luminance / 255.0;
                
                r = qBound(0, (int)(r * 0.9 + 40 * factor), 255);
                g = qBound(0, (int)(g * 0.85 + 20 * factor), 255);
                b = qBound(0, (int)(b * 0.7 + 10 * factor), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "blackwhite") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                // Professional B&W conversion with channel weighting
                int gray = (int)(0.299 * qRed(pixel) + 0.587 * qGreen(pixel) + 0.114 * qBlue(pixel));
                result.setPixel(x, y, qRgb(gray, gray, gray));
            }
        }
    }
    else if (filterType == "crossprocess") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Cross process effect: inverted color curves
                r = qBound(0, 255 - (255 - r) * (255 - r) / 255, 255);
                g = qBound(0, g * g / 255, 255);
                b = qBound(0, 255 - (255 - b) * (255 - b) / 255, 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "lomography") {
        int centerX = result.width() / 2;
        int centerY = result.height() / 2;
        double maxDistance = sqrt(centerX * centerX + centerY * centerY);
        
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Lomography: vignette + color shift
                double distance = sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                double vignette = 1.0 - (distance / maxDistance) * 0.6;
                
                r = qBound(0, (int)(r * vignette + 20), 255);
                g = qBound(0, (int)(g * vignette), 255);
                b = qBound(0, (int)(b * vignette - 10), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "polaroid") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Polaroid: warm tint, soft contrast, slight overexposure
                r = qBound(0, (int)(r * 1.1 + 15), 255);
                g = qBound(0, (int)(g * 1.05 + 10), 255);
                b = qBound(0, (int)(b * 0.95 + 5), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "invert") {
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = 255 - qRed(pixel);
                int g = 255 - qGreen(pixel);
                int b = 255 - qBlue(pixel);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "posterize") {
        int levels = 8; // Reduce to 8 color levels per channel
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = (qRed(pixel) / (256 / levels)) * (256 / levels);
                int g = (qGreen(pixel) / (256 / levels)) * (256 / levels);
                int b = (qBlue(pixel) / (256 / levels)) * (256 / levels);
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "sobeledge") {
        QImage edges(result.size(), result.format());
        edges.fill(Qt::black);
        
        // Sobel edge detection kernels
        int sobelX[3][3] = {{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}};
        int sobelY[3][3] = {{-1, -2, -1}, {0, 0, 0}, {1, 2, 1}};
        
        for (int y = 1; y < result.height() - 1; ++y) {
            for (int x = 1; x < result.width() - 1; ++x) {
                int gx = 0, gy = 0;
                
                for (int ky = -1; ky <= 1; ++ky) {
                    for (int kx = -1; kx <= 1; ++kx) {
                        QRgb pixel = result.pixel(x + kx, y + ky);
                        int gray = qGray(pixel);
                        gx += gray * sobelX[ky + 1][kx + 1];
                        gy += gray * sobelY[ky + 1][kx + 1];
                    }
                }
                
                int magnitude = qBound(0, (int)sqrt(gx * gx + gy * gy), 255);
                edges.setPixel(x, y, qRgb(magnitude, magnitude, magnitude));
            }
        }
        result = edges;
    }
    // === PROFESSIONAL EFFECTS IMPLEMENTATION ===
    else if (filterType == "hsl") {
        QVariantList params = parameter.toList();
        if (params.size() >= 4) {
            double hueShift = params[0].toDouble();
            double satAdjust = params[1].toDouble();
            double lumAdjust = params[2].toDouble();
            int channel = params[3].toInt();
            
            for (int y = 0; y < result.height(); ++y) {
                for (int x = 0; x < result.width(); ++x) {
                    QRgb pixel = result.pixel(x, y);
                    QColor color(pixel);
                    int h, s, l;
                    color.getHsl(&h, &s, &l);
                    
                    // Calculate smooth channel weight (eliminates hard boundaries)
                    double channelWeight = 1.0;
                    if (channel != 0 && h >= 0) { // Not "All" channel
                        // Smooth channel selection with gradual falloff
                        double hueRange = 360.0 / 8.0; // 8 color channels
                        double channelHue = (channel - 1) * hueRange;
                        double hueDiff = qAbs(h - channelHue);
                        if (hueDiff > 180) hueDiff = 360 - hueDiff;
                        
                        // Smooth falloff instead of hard cutoff
                        if (hueDiff <= hueRange * 0.5) {
                            channelWeight = 1.0;
                        } else if (hueDiff <= hueRange * 0.75) {
                            // Smooth transition zone
                            channelWeight = 1.0 - (hueDiff - hueRange * 0.5) / (hueRange * 0.25);
                        } else {
                            channelWeight = 0.0;
                        }
                    }
                    
                    if (channelWeight > 0.0) {
                        // Apply smooth adjustments with floating-point precision
                        if (h >= 0 && hueShift != 0) {
                            double newHue = h + (hueShift * channelWeight);
                            while (newHue < 0) newHue += 360;
                            while (newHue >= 360) newHue -= 360;
                            h = (int)newHue;
                        }
                        if (satAdjust != 0) {
                            double newSat = s + (satAdjust * 2.55 * channelWeight);
                            s = qBound(0, (int)newSat, 255);
                        }
                        if (lumAdjust != 0) {
                            double newLum = l + (lumAdjust * 2.55 * channelWeight);
                            l = qBound(0, (int)newLum, 255);
                        }
                        color.setHsl(h, s, l);
                        result.setPixel(x, y, color.rgb());
                    }
                }
            }
        }
    }
    else if (filterType == "colorbalance") {
        QVariantList params = parameter.toList();
        if (params.size() >= 3) {
            QColor shadowBalance = params[0].value<QColor>();
            QColor midtoneBalance = params[1].value<QColor>();
            QColor highlightBalance = params[2].value<QColor>();
            
            for (int y = 0; y < result.height(); ++y) {
                for (int x = 0; x < result.width(); ++x) {
                    QRgb pixel = result.pixel(x, y);
                    int r = qRed(pixel);
                    int g = qGreen(pixel);
                    int b = qBlue(pixel);
                    
                    // Calculate luminance to determine shadow/midtone/highlight
                    int luminance = (r + g + b) / 3;
                    double shadowWeight = (luminance < 85) ? (85 - luminance) / 85.0 : 0;
                    double highlightWeight = (luminance > 170) ? (luminance - 170) / 85.0 : 0;
                    double midtoneWeight = 1.0 - shadowWeight - highlightWeight;
                    
                    // Apply color balance
                    if (shadowWeight > 0) {
                        r = qBound(0, (int)(r + (shadowBalance.red() - 128) * shadowWeight * 0.3), 255);
                        g = qBound(0, (int)(g + (shadowBalance.green() - 128) * shadowWeight * 0.3), 255);
                        b = qBound(0, (int)(b + (shadowBalance.blue() - 128) * shadowWeight * 0.3), 255);
                    }
                    if (midtoneWeight > 0) {
                        r = qBound(0, (int)(r + (midtoneBalance.red() - 128) * midtoneWeight * 0.3), 255);
                        g = qBound(0, (int)(g + (midtoneBalance.green() - 128) * midtoneWeight * 0.3), 255);
                        b = qBound(0, (int)(b + (midtoneBalance.blue() - 128) * midtoneWeight * 0.3), 255);
                    }
                    if (highlightWeight > 0) {
                        r = qBound(0, (int)(r + (highlightBalance.red() - 128) * highlightWeight * 0.3), 255);
                        g = qBound(0, (int)(g + (highlightBalance.green() - 128) * highlightWeight * 0.3), 255);
                        b = qBound(0, (int)(b + (highlightBalance.blue() - 128) * highlightWeight * 0.3), 255);
                    }
                    
                    result.setPixel(x, y, qRgb(r, g, b));
                }
            }
        }
    }
    else if (filterType == "splittoning") {
        QVariantList params = parameter.toList();
        if (params.size() >= 3) {
            int highlightTint = params[0].toInt();
            int shadowTint = params[1].toInt();
            int balance = params[2].toInt();
            
            for (int y = 0; y < result.height(); ++y) {
                for (int x = 0; x < result.width(); ++x) {
                    QRgb pixel = result.pixel(x, y);
                    QColor color(pixel);
                    int h, s, l;
                    color.getHsl(&h, &s, &l);
                    
                    // Apply split toning based on luminance
                    double luminanceRatio = l / 255.0;
                    double balanceRatio = (balance + 100) / 200.0;
                    
                    if (luminanceRatio > balanceRatio && highlightTint != 0) {
                        // Apply highlight tint
                        h = highlightTint;
                        s = qBound(0, (int)(s + 30), 255); // Increase saturation slightly
                    } else if (luminanceRatio <= balanceRatio && shadowTint != 0) {
                        // Apply shadow tint
                        h = shadowTint;
                        s = qBound(0, (int)(s + 20), 255); // Increase saturation slightly
                    }
                    
                    color.setHsl(h, s, l);
                    result.setPixel(x, y, color.rgb());
                }
            }
        }
    }
    else if (filterType == "clarity") {
        int clarityAmount = parameter.toInt();
        if (clarityAmount != 0) {
            // Clarity: midtone contrast enhancement
            double factor = 1.0 + (clarityAmount / 100.0);
            for (int y = 0; y < result.height(); ++y) {
                for (int x = 0; x < result.width(); ++x) {
                    QRgb pixel = result.pixel(x, y);
                    int r = qRed(pixel);
                    int g = qGreen(pixel);
                    int b = qBlue(pixel);
                    
                    // Apply midtone contrast
                    r = qBound(0, (int)((r - 128) * factor + 128), 255);
                    g = qBound(0, (int)((g - 128) * factor + 128), 255);
                    b = qBound(0, (int)((b - 128) * factor + 128), 255);
                    
                    result.setPixel(x, y, qRgb(r, g, b));
                }
            }
        }
    }
    else if (filterType == "dehaze") {
        int dehazeAmount = parameter.toInt();
        if (dehazeAmount != 0) {
            // Dehaze: increase contrast and saturation
            double contrastFactor = 1.0 + (dehazeAmount / 200.0);
            double saturationFactor = 1.0 + (dehazeAmount / 300.0);
            
            for (int y = 0; y < result.height(); ++y) {
                for (int x = 0; x < result.width(); ++x) {
                    QRgb pixel = result.pixel(x, y);
                    QColor color(pixel);
                    int h, s, v;
                    color.getHsv(&h, &s, &v);
                    
                    // Apply contrast
                    v = qBound(0, (int)((v - 128) * contrastFactor + 128), 255);
                    // Apply saturation
                    s = qBound(0, (int)(s * saturationFactor), 255);
                    
                    color.setHsv(h, s, v);
                    result.setPixel(x, y, color.rgb());
                }
            }
        }
    }
    else if (filterType == "kodak") {
        // Kodak film emulation: warm tones, increased contrast
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Kodak characteristics: warm highlights, rich colors
                r = qBound(0, (int)(r * 1.05 + 10), 255);
                g = qBound(0, (int)(g * 1.02 + 5), 255);
                b = qBound(0, (int)(b * 0.98), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "fuji") {
        // Fuji film emulation: vibrant colors, slight green tint
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Fuji characteristics: vibrant greens and blues
                r = qBound(0, (int)(r * 1.02), 255);
                g = qBound(0, (int)(g * 1.08 + 8), 255);
                b = qBound(0, (int)(b * 1.05 + 3), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "ilford") {
        // Ilford B&W film emulation: high contrast B&W
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                // Professional B&W with high contrast
                int gray = (int)(0.299 * qRed(pixel) + 0.587 * qGreen(pixel) + 0.114 * qBlue(pixel));
                // Increase contrast for Ilford look
                gray = qBound(0, (int)((gray - 128) * 1.3 + 128), 255);
                result.setPixel(x, y, qRgb(gray, gray, gray));
            }
        }
    }
    else if (filterType == "cinemalog") {
        // Cinema Log: flat, desaturated look for grading
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                
                // Reduce contrast and saturation for log look
                v = qBound(0, (int)((v - 128) * 0.7 + 128), 255);
                s = qBound(0, (int)(s * 0.6), 255);
                
                color.setHsv(h, s, v);
                result.setPixel(x, y, color.rgb());
            }
        }
    }
    else if (filterType == "cinemar709") {
        // Cinema Rec.709: standard video color space
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Apply Rec.709 gamma curve (simplified)
                r = qBound(0, (int)(pow(r / 255.0, 0.45) * 255), 255);
                g = qBound(0, (int)(pow(g / 255.0, 0.45) * 255), 255);
                b = qBound(0, (int)(pow(b / 255.0, 0.45) * 255), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "bleachbypass") {
        // Bleach Bypass: high contrast, desaturated look
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                
                // High contrast, low saturation
                v = qBound(0, (int)((v - 128) * 1.4 + 128), 255);
                s = qBound(0, (int)(s * 0.3), 255);
                
                color.setHsv(h, s, v);
                result.setPixel(x, y, color.rgb());
            }
        }
    }
    else if (filterType == "luminositymask") {
        // Luminosity mask: enhance based on brightness
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int luminance = (qRed(pixel) + qGreen(pixel) + qBlue(pixel)) / 3;
                
                // Apply mask based on luminance
                double maskStrength = luminance / 255.0;
                int r = qBound(0, (int)(qRed(pixel) * (1.0 + maskStrength * 0.2)), 255);
                int g = qBound(0, (int)(qGreen(pixel) * (1.0 + maskStrength * 0.2)), 255);
                int b = qBound(0, (int)(qBlue(pixel) * (1.0 + maskStrength * 0.2)), 255);
                
                result.setPixel(x, y, qRgb(r, g, b));
            }
        }
    }
    else if (filterType == "colormask") {
        // Color mask: enhance dominant colors
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                QColor color(pixel);
                int h, s, v;
                color.getHsv(&h, &s, &v);
                
                // Enhance saturation for vivid colors
                if (s > 100) {
                    s = qBound(0, (int)(s * 1.2), 255);
                    color.setHsv(h, s, v);
                    result.setPixel(x, y, color.rgb());
                }
            }
        }
    }
    else if (filterType == "rangemask") {
        // Range mask: selective enhancement
        for (int y = 0; y < result.height(); ++y) {
            for (int x = 0; x < result.width(); ++x) {
                QRgb pixel = result.pixel(x, y);
                int luminance = (qRed(pixel) + qGreen(pixel) + qBlue(pixel)) / 3;
                
                // Apply selective enhancement to midtones
                if (luminance > 85 && luminance < 170) {
                    int r = qBound(0, (int)(qRed(pixel) * 1.1), 255);
                    int g = qBound(0, (int)(qGreen(pixel) * 1.1), 255);
                    int b = qBound(0, (int)(qBlue(pixel) * 1.1), 255);
                    result.setPixel(x, y, qRgb(r, g, b));
                }
            }
        }
    }
    
    return result;
}

void Zview::saveEditedImage()
{
    if (m_previewImage.isNull()) return;
    
    QString fileName = QFileDialog::getSaveFileName(this, 
        "Save Edited Image", 
        QFileInfo(m_currentImagePath).baseName() + "_edited.png",
        "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)");
    
    if (!fileName.isEmpty()) {
        if (m_previewImage.save(fileName)) {
            qDebug() << "Image saved successfully:" << fileName;
        } else {
            qDebug() << "Failed to save image:" << fileName;
        }
    }
}

// Advanced image editing operations (left toolbox)
void Zview::adjustGamma(int value)
{
    m_gammaValue = value;
    updateImagePreview();
}

void Zview::adjustExposure(int value)
{
    m_exposureValue = value;
    updateImagePreview();
}

void Zview::adjustHighlights(int value)
{
    m_highlightsValue = value;
    updateImagePreview();
}

void Zview::adjustShadows(int value)
{
    m_shadowsValue = value;
    updateImagePreview();
}

void Zview::adjustVibrance(int value)
{
    m_vibranceValue = value;
    updateImagePreview();
}

void Zview::adjustTemperature(int value)
{
    m_temperatureValue = value;
    updateImagePreview();
}

void Zview::adjustTint(int value)
{
    m_tintValue = value;
    updateImagePreview();
}

void Zview::applyNoiseReduction(int value)
{
    m_noiseReductionValue = value;
    updateImagePreview();
}

void Zview::applyVintage()
{
    m_vintageApplied = !m_vintageApplied;
    updateImagePreview();
}

void Zview::applyBlackWhite()
{
    m_blackWhiteApplied = !m_blackWhiteApplied;
    updateImagePreview();
}

void Zview::applyCrossProcess()
{
    m_crossProcessApplied = !m_crossProcessApplied;
    updateImagePreview();
}

void Zview::applyLomography()
{
    m_lomographyApplied = !m_lomographyApplied;
    updateImagePreview();
}

void Zview::applyPolaroid()
{
    m_polaroidApplied = !m_polaroidApplied;
    updateImagePreview();
}

void Zview::applyInvert()
{
    m_invertApplied = !m_invertApplied;
    updateImagePreview();
}

void Zview::applyPosterize()
{
    m_posterizeApplied = !m_posterizeApplied;
    updateImagePreview();
}

void Zview::applySobelEdge()
{
    m_sobelEdgeApplied = !m_sobelEdgeApplied;
    updateImagePreview();
}

// === PROFESSIONAL HSL COLOR EDITOR FUNCTIONS ===
void Zview::setHSLChannel(int channel)
{
    m_currentHSLChannel = channel;
    
    // Update button states
    m_hslAllBtn->setChecked(channel == 0);
    m_hslRedBtn->setChecked(channel == 1);
    m_hslOrangeBtn->setChecked(channel == 2);
    m_hslYellowBtn->setChecked(channel == 3);
    m_hslGreenBtn->setChecked(channel == 4);
    m_hslAquaBtn->setChecked(channel == 5);
    m_hslBlueBtn->setChecked(channel == 6);
    m_hslPurpleBtn->setChecked(channel == 7);
    m_hslMagentaBtn->setChecked(channel == 8);
    
    // Reset sliders to current values for the selected channel
    // For now, we'll use global values, but this could be extended
    // to have per-channel values stored in arrays
    updateImagePreview();
}

void Zview::adjustHSLHue(int value)
{
    m_hslHueValue = value;
    updateImagePreview();
}

void Zview::adjustHSLSaturation(int value)
{
    m_hslSaturationValue = value;
    updateImagePreview();
}

void Zview::adjustHSLLuminance(int value)
{
    m_hslLuminanceValue = value;
    updateImagePreview();
}

// === COLOR BALANCE WHEELS FUNCTIONS ===
void Zview::showShadowColorWheel()
{
    // Create color picker dialog for shadow color balance with live updates
    QColorDialog *colorDialog = new QColorDialog(m_shadowColorBalance, this);
    colorDialog->setWindowTitle("Shadow Color Balance - Live Preview");
    colorDialog->setOption(QColorDialog::DontUseNativeDialog, true); // Enable live updates
    
    // Connect for live updates while dragging/changing color
    connect(colorDialog, &QColorDialog::currentColorChanged, this, [this](const QColor &color) {
        m_shadowColorBalance = color;
        updateImagePreview(); // Live update while adjusting
    });
    
    // Handle final result
    connect(colorDialog, &QColorDialog::accepted, this, [this, colorDialog]() {
        m_shadowColorBalance = colorDialog->selectedColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    connect(colorDialog, &QColorDialog::rejected, this, [this, colorDialog]() {
        // Restore original color if cancelled
        m_shadowColorBalance = colorDialog->currentColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    colorDialog->show(); // Non-modal for better live experience
}

void Zview::showMidtoneColorWheel()
{
    // Create color picker dialog for midtone color balance with live updates
    QColorDialog *colorDialog = new QColorDialog(m_midtoneColorBalance, this);
    colorDialog->setWindowTitle("Midtone Color Balance - Live Preview");
    colorDialog->setOption(QColorDialog::DontUseNativeDialog, true); // Enable live updates
    
    // Connect for live updates while dragging/changing color
    connect(colorDialog, &QColorDialog::currentColorChanged, this, [this](const QColor &color) {
        m_midtoneColorBalance = color;
        updateImagePreview(); // Live update while adjusting
    });
    
    // Handle final result
    connect(colorDialog, &QColorDialog::accepted, this, [this, colorDialog]() {
        m_midtoneColorBalance = colorDialog->selectedColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    connect(colorDialog, &QColorDialog::rejected, this, [this, colorDialog]() {
        // Restore original color if cancelled
        m_midtoneColorBalance = colorDialog->currentColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    colorDialog->show(); // Non-modal for better live experience
}

void Zview::showHighlightColorWheel()
{
    // Create color picker dialog for highlight color balance with live updates
    QColorDialog *colorDialog = new QColorDialog(m_highlightColorBalance, this);
    colorDialog->setWindowTitle("Highlight Color Balance - Live Preview");
    colorDialog->setOption(QColorDialog::DontUseNativeDialog, true); // Enable live updates
    
    // Connect for live updates while dragging/changing color
    connect(colorDialog, &QColorDialog::currentColorChanged, this, [this](const QColor &color) {
        m_highlightColorBalance = color;
        updateImagePreview(); // Live update while adjusting
    });
    
    // Handle final result
    connect(colorDialog, &QColorDialog::accepted, this, [this, colorDialog]() {
        m_highlightColorBalance = colorDialog->selectedColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    connect(colorDialog, &QColorDialog::rejected, this, [this, colorDialog]() {
        // Restore original color if cancelled
        m_highlightColorBalance = colorDialog->currentColor();
        updateImagePreview();
        colorDialog->deleteLater();
    });
    
    colorDialog->show(); // Non-modal for better live experience
}

// === SPLIT TONING FUNCTIONS ===
void Zview::adjustHighlightTint(int value)
{
    m_highlightTintValue = value;
    updateImagePreview();
}

void Zview::adjustShadowTint(int value)
{
    m_shadowTintValue = value;
    updateImagePreview();
}

void Zview::adjustSplitToneBalance(int value)
{
    m_splitToneBalanceValue = value;
    updateImagePreview();
}

// === PROFESSIONAL ENHANCEMENT FUNCTIONS ===
void Zview::adjustClarity(int value)
{
    m_clarityValue = value;
    updateImagePreview();
}

void Zview::adjustDehaze(int value)
{
    m_dehazeValue = value;
    updateImagePreview();
}

void Zview::autoWhiteBalance()
{
    // Auto white balance algorithm - analyze image and adjust temperature/tint
    if (m_trueOriginalImageData.isNull()) return;
    
    // Calculate average color cast
    long long redSum = 0, greenSum = 0, blueSum = 0;
    int pixelCount = 0;
    
    // Sample middle 50% of image to avoid edge effects
    int startX = m_trueOriginalImageData.width() * 0.25;
    int endX = m_trueOriginalImageData.width() * 0.75;
    int startY = m_trueOriginalImageData.height() * 0.25;
    int endY = m_trueOriginalImageData.height() * 0.75;
    
    for (int y = startY; y < endY; y += 4) { // Sample every 4th pixel for performance
        for (int x = startX; x < endX; x += 4) {
            QRgb pixel = m_trueOriginalImageData.pixel(x, y);
            redSum += qRed(pixel);
            greenSum += qGreen(pixel);
            blueSum += qBlue(pixel);
            pixelCount++;
        }
    }
    
    if (pixelCount > 0) {
        double avgRed = redSum / (double)pixelCount;
        double avgGreen = greenSum / (double)pixelCount;
        double avgBlue = blueSum / (double)pixelCount;
        
        // Calculate temperature adjustment (red/blue balance)
        double tempRatio = avgBlue / avgRed;
        int tempAdjust = qBound(-100, (int)((tempRatio - 1.0) * 50), 100);
        
        // Calculate tint adjustment (green balance)
        double avgGray = (avgRed + avgGreen + avgBlue) / 3.0;
        double tintRatio = avgGreen / avgGray;
        int tintAdjust = qBound(-100, (int)((tintRatio - 1.0) * 100), 100);
        
        // Apply adjustments
        m_temperatureValue = tempAdjust;
        m_tintValue = tintAdjust;
        
        // Update UI sliders
        if (m_temperatureSlider) m_temperatureSlider->setValue(tempAdjust);
        if (m_tintSlider) m_tintSlider->setValue(tintAdjust);
        
        updateImagePreview();
    }
}

// === FILM EMULATION FUNCTIONS ===
void Zview::applyKodakEmulation()
{
    m_kodakEmulationApplied = !m_kodakEmulationApplied;
    // Reset other film emulations
    if (m_kodakEmulationApplied) {
        m_fujiEmulationApplied = false;
        m_ilfordEmulationApplied = false;
        if (m_filmFujiBtn) m_filmFujiBtn->setChecked(false);
        if (m_filmIlfordBtn) m_filmIlfordBtn->setChecked(false);
    }
    if (m_filmKodakBtn) m_filmKodakBtn->setChecked(m_kodakEmulationApplied);
    updateImagePreview();
}

void Zview::applyFujiEmulation()
{
    m_fujiEmulationApplied = !m_fujiEmulationApplied;
    // Reset other film emulations
    if (m_fujiEmulationApplied) {
        m_kodakEmulationApplied = false;
        m_ilfordEmulationApplied = false;
        if (m_filmKodakBtn) m_filmKodakBtn->setChecked(false);
        if (m_filmIlfordBtn) m_filmIlfordBtn->setChecked(false);
    }
    if (m_filmFujiBtn) m_filmFujiBtn->setChecked(m_fujiEmulationApplied);
    updateImagePreview();
}

void Zview::applyIlfordEmulation()
{
    m_ilfordEmulationApplied = !m_ilfordEmulationApplied;
    // Reset other film emulations
    if (m_ilfordEmulationApplied) {
        m_kodakEmulationApplied = false;
        m_fujiEmulationApplied = false;
        if (m_filmKodakBtn) m_filmKodakBtn->setChecked(false);
        if (m_filmFujiBtn) m_filmFujiBtn->setChecked(false);
    }
    if (m_filmIlfordBtn) m_filmIlfordBtn->setChecked(m_ilfordEmulationApplied);
    updateImagePreview();
}

// === CINEMA GRADE FUNCTIONS ===
void Zview::applyCinemaLog()
{
    m_cinemaLogApplied = !m_cinemaLogApplied;
    // Reset other cinema grades
    if (m_cinemaLogApplied) {
        m_cinemaRec709Applied = false;
        m_bleachBypassApplied = false;
        if (m_cinemaRec709Btn) m_cinemaRec709Btn->setChecked(false);
        if (m_cinemaBleachBtn) m_cinemaBleachBtn->setChecked(false);
    }
    if (m_cinemaLogBtn) m_cinemaLogBtn->setChecked(m_cinemaLogApplied);
    updateImagePreview();
}

void Zview::applyCinemaRec709()
{
    m_cinemaRec709Applied = !m_cinemaRec709Applied;
    // Reset other cinema grades
    if (m_cinemaRec709Applied) {
        m_cinemaLogApplied = false;
        m_bleachBypassApplied = false;
        if (m_cinemaLogBtn) m_cinemaLogBtn->setChecked(false);
        if (m_cinemaBleachBtn) m_cinemaBleachBtn->setChecked(false);
    }
    if (m_cinemaRec709Btn) m_cinemaRec709Btn->setChecked(m_cinemaRec709Applied);
    updateImagePreview();
}

void Zview::applyBleachBypass()
{
    m_bleachBypassApplied = !m_bleachBypassApplied;
    // Reset other cinema grades
    if (m_bleachBypassApplied) {
        m_cinemaLogApplied = false;
        m_cinemaRec709Applied = false;
        if (m_cinemaLogBtn) m_cinemaLogBtn->setChecked(false);
        if (m_cinemaRec709Btn) m_cinemaRec709Btn->setChecked(false);
    }
    if (m_cinemaBleachBtn) m_cinemaBleachBtn->setChecked(m_bleachBypassApplied);
    updateImagePreview();
}

// === PROFESSIONAL MASKING FUNCTIONS ===
void Zview::createLuminosityMask()
{
    m_luminosityMaskActive = !m_luminosityMaskActive;
    // Reset other masks
    if (m_luminosityMaskActive) {
        m_colorMaskActive = false;
        m_rangeMaskActive = false;
        if (m_colorMaskBtn) m_colorMaskBtn->setChecked(false);
        if (m_rangeMaskBtn) m_rangeMaskBtn->setChecked(false);
    }
    if (m_luminosityMaskBtn) m_luminosityMaskBtn->setChecked(m_luminosityMaskActive);
    updateImagePreview();
}

void Zview::createColorMask()
{
    m_colorMaskActive = !m_colorMaskActive;
    // Reset other masks
    if (m_colorMaskActive) {
        m_luminosityMaskActive = false;
        m_rangeMaskActive = false;
        if (m_luminosityMaskBtn) m_luminosityMaskBtn->setChecked(false);
        if (m_rangeMaskBtn) m_rangeMaskBtn->setChecked(false);
    }
    if (m_colorMaskBtn) m_colorMaskBtn->setChecked(m_colorMaskActive);
    updateImagePreview();
}

void Zview::createRangeMask()
{
    m_rangeMaskActive = !m_rangeMaskActive;
    // Reset other masks
    if (m_rangeMaskActive) {
        m_luminosityMaskActive = false;
        m_colorMaskActive = false;
        if (m_luminosityMaskBtn) m_luminosityMaskBtn->setChecked(false);
        if (m_colorMaskBtn) m_colorMaskBtn->setChecked(false);
    }
    if (m_rangeMaskBtn) m_rangeMaskBtn->setChecked(m_rangeMaskActive);
    updateImagePreview();
}

// Professional Curve Widget Class - Industry Standard Features
class ProfessionalCurveWidget : public QWidget {
    Q_OBJECT
    
public:
    enum CurveMode {
        RGB_COMPOSITE,
        RED_CHANNEL,
        GREEN_CHANNEL,
        BLUE_CHANNEL,
        LUMINANCE_ONLY
    };
    
    enum CurveType {
        PARAMETRIC_CURVE,
        POINT_CURVE,
        HUE_VS_SAT,
        HUE_VS_LUM,
        LUM_VS_SAT
    };
    
    explicit ProfessionalCurveWidget(QWidget *parent = nullptr) : QWidget(parent) {
        setFixedSize(520, 400);
        setMouseTracking(true);
        
        // Initialize with professional curve presets
        m_currentMode = RGB_COMPOSITE;
        m_currentType = POINT_CURVE;
        m_showHistogram = true;
        m_showGrid = true;
        m_showClipping = false;
        m_snapToGrid = false;
        
        // Initialize curve points for each channel
        initializeCurvePoints();
        
        // Professional curve presets
        initializePresets();
        
        // Create lookup tables
        updateAllLookupTables();
        
        // Initialize performance throttling timer for smooth live preview
        m_updateThrottleTimer = new QTimer(this);
        m_updateThrottleTimer->setSingleShot(true);
        m_updateThrottleTimer->setInterval(16); // ~60 FPS for smooth live preview
        connect(m_updateThrottleTimer, &QTimer::timeout, this, [this]() {
            emit curveChanged();
        });
    }
    
    // Public interface
    QVector<QPoint> getCurvePoints() const { return m_curvePoints[m_currentMode]; }
    QVector<int> getLookupTable() const { return m_lookupTables[m_currentMode]; }
    CurveMode getCurrentMode() const { return m_currentMode; }
    CurveType getCurrentType() const { return m_currentType; }
    
    void setCurveMode(CurveMode mode) { 
        qDebug() << "CURVE WIDGET: setCurveMode called with mode:" << mode << "(LUMINANCE_ONLY=" << LUMINANCE_ONLY << ")";
        m_currentMode = mode; 
        update(); 
        emit curveChanged(); 
    }
    
    void setCurveType(CurveType type) { 
        m_currentType = type; 
        update(); 
        emit curveChanged(); 
    }
    
    void setShowHistogram(bool show) { 
        m_showHistogram = show; 
        update(); 
    }
    
    void setShowGrid(bool show) { 
        m_showGrid = show; 
        update(); 
    }
    
    void setShowClipping(bool show) { 
        m_showClipping = show; 
        update(); 
    }
    
    void setSnapToGrid(bool snap) { 
        m_snapToGrid = snap; 
    }
    
    void setHistogramData(const QVector<int> &histogram) {
        m_histogram = histogram;
        update(); // Trigger repaint to show histogram
    }
    
    void applyPreset(const QString &presetName) {
        if (m_presets.contains(presetName)) {
            // Apply preset to the current mode
            m_curvePoints[m_currentMode] = m_presets[presetName];
            updateAllLookupTables();
            update();
            emit curveChanged();
            
            // Debug output to confirm preset application
            qDebug() << "Applied preset:" << presetName << "to mode:" << m_currentMode;
            qDebug() << "Curve points count:" << m_curvePoints[m_currentMode].size();
        } else {
            qDebug() << "Preset not found:" << presetName;
        }
    }
    
    void resetCurve() {
        initializeCurvePoints();
        updateAllLookupTables();
        update();
        emit curveChanged();
    }
    
    void applyPresetToAllModes(const QString &presetName) {
        if (m_presets.contains(presetName)) {
            // Apply preset to all modes (RGB, R, G, B, Luminance)
            for (int mode = 0; mode < m_curvePoints.size(); mode++) {
                m_curvePoints[mode] = m_presets[presetName];
            }
            updateAllLookupTables();
            update();
            emit curveChanged();
            qDebug() << "Applied preset to all modes:" << presetName;
        }
    }
    
    void saveCurrentAsPreset(const QString &presetName) {
        // Save current curve points as a new preset
        m_presets[presetName] = m_curvePoints[m_currentMode];
        qDebug() << "Saved current curve as preset:" << presetName;
        emit presetSaved(presetName);
    }
    
    QStringList getPresetNames() const {
        return m_presets.keys();
    }
    
    void autoAdjust() {
        // Automatic curve adjustment based on image analysis
        createAutoSCurve();
        updateAllLookupTables();
        update();
        emit curveChanged();
    }
    
    // Public curve points for access
    QVector<QVector<QPoint>> m_curvePoints;
    
    void updateAllLookupTables() {
        for (int mode = 0; mode < 5; mode++) {
            updateLookupTable(mode);
        }
    }
    
private:
    CurveMode m_currentMode;
    CurveType m_currentType;
    bool m_showHistogram;
    bool m_showGrid;
    bool m_showClipping;
    bool m_snapToGrid;
    
    QVector<QVector<int>> m_lookupTables;
    QMap<QString, QVector<QPoint>> m_presets;
    QVector<int> m_histogram;
    int m_dragIndex = -1;
    QPoint m_lastMousePos;
    QTimer *m_updateThrottleTimer = nullptr;
    
    void initializeCurvePoints() {
        m_curvePoints.clear();
        m_curvePoints.resize(5); // RGB_COMPOSITE, RED, GREEN, BLUE, LUMINANCE
        m_lookupTables.resize(5);
        
        for (int i = 0; i < 5; i++) {
            m_curvePoints[i].clear();
            m_curvePoints[i].append(QPoint(0, 0));     // Shadow point (input=0, output=0)
            m_curvePoints[i].append(QPoint(255, 255)); // Highlight point (input=255, output=255)
            m_lookupTables[i].resize(256);
        }
    }
    
    void initializePresets() {
        // Professional curve presets based on industry standards
        
        // Strong S-Curve (High Contrast)
        QVector<QPoint> strongS;
        strongS.append(QPoint(0, 0));
        strongS.append(QPoint(64, 55));
        strongS.append(QPoint(128, 128));
        strongS.append(QPoint(192, 200));
        strongS.append(QPoint(255, 255));
        m_presets["Strong Contrast"] = strongS;
        
        // Medium S-Curve (Cinematic)
        QVector<QPoint> mediumS;
        mediumS.append(QPoint(0, 0));
        mediumS.append(QPoint(85, 70));
        mediumS.append(QPoint(128, 128));
        mediumS.append(QPoint(170, 185));
        mediumS.append(QPoint(255, 255));
        m_presets["Cinematic"] = mediumS;
        
        // Film Emulation
        QVector<QPoint> filmLook;
        filmLook.append(QPoint(0, 20));   // Lifted blacks
        filmLook.append(QPoint(64, 75));
        filmLook.append(QPoint(128, 128));
        filmLook.append(QPoint(192, 180));
        filmLook.append(QPoint(255, 235)); // Compressed whites
        m_presets["Film Emulation"] = filmLook;
        
        // Matte Finish
        QVector<QPoint> matte;
        matte.append(QPoint(0, 55));      // Heavily lifted blacks
        matte.append(QPoint(128, 128));
        matte.append(QPoint(255, 200));   // Compressed whites
        m_presets["Matte Finish"] = matte;
        
        // High Key
        QVector<QPoint> highKey;
        highKey.append(QPoint(0, 0));
        highKey.append(QPoint(64, 35));
        highKey.append(QPoint(128, 95));
        highKey.append(QPoint(192, 155));
        highKey.append(QPoint(255, 255));
        m_presets["High Key"] = highKey;
        
        // Low Key
        QVector<QPoint> lowKey;
        lowKey.append(QPoint(0, 0));
        lowKey.append(QPoint(64, 100));
        lowKey.append(QPoint(128, 160));
        lowKey.append(QPoint(192, 220));
        lowKey.append(QPoint(255, 255));
        m_presets["Low Key"] = lowKey;
        
        // Vintage
        QVector<QPoint> vintage;
        vintage.append(QPoint(0, 15));
        vintage.append(QPoint(85, 80));
        vintage.append(QPoint(128, 135));
        vintage.append(QPoint(170, 175));
        vintage.append(QPoint(255, 240));
        m_presets["Vintage"] = vintage;
        
        // Linear (No adjustment)
        QVector<QPoint> linear;
        linear.append(QPoint(0, 0));
        linear.append(QPoint(255, 255));
        m_presets["Linear"] = linear;
    }
    
    void createAutoSCurve() {
        // Automatic S-curve generation based on image analysis
        m_curvePoints[m_currentMode].clear();
        m_curvePoints[m_currentMode].append(QPoint(0, 245));    // Slight black lift
        m_curvePoints[m_currentMode].append(QPoint(64, 190));   // Shadow control
        m_curvePoints[m_currentMode].append(QPoint(128, 128));  // Midtone anchor
        m_curvePoints[m_currentMode].append(QPoint(192, 65));   // Highlight control
        m_curvePoints[m_currentMode].append(QPoint(255, 10));   // Slight white compression
    }
    
    void updateLookupTable(int mode) {
        if (mode >= m_lookupTables.size()) return;
        
        m_lookupTables[mode].clear();
        m_lookupTables[mode].resize(256);
        
        // Cubic spline interpolation for smoother curves
        for (int x = 0; x <= 255; x++) {
            int y = calculateCubicSplineValue(x, m_curvePoints[mode]);
            m_lookupTables[mode][x] = qBound(0, y, 255);
        }
    }
    
    int calculateCubicSplineValue(int x, const QVector<QPoint> &points) {
        if (points.size() < 2) return x;
        
        // Find the segment
        int i = 0;
        while (i < points.size() - 1 && points[i + 1].x() <= x) {
            i++;
        }
        
        if (i >= points.size() - 1) {
            return points.last().y();
        }
        
        QPoint p1 = points[i];
        QPoint p2 = points[i + 1];
        
        // Simple linear interpolation (can be enhanced to cubic spline)
        if (p2.x() == p1.x()) {
            return p1.y();
        }
        
        float t = float(x - p1.x()) / float(p2.x() - p1.x());
        
        // Smooth interpolation using cubic easing
        t = t * t * (3.0f - 2.0f * t); // Smoothstep function
        
        return p1.y() + t * (p2.y() - p1.y());
    }
    
signals:
    void curveChanged();
    void modeChanged(CurveMode mode);
    void presetSaved(const QString &presetName);
    
protected:
    void paintEvent(QPaintEvent *event) override {
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setRenderHint(QPainter::SmoothPixmapTransform);
        
        // Calculate curve area (leave space for controls)
        QRect curveRect = rect().adjusted(40, 40, -40, -60);
        
        // Draw professional background
        QLinearGradient bg(0, 0, 0, height());
        bg.setColorAt(0, QColor(32, 32, 36));
        bg.setColorAt(1, QColor(24, 24, 28));
        painter.fillRect(rect(), bg);
        
        // Draw curve area background
        painter.fillRect(curveRect, QColor(28, 28, 32));
        
        // Draw histogram if enabled
        if (m_showHistogram && !m_histogram.isEmpty()) {
            drawHistogram(painter, curveRect);
        }
        
        // Draw grid if enabled
        if (m_showGrid) {
            drawGrid(painter, curveRect);
        }
        
        // Draw clipping indicators if enabled
        if (m_showClipping) {
            drawClippingIndicators(painter, curveRect);
        }
        
        // Draw curve area border
        painter.setPen(QPen(QColor(120, 120, 125), 2));
        painter.drawRect(curveRect);
        
        // Draw diagonal reference line
        painter.setPen(QPen(QColor(80, 80, 85), 1, Qt::DashLine));
        painter.drawLine(curveRect.bottomLeft(), curveRect.topRight());
        
        // Draw the curve
        drawCurve(painter, curveRect);
        
        // Draw curve mode indicator
        drawModeIndicator(painter);
        
        // Draw input/output value indicators
        drawValueIndicators(painter, curveRect);
    }
    
private:
    void drawHistogram(QPainter &painter, const QRect &curveRect) {
        if (m_histogram.isEmpty()) return;
        
        painter.save();
        painter.setOpacity(0.3);
        
        int maxValue = *std::max_element(m_histogram.begin(), m_histogram.end());
        if (maxValue == 0) return;
        
        // Draw histogram bars
        for (int i = 0; i < m_histogram.size() && i < 256; i++) {
            int x = curveRect.left() + (i * curveRect.width()) / 255;
            int barHeight = (m_histogram[i] * curveRect.height()) / maxValue;
            int y = curveRect.bottom() - barHeight;
            
            painter.setPen(QPen(QColor(180, 180, 185), 1));
            painter.drawLine(x, curveRect.bottom(), x, y);
        }
        
        painter.restore();
    }
    
    void drawGrid(QPainter &painter, const QRect &curveRect) {
        painter.save();
        painter.setPen(QPen(QColor(60, 60, 65), 1, Qt::DotLine));
        
        // Draw quarter lines
        for (int i = 1; i < 4; i++) {
            int x = curveRect.left() + (curveRect.width() * i) / 4;
            int y = curveRect.top() + (curveRect.height() * i) / 4;
            painter.drawLine(x, curveRect.top(), x, curveRect.bottom());
            painter.drawLine(curveRect.left(), y, curveRect.right(), y);
        }
        
        // Draw eighth lines (lighter)
        painter.setPen(QPen(QColor(45, 45, 50), 1, Qt::DotLine));
        for (int i = 1; i < 8; i++) {
            if (i % 2 == 0) continue; // Skip quarter lines
            int x = curveRect.left() + (curveRect.width() * i) / 8;
            int y = curveRect.top() + (curveRect.height() * i) / 8;
            painter.drawLine(x, curveRect.top(), x, curveRect.bottom());
            painter.drawLine(curveRect.left(), y, curveRect.right(), y);
        }
        
        painter.restore();
    }
    
    void drawClippingIndicators(QPainter &painter, const QRect &curveRect) {
        painter.save();
        
        // Check for clipping in current curve
        auto currentPoints = m_curvePoints[m_currentMode];
        bool hasClipping = false;
        
        for (const QPoint &point : currentPoints) {
            if (point.y() <= 5 || point.y() >= 250) {
                hasClipping = true;
                break;
            }
        }
        
        if (hasClipping) {
            painter.setPen(QPen(QColor(255, 100, 100), 2));
            painter.drawRect(curveRect.adjusted(-2, -2, 2, 2));
        }
        
        painter.restore();
    }
    
    void drawCurve(QPainter &painter, const QRect &curveRect) {
        auto currentPoints = m_curvePoints[m_currentMode];
        if (currentPoints.size() < 2) return;
        
        painter.save();
        
        // Get curve color based on mode
        QColor curveColor = getCurveColor();
        
        // Draw smooth curve using lookup table
        QPainterPath curvePath;
        bool firstPoint = true;
        
        for (int x = 0; x <= 255; x++) {
            int y = m_lookupTables[m_currentMode][x];
            QPoint widgetPoint = mapToWidget(QPoint(x, y), curveRect);
            
            if (firstPoint) {
                curvePath.moveTo(widgetPoint);
                firstPoint = false;
            } else {
                curvePath.lineTo(widgetPoint);
            }
        }
        
        // Draw curve with glow effect
        painter.setPen(QPen(curveColor.darker(150), 5));
        painter.setOpacity(0.3);
        painter.drawPath(curvePath);
        
        painter.setPen(QPen(curveColor, 3));
        painter.setOpacity(1.0);
        painter.drawPath(curvePath);
        
        // Draw control points
        painter.setBrush(QColor(255, 255, 255));
        painter.setPen(QPen(curveColor, 2));
        
        for (int i = 0; i < currentPoints.size(); i++) {
            QPoint widgetPoint = mapToWidget(currentPoints[i], curveRect);
            
            // Highlight selected point
            if (i == m_dragIndex) {
                painter.setBrush(curveColor);
                painter.drawEllipse(widgetPoint, 8, 8);
                painter.setBrush(QColor(255, 255, 255));
            } else {
                painter.drawEllipse(widgetPoint, 6, 6);
            }
        }
        
        painter.restore();
    }
    
    void drawModeIndicator(QPainter &painter) {
        painter.save();
        
        // Draw mode tabs at the top with enhanced visibility
        QStringList modeNames = {"RGB", "R", "G", "B", "L"};
        QVector<QColor> modeColors = {
            QColor(255, 140, 80),   // RGB - Orange
            QColor(255, 120, 120),  // Red
            QColor(120, 255, 120),  // Green
            QColor(120, 120, 255),  // Blue
            QColor(220, 220, 220)   // Luminance - Gray
        };
        
        int tabWidth = 70;
        int tabHeight = 30;
        int startX = (width() - (modeNames.size() * tabWidth)) / 2;
        
        for (int i = 0; i < modeNames.size(); i++) {
            QRect tabRect(startX + i * tabWidth, 5, tabWidth, tabHeight);
            
            // Draw tab background with gradient
            if (i == m_currentMode) {
                // Active tab - bright background with glow
                QLinearGradient activeGrad(tabRect.topLeft(), tabRect.bottomLeft());
                activeGrad.setColorAt(0, modeColors[i].lighter(150));
                activeGrad.setColorAt(1, modeColors[i]);
                painter.fillRect(tabRect, activeGrad);
                painter.setPen(QPen(modeColors[i].lighter(200), 3));
                painter.drawRect(tabRect);
                
                // Add glow effect
                painter.setPen(QPen(modeColors[i], 1));
                painter.drawRect(tabRect.adjusted(-1, -1, 1, 1));
            } else {
                // Inactive tab - subtle background
                painter.fillRect(tabRect, QColor(45, 45, 50, 180));
                painter.setPen(QPen(QColor(120, 120, 125), 1));
                painter.drawRect(tabRect);
            }
            
            // Draw tab text with better contrast
            if (i == m_currentMode) {
                painter.setPen(QColor(0, 0, 0)); // Black text on bright background
            } else {
                painter.setPen(modeColors[i].darker(120));
            }
            painter.setFont(QFont("SF Pro Display", 11, QFont::Bold));
            painter.drawText(tabRect, Qt::AlignCenter, modeNames[i]);
        }
        
        // Draw current mode name below tabs
        QString currentModeName;
        switch (m_currentMode) {
            case RGB_COMPOSITE: currentModeName = "RGB Composite"; break;
            case RED_CHANNEL: currentModeName = "Red Channel"; break;
            case GREEN_CHANNEL: currentModeName = "Green Channel"; break;
            case BLUE_CHANNEL: currentModeName = "Blue Channel"; break;
            case LUMINANCE_ONLY: currentModeName = "Luminance"; break;
        }
        
        painter.setPen(modeColors[m_currentMode]);
        painter.setFont(QFont("SF Pro Display", 9, QFont::Medium));
        painter.drawText(QRect(0, 40, width(), 20), Qt::AlignCenter, currentModeName);
        
        painter.restore();
    }
    
    void drawValueIndicators(QPainter &painter, const QRect &curveRect) {
        if (m_dragIndex < 0 || m_dragIndex >= m_curvePoints[m_currentMode].size()) return;
        
        painter.save();
        
        QPoint currentPoint = m_curvePoints[m_currentMode][m_dragIndex];
        QPoint widgetPoint = mapToWidget(currentPoint, curveRect);
        
        // Draw crosshair
        painter.setPen(QPen(QColor(255, 255, 100), 1, Qt::DashLine));
        painter.drawLine(widgetPoint.x(), curveRect.top(), widgetPoint.x(), curveRect.bottom());
        painter.drawLine(curveRect.left(), widgetPoint.y(), curveRect.right(), widgetPoint.y());
        
        // Draw value text
        QString valueText = QString("In: %1, Out: %2").arg(currentPoint.x()).arg(255 - currentPoint.y());
        QRect textRect = painter.fontMetrics().boundingRect(valueText);
        textRect.moveCenter(QPoint(width() / 2, height() - 20));
        
        painter.fillRect(textRect.adjusted(-5, -2, 5, 2), QColor(0, 0, 0, 180));
        painter.setPen(QColor(255, 255, 255));
        painter.drawText(textRect, Qt::AlignCenter, valueText);
        
        painter.restore();
    }
    
    QColor getCurveColor() {
        switch (m_currentMode) {
            case RED_CHANNEL: return QColor(255, 120, 120);
            case GREEN_CHANNEL: return QColor(120, 255, 120);
            case BLUE_CHANNEL: return QColor(120, 120, 255);
            case LUMINANCE_ONLY: return QColor(220, 220, 220);
            default: return QColor(255, 140, 80); // RGB Composite - Orange
        }
    }
    
    void mousePressEvent(QMouseEvent *event) override {
        QRect curveRect = rect().adjusted(40, 40, -40, -60);
        
        // Check if clicking on mode tabs
        if (event->pos().y() < 30) {
            handleModeTabClick(event->pos());
            return;
        }
        
        // Only handle curve area clicks
        if (!curveRect.contains(event->pos())) return;
        
        if (event->button() == Qt::LeftButton) {
            QPoint imagePoint = mapToImage(event->pos(), curveRect);
            auto &currentPoints = m_curvePoints[m_currentMode];
            
            // Check if clicking on existing point
            m_dragIndex = -1;
            for (int i = 0; i < currentPoints.size(); i++) {
                QPoint widgetPoint = mapToWidget(currentPoints[i], curveRect);
                if ((event->pos() - widgetPoint).manhattanLength() < 15) {
                    m_dragIndex = i;
                    m_lastMousePos = event->pos();
                    break;
                }
            }
            
            // If not on existing point, add new point
            if (m_dragIndex == -1 && imagePoint.x() >= 0 && imagePoint.x() <= 255 
                && imagePoint.y() >= 0 && imagePoint.y() <= 255) {
                
                // Limit maximum number of points
                if (currentPoints.size() >= 16) return;
                
                // Find insertion position to keep points sorted by x
                int insertPos = 0;
                for (int i = 0; i < currentPoints.size(); i++) {
                    if (currentPoints[i].x() < imagePoint.x()) {
                        insertPos = i + 1;
                    } else {
                        break;
                    }
                }
                
                // Apply grid snapping if enabled
                imagePoint = snapToGrid(imagePoint);
                
                currentPoints.insert(insertPos, imagePoint);
                m_dragIndex = insertPos;
                updateAllLookupTables();
                emit curveChanged();
                update();
            }
        } else if (event->button() == Qt::RightButton) {
            // Remove point on right click (except first and last)
            auto &currentPoints = m_curvePoints[m_currentMode];
            for (int i = 1; i < currentPoints.size() - 1; i++) {
                QPoint widgetPoint = mapToWidget(currentPoints[i], curveRect);
                if ((event->pos() - widgetPoint).manhattanLength() < 15) {
                    currentPoints.removeAt(i);
                    updateAllLookupTables();
                    emit curveChanged();
                    update();
                    break;
                }
            }
        }
    }
    
    void mouseMoveEvent(QMouseEvent *event) override {
        QRect curveRect = rect().adjusted(40, 40, -40, -60);
        
        if (m_dragIndex >= 0 && m_dragIndex < m_curvePoints[m_currentMode].size()) {
            QPoint imagePoint = mapToImage(event->pos(), curveRect);
            auto &currentPoints = m_curvePoints[m_currentMode];
            
            // Constrain to valid range
            imagePoint.setX(qBound(0, imagePoint.x(), 255));
            imagePoint.setY(qBound(0, imagePoint.y(), 255));
            
            // Don't allow first and last points to move horizontally
            if (m_dragIndex == 0) {
                imagePoint.setX(0);
            } else if (m_dragIndex == currentPoints.size() - 1) {
                imagePoint.setX(255);
            } else {
                // Ensure x ordering is maintained
                int minX = (m_dragIndex > 0) ? currentPoints[m_dragIndex - 1].x() + 1 : 0;
                int maxX = (m_dragIndex < currentPoints.size() - 1) ? currentPoints[m_dragIndex + 1].x() - 1 : 255;
                imagePoint.setX(qBound(minX, imagePoint.x(), maxX));
            }
            
            // Apply grid snapping if enabled
            imagePoint = snapToGrid(imagePoint);
            
            // Fine adjustment with Shift key
            if (event->modifiers() & Qt::ShiftModifier) {
                QPoint delta = event->pos() - m_lastMousePos;
                imagePoint = currentPoints[m_dragIndex];
                imagePoint.setX(qBound(0, imagePoint.x() + delta.x() / 4, 255));
                imagePoint.setY(qBound(0, imagePoint.y() - delta.y() / 4, 255));
            }
            
            currentPoints[m_dragIndex] = imagePoint;
            updateAllLookupTables();
            
            // Use throttled updates for smooth performance during live dragging
            if (m_updateThrottleTimer && !m_updateThrottleTimer->isActive()) {
                m_updateThrottleTimer->start();
            }
            update();
        }
        
        m_lastMousePos = event->pos();
        
        // Update cursor based on hover state
        updateCursor(event->pos(), curveRect);
    }
    
    void mouseReleaseEvent(QMouseEvent *event) override {
        m_dragIndex = -1;
        setCursor(Qt::ArrowCursor);
    }
    
    void wheelEvent(QWheelEvent *event) override {
        // Zoom functionality for precise editing
        QRect curveRect = rect().adjusted(40, 40, -40, -60);
        if (!curveRect.contains(event->position().toPoint())) return;
        
        // Future: Implement zoom functionality
        event->accept();
    }
    
    void keyPressEvent(QKeyEvent *event) override {
        switch (event->key()) {
            case Qt::Key_Delete:
            case Qt::Key_Backspace:
                if (m_dragIndex > 0 && m_dragIndex < m_curvePoints[m_currentMode].size() - 1) {
                    m_curvePoints[m_currentMode].removeAt(m_dragIndex);
                    m_dragIndex = -1;
                    updateAllLookupTables();
                    emit curveChanged();
                    update();
                }
                break;
            case Qt::Key_R:
                if (event->modifiers() & Qt::ControlModifier) {
                    resetCurve();
                }
                break;
            case Qt::Key_A:
                if (event->modifiers() & Qt::ControlModifier) {
                    autoAdjust();
                }
                break;
            case Qt::Key_1: setCurveMode(RGB_COMPOSITE); break;
            case Qt::Key_2: setCurveMode(RED_CHANNEL); break;
            case Qt::Key_3: setCurveMode(GREEN_CHANNEL); break;
            case Qt::Key_4: setCurveMode(BLUE_CHANNEL); break;
            case Qt::Key_5: setCurveMode(LUMINANCE_ONLY); break;
        }
        QWidget::keyPressEvent(event);
    }
    
private:
    void handleModeTabClick(const QPoint &pos) {
        QStringList modeNames = {"RGB", "R", "G", "B", "L"};
        int tabWidth = 60;
        int startX = (width() - (modeNames.size() * tabWidth)) / 2;
        
        for (int i = 0; i < modeNames.size(); i++) {
            QRect tabRect(startX + i * tabWidth, 5, tabWidth, 25);
            if (tabRect.contains(pos)) {
                qDebug() << "CURVE ADJUSTOR: Mode tab clicked -" << modeNames[i] << "at position" << pos;
                setCurveMode(static_cast<CurveMode>(i));
                break;
            }
        }
    }
    
    void updateCursor(const QPoint &pos, const QRect &curveRect) {
        if (!curveRect.contains(pos)) {
            setCursor(Qt::ArrowCursor);
            return;
        }
        
        // Check if hovering over a control point
        auto &currentPoints = m_curvePoints[m_currentMode];
        for (int i = 0; i < currentPoints.size(); i++) {
            QPoint widgetPoint = mapToWidget(currentPoints[i], curveRect);
            if ((pos - widgetPoint).manhattanLength() < 15) {
                setCursor(Qt::SizeAllCursor);
                return;
            }
        }
        
        setCursor(Qt::CrossCursor);
    }
    
    QPoint snapToGrid(const QPoint &point) {
        if (!m_snapToGrid) return point;
        
        // Snap to 16-level grid (every 16 units)
        int snappedX = ((point.x() + 8) / 16) * 16;
        int snappedY = ((point.y() + 8) / 16) * 16;
        return QPoint(qBound(0, snappedX, 255), qBound(0, snappedY, 255));
    }
    
    QPoint mapToWidget(const QPoint &imagePoint, const QRect &curveRect) const {
        int x = curveRect.left() + (imagePoint.x() * curveRect.width()) / 255;
        int y = curveRect.top() + ((255 - imagePoint.y()) * curveRect.height()) / 255;
        return QPoint(x, y);
    }
    
    QPoint mapToImage(const QPoint &widgetPoint, const QRect &curveRect) const {
        int x = ((widgetPoint.x() - curveRect.left()) * 255) / curveRect.width();
        int y = 255 - ((widgetPoint.y() - curveRect.top()) * 255) / curveRect.height();
        return QPoint(qBound(0, x, 255), qBound(0, y, 255));
    }
};

void Zview::showCurveAdjustor() {
    if (m_curveAdjustorDialog) {
        m_curveAdjustorDialog->raise();
        m_curveAdjustorDialog->activateWindow();
        return;
    }
    
    // Store original image for curve adjustments
    if (!m_currentImage.isNull()) {
        m_originalImage = m_currentImage;
    } else {
        qDebug() << "No current image available for curve adjustment";
        return;
    }
    
         // Create ultra-modern curve adjustment dialog
     m_curveAdjustorDialog = new QWidget(this, Qt::Window | Qt::FramelessWindowHint);
     m_curveAdjustorDialog->setWindowTitle("🎯 Curve Adjustor");
     m_curveAdjustorDialog->setFixedSize(750, 780);
     m_curveAdjustorDialog->setAttribute(Qt::WA_DeleteOnClose);
     m_curveAdjustorDialog->setAttribute(Qt::WA_TranslucentBackground);
    
    // Ultra-modern 2024 styling with glassmorphism and neumorphism
    m_curveAdjustorDialog->setStyleSheet(
        "QWidget {"
        "    background: qradialgradient(cx:0.5, cy:0.3, radius:1.2,"
        "                               stop:0 rgba(15, 15, 20, 0.95),"
        "                               stop:0.6 rgba(10, 10, 15, 0.98),"
        "                               stop:1 rgba(5, 5, 10, 1.0));"
        "    color: rgba(255, 255, 255, 0.95);"
        "    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI Variable', sans-serif;"
        "    border-radius: 24px;"
        "    border: 1px solid rgba(255, 255, 255, 0.08);"
        "}"
        "QWidget#mainContainer {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(18, 18, 25, 0.97),"
        "                               stop:0.3 rgba(22, 22, 30, 0.95),"
        "                               stop:0.7 rgba(15, 15, 22, 0.98),"
        "                               stop:1 rgba(12, 12, 18, 1.0));"
        "    border-radius: 24px;"
        "    border: 1px solid rgba(255, 255, 255, 0.12);"
        "    backdrop-filter: blur(20px);"
        "}"
                 "QLabel#titleLabel {"
         "    color: rgba(255, 255, 255, 0.95);"
         "    background: transparent;"
         "    font-size: 24px;"
         "    font-weight: 700;"
         "    letter-spacing: -0.3px;"
         "    padding: 12px 0;"
         "}"
        "QLabel#subtitleLabel {"
        "    color: rgba(255, 255, 255, 0.65);"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    letter-spacing: 0.2px;"
        "    padding: 0 0 20px 0;"
        "}"
                 "QPushButton {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(45, 45, 55, 0.9),"
         "                               stop:1 rgba(35, 35, 45, 0.95));"
         "    color: rgba(255, 255, 255, 0.9);"
         "    border: 1px solid rgba(255, 255, 255, 0.15);"
         "    border-radius: 10px;"
         "    padding: 8px 16px;"
         "    font-size: 12px;"
         "    font-weight: 600;"
         "    letter-spacing: 0.2px;"
         "    min-height: 32px;"
         "    max-height: 36px;"
         "    backdrop-filter: blur(10px);"
         "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(65, 65, 75, 0.95),"
        "                               stop:1 rgba(55, 55, 65, 1.0));"
        "    border-color: rgba(255, 255, 255, 0.25);"
        "    transform: translateY(-2px);"
        "    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4),"
        "                0 4px 20px rgba(255, 255, 255, 0.08);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(25, 25, 35, 0.9),"
        "                               stop:1 rgba(15, 15, 25, 0.95));"
        "    transform: translateY(1px);"
        "}"
        "QPushButton#primaryBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(138, 43, 226, 0.9),"
        "                               stop:0.5 rgba(30, 144, 255, 0.9),"
        "                               stop:1 rgba(0, 191, 255, 0.9));"
        "    color: rgba(255, 255, 255, 0.98);"
        "    font-weight: 700;"
        "    border: 1px solid rgba(255, 255, 255, 0.2);"
        "}"
        "QPushButton#primaryBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(158, 63, 246, 1.0),"
        "                               stop:0.5 rgba(50, 164, 255, 1.0),"
        "                               stop:1 rgba(20, 211, 255, 1.0));"
        "    box-shadow: 0 16px 50px rgba(138, 43, 226, 0.4),"
        "                0 8px 30px rgba(30, 144, 255, 0.3);"
        "}"
        "QPushButton#dangerBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(255, 59, 92, 0.9),"
        "                               stop:1 rgba(255, 69, 58, 0.9));"
        "    color: rgba(255, 255, 255, 0.98);"
        "    font-weight: 700;"
        "}"
        "QPushButton#dangerBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(255, 79, 112, 1.0),"
        "                               stop:1 rgba(255, 89, 78, 1.0));"
        "    box-shadow: 0 16px 50px rgba(255, 59, 92, 0.4);"
        "}"
        "QPushButton#successBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(52, 199, 89, 0.9),"
        "                               stop:1 rgba(48, 209, 88, 0.9));"
        "    color: rgba(255, 255, 255, 0.98);"
        "    font-weight: 700;"
        "}"
        "QPushButton#successBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(72, 219, 109, 1.0),"
        "                               stop:1 rgba(68, 229, 108, 1.0));"
        "    box-shadow: 0 16px 50px rgba(52, 199, 89, 0.4);"
        "}"
        "QComboBox {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(40, 40, 50, 0.9),"
        "                               stop:1 rgba(30, 30, 40, 0.95));"
        "    color: rgba(255, 255, 255, 0.9);"
        "    border: 1px solid rgba(255, 255, 255, 0.15);"
        "    border-radius: 12px;"
        "    padding: 12px 16px;"
        "    font-size: 13px;"
        "    font-weight: 600;"
        "    min-width: 140px;"
        "    backdrop-filter: blur(8px);"
        "}"
        "QComboBox:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(60, 60, 70, 0.95),"
        "                               stop:1 rgba(50, 50, 60, 1.0));"
        "    border-color: rgba(255, 255, 255, 0.25);"
        "}"
        "QComboBox::drop-down {"
        "    border: none;"
        "    width: 24px;"
        "}"
        "QComboBox::down-arrow {"
        "    image: none;"
        "    border-left: 6px solid transparent;"
        "    border-right: 6px solid transparent;"
        "    border-top: 8px solid rgba(255, 255, 255, 0.8);"
        "}"
        "QComboBox QAbstractItemView {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(35, 35, 45, 0.98),"
        "                               stop:1 rgba(25, 25, 35, 1.0));"
        "    border: 1px solid rgba(255, 255, 255, 0.2);"
        "    border-radius: 12px;"
        "    selection-background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
        "                                              stop:0 rgba(138, 43, 226, 0.8),"
        "                                              stop:1 rgba(30, 144, 255, 0.8));"
        "    backdrop-filter: blur(20px);"
        "}"
        "QCheckBox {"
        "    font-size: 13px;"
        "    font-weight: 600;"
        "    spacing: 12px;"
        "    color: rgba(255, 255, 255, 0.9);"
        "    letter-spacing: 0.2px;"
        "}"
        "QCheckBox::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border: 2px solid rgba(255, 255, 255, 0.3);"
        "    border-radius: 6px;"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(30, 30, 40, 0.9),"
        "                               stop:1 rgba(20, 20, 30, 0.95));"
        "    backdrop-filter: blur(5px);"
        "}"
        "QCheckBox::indicator:hover {"
        "    border-color: rgba(138, 43, 226, 0.8);"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(40, 40, 50, 0.95),"
        "                               stop:1 rgba(30, 30, 40, 1.0));"
        "}"
        "QCheckBox::indicator:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,"
        "                               stop:0 rgba(138, 43, 226, 1.0),"
        "                               stop:1 rgba(30, 144, 255, 1.0));"
        "    border-color: rgba(138, 43, 226, 1.0);"
        "}"
        "QGroupBox {"
        "    font-weight: 700;"
        "    font-size: 14px;"
        "    color: rgba(255, 255, 255, 0.95);"
        "    border: 1px solid rgba(255, 255, 255, 0.15);"
        "    border-radius: 16px;"
        "    margin-top: 16px;"
        "    padding-top: 12px;"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "                               stop:0 rgba(25, 25, 35, 0.6),"
        "                               stop:1 rgba(15, 15, 25, 0.8));"
        "    backdrop-filter: blur(8px);"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 16px;"
        "    padding: 0 12px 0 12px;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
        "                               stop:0 rgba(138, 43, 226, 0.8),"
        "                               stop:1 rgba(30, 144, 255, 0.8));"
        "    border-radius: 8px;"
        "    color: rgba(255, 255, 255, 0.98);"
        "    font-weight: 700;"
        "}"
                 "QPushButton#closeBtn {"
         "    background: rgba(255, 59, 92, 1.0);"
         "    border: 2px solid rgba(255, 255, 255, 0.4);"
         "    border-radius: 14px;"
         "    color: rgba(0, 0, 0, 1.0);"
         "    font-size: 18px;"
         "    font-weight: 900;"
         "    text-align: center;"
         "    font-family: Arial, sans-serif;"
         "    line-height: 1.0;"
         "}"
         "QPushButton#closeBtn:hover {"
         "    background: rgba(255, 79, 112, 1.0);"
         "    border-color: rgba(255, 255, 255, 0.6);"
         "    color: rgba(0, 0, 0, 1.0);"
         "    transform: scale(1.05);"
         "}"
         "QPushButton#closeBtn:pressed {"
         "    background: rgba(220, 40, 70, 1.0);"
         "    color: rgba(0, 0, 0, 1.0);"
         "    transform: scale(0.95);"
         "}"
    );
    
         // Main container with glassmorphism effect
     QWidget *mainContainer = new QWidget(m_curveAdjustorDialog);
     mainContainer->setObjectName("mainContainer");
     mainContainer->setGeometry(12, 12, 726, 756);
     
     // Create a simple drag event filter class inline
     class DragEventFilter : public QObject {
     public:
         DragEventFilter(QWidget *parent) : QObject(parent), m_dragging(false) {}
         
     protected:
         bool eventFilter(QObject *obj, QEvent *event) override {
             QWidget *widget = qobject_cast<QWidget*>(obj);
             if (!widget) return false;
             
             if (event->type() == QEvent::MouseButtonPress) {
                 QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
                 if (mouseEvent->button() == Qt::LeftButton) {
                     // Only start dragging if the click is on the title area (top 60 pixels)
                     // and not on a button or other interactive widget
                     QWidget *childWidget = widget->childAt(mouseEvent->pos());
                     if (childWidget && (qobject_cast<QPushButton*>(childWidget) || 
                                        qobject_cast<QComboBox*>(childWidget) ||
                                        qobject_cast<QCheckBox*>(childWidget) ||
                                        qobject_cast<QSlider*>(childWidget))) {
                         // Don't intercept clicks on interactive widgets
                         return false;
                     }
                     
                     // Only allow dragging from the top title area
                     if (mouseEvent->pos().y() <= 60) {
                         m_dragging = true;
                         m_dragStartPos = mouseEvent->globalPos() - widget->pos();
                         return true;
                     }
                     return false;
                 }
             }
             else if (event->type() == QEvent::MouseMove && m_dragging) {
                 QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
                 widget->move(mouseEvent->globalPos() - m_dragStartPos);
                 return true;
             }
             else if (event->type() == QEvent::MouseButtonRelease) {
                 if (m_dragging) {
                     m_dragging = false;
                     return true;
                 }
             }
             
             return QObject::eventFilter(obj, event);
         }
         
     private:
         bool m_dragging;
         QPoint m_dragStartPos;
     };
     
     // Install the drag event filter on the dialog
     m_curveAdjustorDialog->installEventFilter(new DragEventFilter(m_curveAdjustorDialog));
    
         QVBoxLayout *mainLayout = new QVBoxLayout(mainContainer);
     mainLayout->setContentsMargins(16, 16, 16, 16);
     mainLayout->setSpacing(12);
    
         // Draggable title area with close button (no title bar)
     QHBoxLayout *titleBarLayout = new QHBoxLayout();
     titleBarLayout->setContentsMargins(0, 0, 0, 0);
     
     QLabel *titleLabel = new QLabel("🎯 Curve Adjustor");
     titleLabel->setObjectName("titleLabel");
     titleLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
     titleLabel->setCursor(Qt::SizeAllCursor); // Indicate draggable area
     titleBarLayout->addWidget(titleLabel);
     
     titleBarLayout->addStretch();
     
     // Custom close button - Red circle with BLACK X
     QPushButton *closeBtn = new QPushButton("✕");
     closeBtn->setObjectName("closeBtn");
     closeBtn->setFixedSize(28, 28);
     closeBtn->setCursor(Qt::PointingHandCursor);
     closeBtn->setFlat(false);
     closeBtn->setToolTip("Close");
     closeBtn->setFocusPolicy(Qt::NoFocus); // Remove focus rectangle
     titleBarLayout->addWidget(closeBtn);
     
     mainLayout->addLayout(titleBarLayout);
    
         // Subtitle with animation hint
     QLabel *subtitleLabel = new QLabel("Professional-grade curve adjustment with real-time preview and advanced color grading");
     subtitleLabel->setObjectName("subtitleLabel");
     subtitleLabel->setAlignment(Qt::AlignCenter);
     subtitleLabel->setWordWrap(true);
     mainLayout->addWidget(subtitleLabel);
     
     // Professional curve widget with enhanced styling - DECLARE FIRST
     ProfessionalCurveWidget *curveWidget = new ProfessionalCurveWidget();
     curveWidget->setFixedSize(550, 380);
     curveWidget->setStyleSheet(
         "ProfessionalCurveWidget {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(20, 20, 28, 0.95),"
         "                               stop:1 rgba(12, 12, 18, 0.98));"
         "    border: 2px solid rgba(138, 43, 226, 0.4);"
         "    border-radius: 16px;"
         "    backdrop-filter: blur(15px);"
         "}"
     );
     
          // Advanced controls in modern card layout - MOVED ABOVE CURVE
     QHBoxLayout *controlsLayout = new QHBoxLayout();
     controlsLayout->setSpacing(16);
     controlsLayout->setContentsMargins(0, 0, 0, 16);
     
     // Curve Mode Group - Clean design with just dropdowns
     QGroupBox *curveModeGroup = new QGroupBox("🎨 Modes");
     curveModeGroup->setMaximumHeight(120);
     QVBoxLayout *modeLayout = new QVBoxLayout(curveModeGroup);
     modeLayout->setSpacing(8);
     modeLayout->setContentsMargins(12, 20, 12, 12);
     
     QComboBox *modeCombo = new QComboBox();
     modeCombo->addItems({"🌈 RGB Composite", "🔴 Red Channel", "🟢 Green Channel", "🔵 Blue Channel", "⚫ Luminance"});
     modeCombo->setCurrentIndex(0);
     modeCombo->setMaximumHeight(32);
     modeCombo->setStyleSheet(
         "QComboBox {"
         "    background: rgba(45, 45, 50, 0.9);"
         "    border: 1px solid rgba(138, 43, 226, 0.3);"
         "    border-radius: 6px;"
         "    padding: 4px 8px;"
         "    color: white;"
         "    font-size: 11px;"
         "}"
         "QComboBox::drop-down {"
         "    border: none;"
         "    width: 20px;"
         "}"
         "QComboBox::down-arrow {"
         "    image: none;"
         "    border-left: 4px solid transparent;"
         "    border-right: 4px solid transparent;"
         "    border-top: 4px solid white;"
         "    margin-right: 4px;"
         "}"
         "QComboBox QAbstractItemView {"
         "    background: rgba(45, 45, 50, 0.95);"
         "    border: 1px solid rgba(138, 43, 226, 0.4);"
         "    selection-background-color: rgba(138, 43, 226, 0.3);"
         "    color: white;"
         "}"
     );
     modeLayout->addWidget(modeCombo);
     
     QComboBox *typeCombo = new QComboBox();
     typeCombo->addItems({"📈 Point Curve", "🎛️ Parametric", "🎨 Hue vs Saturation", "💡 Hue vs Luminance", "🌓 Luminance vs Saturation"});
     typeCombo->setCurrentIndex(0); // Changed to Point Curve as default
     typeCombo->setMaximumHeight(32);
     typeCombo->setStyleSheet(
         "QComboBox {"
         "    background: rgba(45, 45, 50, 0.9);"
         "    border: 1px solid rgba(30, 144, 255, 0.3);"
         "    border-radius: 6px;"
         "    padding: 4px 8px;"
         "    color: white;"
         "    font-size: 11px;"
         "}"
         "QComboBox::drop-down {"
         "    border: none;"
         "    width: 20px;"
         "}"
         "QComboBox::down-arrow {"
         "    image: none;"
         "    border-left: 4px solid transparent;"
         "    border-right: 4px solid transparent;"
         "    border-top: 4px solid white;"
         "    margin-right: 4px;"
         "}"
         "QComboBox QAbstractItemView {"
         "    background: rgba(45, 45, 50, 0.95);"
         "    border: 1px solid rgba(30, 144, 255, 0.4);"
         "    selection-background-color: rgba(30, 144, 255, 0.3);"
         "    color: white;"
         "}"
     );
     modeLayout->addWidget(typeCombo);
     
     // Connect dropdown signals - single connections only
     connect(modeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
             [curveWidget](int index) {
                 qDebug() << "Mode changed to index:" << index;
                 curveWidget->setCurveMode(static_cast<ProfessionalCurveWidget::CurveMode>(index));
                 curveWidget->update(); // Force immediate repaint
             });
     
     connect(typeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
             [curveWidget](int index) {
                 qDebug() << "Type changed to index:" << index;
                 curveWidget->setCurveType(static_cast<ProfessionalCurveWidget::CurveType>(index));
                 curveWidget->update(); // Force immediate repaint
             });
     
     controlsLayout->addWidget(curveModeGroup);
     
     // Display Options Group - Compact design
     QGroupBox *displayGroup = new QGroupBox("👁️ Display");
     displayGroup->setMaximumHeight(120);
     QHBoxLayout *displayLayout = new QHBoxLayout(displayGroup);
     displayLayout->setSpacing(8);
     displayLayout->setContentsMargins(12, 20, 12, 12);
     
     // Left column
     QVBoxLayout *leftDisplayLayout = new QVBoxLayout();
     leftDisplayLayout->setSpacing(4);
     
     QCheckBox *histogramCheck = new QCheckBox("📊 Histogram");
     histogramCheck->setChecked(true);
     histogramCheck->setStyleSheet("font-size: 11px;");
     connect(histogramCheck, &QCheckBox::toggled, curveWidget, &ProfessionalCurveWidget::setShowHistogram);
     leftDisplayLayout->addWidget(histogramCheck);
     
     QCheckBox *gridCheck = new QCheckBox("📐 Grid");
     gridCheck->setChecked(true);
     gridCheck->setStyleSheet("font-size: 11px;");
     connect(gridCheck, &QCheckBox::toggled, curveWidget, &ProfessionalCurveWidget::setShowGrid);
     leftDisplayLayout->addWidget(gridCheck);
     
     displayLayout->addLayout(leftDisplayLayout);
     
     // Right column
     QVBoxLayout *rightDisplayLayout = new QVBoxLayout();
     rightDisplayLayout->setSpacing(4);
     
     QCheckBox *clippingCheck = new QCheckBox("⚠️ Clipping");
     clippingCheck->setChecked(false);
     clippingCheck->setStyleSheet("font-size: 11px;");
     connect(clippingCheck, &QCheckBox::toggled, curveWidget, &ProfessionalCurveWidget::setShowClipping);
     rightDisplayLayout->addWidget(clippingCheck);
     
     QCheckBox *snapCheck = new QCheckBox("🧲 Snap");
     snapCheck->setChecked(false);
     snapCheck->setStyleSheet("font-size: 11px;");
     connect(snapCheck, &QCheckBox::toggled, curveWidget, &ProfessionalCurveWidget::setSnapToGrid);
     rightDisplayLayout->addWidget(snapCheck);
     
     displayLayout->addLayout(rightDisplayLayout);
     
     controlsLayout->addWidget(displayGroup);
     
     // Presets Group - Same size as Display and Model groups
     QGroupBox *presetsGroup = new QGroupBox("🎭 Presets");
     presetsGroup->setMaximumHeight(120);
     QVBoxLayout *presetsLayout = new QVBoxLayout(presetsGroup);
     presetsLayout->setSpacing(8);
     presetsLayout->setContentsMargins(12, 20, 12, 12);
     
     QComboBox *presetCombo = new QComboBox();
     presetCombo->addItems({
         "Select Preset...",
         "📏 Linear", 
         "💪 Strong Contrast", 
         "🎬 Cinematic", 
         "🎞️ Film Emulation", 
         "🌫️ Matte Finish", 
         "☀️ High Key", 
         "🌙 Low Key", 
         "📸 Vintage"
     });
     presetCombo->setMaximumHeight(32);
     presetCombo->setStyleSheet(
         "QComboBox {"
         "    background: rgba(45, 45, 50, 0.9);"
         "    border: 1px solid rgba(255, 140, 80, 0.3);"
         "    border-radius: 6px;"
         "    padding: 4px 8px;"
         "    color: white;"
         "    font-size: 11px;"
         "}"
         "QComboBox::drop-down {"
         "    border: none;"
         "    width: 20px;"
         "}"
         "QComboBox::down-arrow {"
         "    image: none;"
         "    border-left: 4px solid transparent;"
         "    border-right: 4px solid transparent;"
         "    border-top: 4px solid white;"
         "    margin-right: 4px;"
         "}"
         "QComboBox QAbstractItemView {"
         "    background: rgba(45, 45, 50, 0.95);"
         "    border: 1px solid rgba(255, 140, 80, 0.4);"
         "    selection-background-color: rgba(255, 140, 80, 0.3);"
         "    color: white;"
         "}"
     );
     connect(presetCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
             [curveWidget, presetCombo](int index) {
                 if (index > 0) { // Skip "Select Preset..." option
                     QString itemText = presetCombo->itemText(index);
                     QString presetName;
                     
                     // Map dropdown items to actual preset names
                     if (itemText.contains("Linear")) {
                         presetName = "Linear";
                     } else if (itemText.contains("Strong Contrast")) {
                         presetName = "Strong Contrast";
                     } else if (itemText.contains("Cinematic")) {
                         presetName = "Cinematic";
                     } else if (itemText.contains("Film Emulation")) {
                         presetName = "Film Emulation";
                     } else if (itemText.contains("Matte Finish")) {
                         presetName = "Matte Finish";
                     } else if (itemText.contains("High Key")) {
                         presetName = "High Key";
                     } else if (itemText.contains("Low Key")) {
                         presetName = "Low Key";
                     } else if (itemText.contains("Vintage")) {
                         presetName = "Vintage";
                     }
                     
                     if (!presetName.isEmpty()) {
                         qDebug() << "Applying preset:" << presetName << "from dropdown item:" << itemText;
                         curveWidget->applyPreset(presetName);
                         
                         // Visual feedback - briefly highlight the combo box
                         QString originalStyle = presetCombo->styleSheet();
                         presetCombo->setStyleSheet(originalStyle + "QComboBox { border: 2px solid #00FF7F; }");
                         
                         // Reset styling after 500ms
                         QTimer::singleShot(500, [presetCombo, originalStyle]() {
                             presetCombo->setStyleSheet(originalStyle);
                         });
                     }
                 }
             });
     presetsLayout->addWidget(presetCombo);
     
     // Create horizontal layout for buttons
     QHBoxLayout *presetButtonsLayout = new QHBoxLayout();
     presetButtonsLayout->setSpacing(8);
     presetButtonsLayout->setContentsMargins(0, 0, 0, 0);
     
     // Add "Apply to All" button for presets
     QPushButton *applyAllBtn = new QPushButton("🎯 All");
     applyAllBtn->setToolTip("Apply current preset to all color channels");
     applyAllBtn->setFixedSize(45, 22);
     applyAllBtn->setStyleSheet(
         "QPushButton {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(255, 140, 0, 0.8),"
         "                               stop:1 rgba(255, 100, 0, 0.8));"
         "    border: none;"
         "    border-radius: 4px;"
         "    color: white;"
         "    font-size: 11px;"
         "    font-weight: 600;"
         "    padding: 1px 4px;"
         "}"
         "QPushButton:hover {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(255, 160, 20, 0.9),"
         "                               stop:1 rgba(255, 120, 20, 0.9));"
         "}"
         "QPushButton:pressed {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(255, 100, 0, 1.0),"
         "                               stop:1 rgba(255, 80, 0, 1.0));"
         "}"
     );
     
     connect(applyAllBtn, &QPushButton::clicked, [curveWidget, presetCombo]() {
         int currentIndex = presetCombo->currentIndex();
         if (currentIndex > 0) {
             QString itemText = presetCombo->itemText(currentIndex);
             QString presetName;
             
             // Map dropdown items to actual preset names
             if (itemText.contains("Linear")) {
                 presetName = "Linear";
             } else if (itemText.contains("Strong Contrast")) {
                 presetName = "Strong Contrast";
             } else if (itemText.contains("Cinematic")) {
                 presetName = "Cinematic";
             } else if (itemText.contains("Film Emulation")) {
                 presetName = "Film Emulation";
             } else if (itemText.contains("Matte Finish")) {
                 presetName = "Matte Finish";
             } else if (itemText.contains("High Key")) {
                 presetName = "High Key";
             } else if (itemText.contains("Low Key")) {
                 presetName = "Low Key";
             } else if (itemText.contains("Vintage")) {
                 presetName = "Vintage";
             }
             
             if (!presetName.isEmpty()) {
                 qDebug() << "Applying preset to all modes:" << presetName;
                 curveWidget->applyPresetToAllModes(presetName);
             }
         }
     });
     
     presetButtonsLayout->addWidget(applyAllBtn);
     
     // Add "Save Preset" button
     QPushButton *savePresetBtn = new QPushButton("💾 Save");
     savePresetBtn->setToolTip("Save current curve as a custom preset");
     savePresetBtn->setFixedSize(55, 22);
     savePresetBtn->setStyleSheet(
         "QPushButton {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(138, 43, 226, 0.8),"
         "                               stop:1 rgba(75, 0, 130, 0.8));"
         "    border: none;"
         "    border-radius: 4px;"
         "    color: white;"
         "    font-size: 11px;"
         "    font-weight: 600;"
         "    padding: 1px 4px;"
         "}"
         "QPushButton:hover {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(158, 63, 246, 0.9),"
         "                               stop:1 rgba(95, 20, 150, 0.9));"
         "}"
         "QPushButton:pressed {"
         "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
         "                               stop:0 rgba(118, 23, 206, 1.0),"
         "                               stop:1 rgba(55, 0, 110, 1.0));"
         "}"
     );
     
     connect(savePresetBtn, &QPushButton::clicked, [curveWidget, presetCombo]() {
         bool ok;
         QString presetName = QInputDialog::getText(nullptr, "Save Preset", 
                                                   "Enter preset name:", QLineEdit::Normal, 
                                                   "Custom Preset", &ok);
         if (ok && !presetName.isEmpty()) {
             curveWidget->saveCurrentAsPreset(presetName);
             
             // Add to combo box if not already present
             bool found = false;
             for (int i = 0; i < presetCombo->count(); i++) {
                 if (presetCombo->itemText(i).contains(presetName)) {
                     found = true;
                     break;
                 }
             }
             if (!found) {
                 presetCombo->addItem("💎 " + presetName);
             }
         }
     });
     
     presetButtonsLayout->addWidget(savePresetBtn);
     presetButtonsLayout->addStretch(); // Push buttons to the left
     
     // Add the button layout to the main presets layout
     presetsLayout->addLayout(presetButtonsLayout);
     
     controlsLayout->addWidget(presetsGroup);
     
     // Add stretch to right-align the groups
     controlsLayout->addStretch();
     
     mainLayout->addLayout(controlsLayout);
     
     // Add the curve widget to layout - ALREADY DECLARED ABOVE
     mainLayout->addWidget(curveWidget, 1, Qt::AlignCenter);
    
         // Modern action buttons with enhanced styling - Compact design
     QHBoxLayout *buttonLayout = new QHBoxLayout();
     buttonLayout->setSpacing(12);
     buttonLayout->setContentsMargins(0, 20, 0, 0);
    
    QPushButton *resetBtn = new QPushButton("🔄 Reset");
    resetBtn->setToolTip("Reset curve to linear state (Ctrl+R)");
    connect(resetBtn, &QPushButton::clicked, curveWidget, &ProfessionalCurveWidget::resetCurve);
    buttonLayout->addWidget(resetBtn);
    
    QPushButton *autoBtn = new QPushButton("🤖 Auto Adjust");
    autoBtn->setObjectName("primaryBtn");
    autoBtn->setToolTip("Intelligent automatic curve adjustment (Ctrl+A)");
    connect(autoBtn, &QPushButton::clicked, curveWidget, &ProfessionalCurveWidget::autoAdjust);
    buttonLayout->addWidget(autoBtn);
    
    buttonLayout->addStretch();
    
    QPushButton *applyBtn = new QPushButton("✨ Apply Changes");
    applyBtn->setObjectName("successBtn");
    applyBtn->setToolTip("Apply curve adjustments to image");
    connect(applyBtn, &QPushButton::clicked, [this, curveWidget]() {
        applyCurveAdjustment(curveWidget->getCurvePoints());
        m_curveAdjustorDialog->close();
        m_curveAdjustorDialog = nullptr;
    });
    buttonLayout->addWidget(applyBtn);
    
    QPushButton *cancelBtn = new QPushButton("❌ Cancel");
    cancelBtn->setObjectName("dangerBtn");
    cancelBtn->setToolTip("Cancel and revert changes");
    connect(cancelBtn, &QPushButton::clicked, [this]() {
        if (!m_originalImage.isNull()) {
            m_currentImage = m_originalImage;
            updateImagePreview();
        }
        m_curveAdjustorDialog->close();
        m_curveAdjustorDialog = nullptr;
    });
    buttonLayout->addWidget(cancelBtn);
    
    mainLayout->addLayout(buttonLayout);
    
         // Keyboard shortcuts info with modern styling - Readable size
     QLabel *shortcutsLabel = new QLabel(
         "⌨️ <b>Shortcuts:</b> 1-5 (Channels) • Ctrl+R (Reset) • Ctrl+A (Auto) • Del (Remove) • Shift+Drag (Fine)"
     );
     shortcutsLabel->setStyleSheet(
         "font-size: 13px;"
         "color: rgba(255, 255, 255, 0.7);"
         "background: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
         "                           stop:0 rgba(138, 43, 226, 0.1),"
         "                           stop:1 rgba(30, 144, 255, 0.1));"
         "border-radius: 8px;"
         "padding: 12px 16px;"
         "margin-top: 10px;"
         "font-weight: 500;"
         "letter-spacing: 0.2px;"
     );
     shortcutsLabel->setAlignment(Qt::AlignCenter);
     mainLayout->addWidget(shortcutsLabel);
    
    // Generate histogram from current image
    if (!m_originalImage.isNull()) {
        QImage image = m_originalImage.toImage();
        QVector<int> histogram(256, 0);
        
        for (int y = 0; y < image.height(); y++) {
            QRgb *line = reinterpret_cast<QRgb*>(image.scanLine(y));
            for (int x = 0; x < image.width(); x++) {
                QRgb pixel = line[x];
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Calculate luminance for histogram
                int luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                histogram[qBound(0, luminance, 255)]++;
            }
        }
        
        // Set histogram data to curve widget
        curveWidget->setHistogramData(histogram);
    }
    
    // Connect signals for live preview with performance optimization
    connect(curveWidget, &ProfessionalCurveWidget::curveChanged, [this, curveWidget]() {
        // Fast live preview while adjusting curves
        applyCurveAdjustmentOptimized(curveWidget);
    });
    
    // Close button functionality
    auto closeDialog = [this]() {
        if (!m_originalImage.isNull()) {
            m_currentImage = m_originalImage;
            updateImagePreview();
        }
        m_curveAdjustorDialog->close();
        m_curveAdjustorDialog = nullptr;
    };
    
              // Make close button clickable with proper event handling
     connect(closeBtn, &QPushButton::clicked, closeDialog);
    
    connect(m_curveAdjustorDialog, &QWidget::destroyed, [this]() {
        m_curveAdjustorDialog = nullptr;
    });
    
    // Set focus for keyboard shortcuts
    curveWidget->setFocusPolicy(Qt::StrongFocus);
    curveWidget->setFocus();
    
    // Center dialog with smooth animation potential
    QRect parentGeometry = this->geometry();
    int x = parentGeometry.x() + (parentGeometry.width() - m_curveAdjustorDialog->width()) / 2;
    int y = parentGeometry.y() + (parentGeometry.height() - m_curveAdjustorDialog->height()) / 2;
    m_curveAdjustorDialog->move(x, y);
    
    // Show with modern effects
    m_curveAdjustorDialog->show();
    m_curveAdjustorDialog->raise();
    m_curveAdjustorDialog->activateWindow();
}

// Ultra-fast optimized curve adjustment with professional performance
void Zview::applyCurveAdjustmentOptimized(ProfessionalCurveWidget *curveWidget) {
    if (m_originalImage.isNull() || !curveWidget) {
        return;
    }
    
    // Get current curve parameters
    ProfessionalCurveWidget::CurveMode currentMode = curveWidget->getCurrentMode();
    ProfessionalCurveWidget::CurveType currentType = curveWidget->getCurrentType();
    QVector<QPoint> curvePoints = curveWidget->getCurvePoints();
    
    // Check if curve is linear (no processing needed)
    bool isLinearCurve = true;
    if (curvePoints.size() >= 2) {
        for (const QPoint &point : curvePoints) {
            if (abs(point.x() - point.y()) > 2) {
                isLinearCurve = false;
                break;
            }
        }
    }
    
    if (isLinearCurve) {
        m_currentImage = m_originalImage;
        m_textureNeedsUpdate = true;
        update();
        return;
    }
    
    // Generate cache key for curve parameters
    QString curveKey = QString("curve_%1_%2_%3").arg(int(currentMode)).arg(int(currentType)).arg(curvePoints.size());
    for (const QPoint &pt : curvePoints) {
        curveKey += QString("_%1_%2").arg(pt.x()).arg(pt.y());
    }
    
    // Check if result is cached
    if (m_performanceCache.contains(curveKey)) {
        m_currentImage = QPixmap::fromImage(m_performanceCache.value(curveKey));
        m_textureNeedsUpdate = true;
        update();
        return;
    }
    
    // Use high-quality resolution for live editing - NO PIXELATION
    QSize previewSize = m_originalImage.size();
    if (previewSize.width() > 2560 || previewSize.height() > 1440) {
        previewSize = previewSize.scaled(2560, 1440, Qt::KeepAspectRatio);  // Increased from 1920x1080
    }
    
    // Process performance-optimized preview
    QImage sourceImage = m_originalImage.toImage();
    if (sourceImage.size() != previewSize) {
        sourceImage = getOptimizedScaling(sourceImage, previewSize, true);
    }
    
    // Pre-calculate all lookup tables
    QVector<QVector<int>> lookupTables(5);
    for (int channel = 0; channel < 5; channel++) {
        if (channel < curveWidget->m_curvePoints.size()) {
            lookupTables[channel] = createOptimizedLookupTable(curveWidget->m_curvePoints[channel]);
        } else {
            lookupTables[channel].resize(256);
            for (int i = 0; i < 256; i++) {
                lookupTables[channel][i] = i;
            }
        }
    }
    
    // Ultra-fast pixel processing with optimized algorithms
    QImage result = applyCurveToImageOptimized(sourceImage, lookupTables, int(currentMode), int(currentType));
    
    // Scale back to original size if needed
    if (result.size() != m_originalImage.size()) {
        result = result.scaled(m_originalImage.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    
    QPixmap resultPixmap = QPixmap::fromImage(result);
    
    // Cache the result for instant reuse
    m_performanceCache.insert(curveKey, resultPixmap.toImage());
    
    m_currentImage = resultPixmap;
    m_textureNeedsUpdate = true;
    update();
}

// Optimized curve application with professional algorithms
QImage Zview::applyCurveToImageOptimized(const QImage &image, const QVector<QVector<int>> &lookupTables, 
                                        int mode, int type) {
    QImage result = image;
    
    // Pre-computed HSV conversion tables for speed
    static QVector<QVector<int>> hsvCache(3, QVector<int>(256*256*256, -1));
    
    if (type == 2 || type == 3 || type == 4) { // HUE_VS_SAT, HUE_VS_LUM, LUM_VS_SAT
        
        // HSV-based processing with optimized color space conversion
        for (int y = 0; y < result.height(); y++) {
            QRgb *line = reinterpret_cast<QRgb*>(result.scanLine(y));
            for (int x = 0; x < result.width(); x++) {
                QRgb pixel = line[x];
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                // Fast RGB to HSV conversion (optimized algorithm)
                int h, s, v;
                Zview::rgbToHsvFast(r, g, b, h, s, v);
                
                                 switch (type) {
                     case 2: // HUE_VS_SAT
                         if (h >= 0) {
                             int hueIndex = qBound(0, (h * 255) / 359, 255);
                             s = qBound(0, lookupTables[0][hueIndex], 255);
                         }
                         break;
                         
                     case 3: // HUE_VS_LUM
                         if (h >= 0) {
                             int hueIndex = qBound(0, (h * 255) / 359, 255);
                             v = qBound(0, lookupTables[0][hueIndex], 255);
                         }
                         break;
                         
                     case 4: // LUM_VS_SAT
                         s = qBound(0, lookupTables[0][v], 255);
                         break;
                        
                    default:
                        break;
                }
                
                // Fast HSV to RGB conversion
                Zview::hsvToRgbFast(h, s, v, r, g, b);
                line[x] = qRgb(r, g, b);
            }
        }
        
    } else {
        // RGB/Luminance processing (fastest path)
        for (int y = 0; y < result.height(); y++) {
            QRgb *line = reinterpret_cast<QRgb*>(result.scanLine(y));
            for (int x = 0; x < result.width(); x++) {
                QRgb pixel = line[x];
                int r = qRed(pixel);
                int g = qGreen(pixel);
                int b = qBlue(pixel);
                
                                 switch (mode) {
                     case 0: // RGB_COMPOSITE
                         r = lookupTables[0][r];
                         g = lookupTables[0][g];
                         b = lookupTables[0][b];
                         break;
                         
                     case 1: // RED_CHANNEL
                         r = lookupTables[1][r];
                         break;
                         
                     case 2: // GREEN_CHANNEL
                         g = lookupTables[2][g];
                         break;
                         
                     case 3: // BLUE_CHANNEL
                         b = lookupTables[3][b];
                         break;
                         
                     case 4: { // LUMINANCE_ONLY
                        // Fast luminance calculation
                        int lum = (299 * r + 587 * g + 114 * b) / 1000;
                        int newLum = lookupTables[4][qBound(0, lum, 255)];
                        if (lum > 0) {
                            float factor = float(newLum) / float(lum);
                            r = qBound(0, int(r * factor), 255);
                            g = qBound(0, int(g * factor), 255);
                            b = qBound(0, int(b * factor), 255);
                        }
                        break;
                    }
                }
                
                line[x] = qRgb(r, g, b);
            }
        }
    }
    
    return result;
}

// Ultra-fast RGB to HSV conversion (optimized algorithm)
void Zview::rgbToHsvFast(int r, int g, int b, int &h, int &s, int &v) {
    int max_val = qMax(r, qMax(g, b));
    int min_val = qMin(r, qMin(g, b));
    int delta = max_val - min_val;
    
    v = max_val;
    
    if (max_val == 0) {
        s = 0;
        h = -1;
        return;
    }
    
    s = (delta * 255) / max_val;
    
    if (delta == 0) {
        h = 0;
        return;
    }
    
    if (max_val == r) {
        h = (60 * (g - b)) / delta;
    } else if (max_val == g) {
        h = 120 + (60 * (b - r)) / delta;
    } else {
        h = 240 + (60 * (r - g)) / delta;
    }
    
    if (h < 0) h += 360;
}

// Ultra-fast HSV to RGB conversion (optimized algorithm)
void Zview::hsvToRgbFast(int h, int s, int v, int &r, int &g, int &b) {
    if (s == 0) {
        r = g = b = v;
        return;
    }
    
    int sector = h / 60;
    int remainder = (h % 60) * 255 / 60;
    
    int p = (v * (255 - s)) / 255;
    int q = (v * (255 - (s * remainder) / 255)) / 255;
    int t = (v * (255 - (s * (255 - remainder)) / 255)) / 255;
    
    switch (sector) {
        case 0: r = v; g = t; b = p; break;
        case 1: r = q; g = v; b = p; break;
        case 2: r = p; g = v; b = t; break;
        case 3: r = p; g = q; b = v; break;
        case 4: r = t; g = p; b = v; break;
        default: r = v; g = p; b = q; break;
    }
}

// Optimized lookup table creation
QVector<int> Zview::createOptimizedLookupTable(const QVector<QPoint> &curvePoints) {
    QVector<int> lookupTable(256);
    
    if (curvePoints.size() < 2) {
        for (int i = 0; i < 256; i++) {
            lookupTable[i] = i;
        }
        return lookupTable;
    }
    
    // Sort points by x coordinate
    QVector<QPoint> sortedPoints = curvePoints;
    std::sort(sortedPoints.begin(), sortedPoints.end(), [](const QPoint &a, const QPoint &b) {
        return a.x() < b.x();
    });
    
    // Fast linear interpolation (much faster than cubic spline for live preview)
    for (int x = 0; x <= 255; x++) {
        int y = calculateLinearInterpolation(x, sortedPoints);
        lookupTable[x] = qBound(0, y, 255);
    }
    
    return lookupTable;
}

// Fast linear interpolation for live preview
int Zview::calculateLinearInterpolation(int x, const QVector<QPoint> &points) {
    if (points.size() < 2) return x;
    
    // Find the segment containing x
    int i = 0;
    while (i < points.size() - 1 && points[i + 1].x() <= x) {
        i++;
    }
    
    if (i >= points.size() - 1) {
        return points.last().y();
    }
    
    QPoint p1 = points[i];
    QPoint p2 = points[i + 1];
    
    if (p2.x() == p1.x()) {
        return p1.y();
    }
    
    // Simple linear interpolation
    float t = float(x - p1.x()) / float(p2.x() - p1.x());
    int y = p1.y() + t * (p2.y() - p1.y());
    return qBound(0, y, 255);
}

// Keep original function for final high-quality processing
void Zview::applyCurveAdjustment(const QVector<QPoint> &curvePoints) {
    // This function is now used for final high-quality processing only
    // Live preview uses the optimized version above
    
    if (m_originalImage.isNull() || curvePoints.size() < 2) {
        return;
    }
    
    // Check if the curve is actually modified (not linear)
    bool isLinearCurve = true;
    if (curvePoints.size() >= 2) {
        for (const QPoint &point : curvePoints) {
            if (abs(point.x() - point.y()) > 2) {
                isLinearCurve = false;
                break;
            }
        }
    }
    
    if (isLinearCurve) {
        m_currentImage = m_originalImage;
        m_textureNeedsUpdate = true;
        update();
        return;
    }
    
    // Get the current curve widget to access mode information
    ProfessionalCurveWidget *curveWidget = nullptr;
    if (m_curveAdjustorDialog) {
        curveWidget = m_curveAdjustorDialog->findChild<ProfessionalCurveWidget*>();
    }
    
    if (!curveWidget) {
        return;
    }
    
    // Use the optimized version for better performance
    applyCurveAdjustmentOptimized(curveWidget);
}

// Helper function to create lookup table from curve points
QVector<int> Zview::createLookupTable(const QVector<QPoint> &curvePoints) {
    QVector<int> lookupTable(256);
    
    if (curvePoints.size() < 2) {
        // Linear fallback
        for (int i = 0; i < 256; i++) {
            lookupTable[i] = i;
        }
        return lookupTable;
    }
    
    // Sort points by x coordinate
    QVector<QPoint> sortedPoints = curvePoints;
    std::sort(sortedPoints.begin(), sortedPoints.end(), [](const QPoint &a, const QPoint &b) {
        return a.x() < b.x();
    });
    
    // Create smooth interpolation using cubic spline
    for (int x = 0; x <= 255; x++) {
        int y = calculateCubicSplineValue(x, sortedPoints);
        lookupTable[x] = qBound(0, y, 255); // Direct mapping, no inversion needed
    }
    
    return lookupTable;
}

// Helper function for cubic spline interpolation
int Zview::calculateCubicSplineValue(int x, const QVector<QPoint> &points) {
    if (points.size() < 2) return x;
    
    // Find the segment containing x
    int i = 0;
    while (i < points.size() - 1 && points[i + 1].x() <= x) {
        i++;
    }
    
    if (i >= points.size() - 1) {
        return points.last().y();
    }
    
    QPoint p1 = points[i];
    QPoint p2 = points[i + 1];
    
    if (p2.x() == p1.x()) {
        return p1.y();
    }
    
    // Smooth interpolation using smoothstep function
    float t = float(x - p1.x()) / float(p2.x() - p1.x());
    t = t * t * (3.0f - 2.0f * t); // Smoothstep function
    
    int y = p1.y() + t * (p2.y() - p1.y());
    return qBound(0, y, 255);
}

// ===== PROFESSIONAL ANTI-PIXELATION FUNCTIONS =====

QImage Zview::applyProfessionalScaling(const QImage &source, const QSize &targetSize, bool highQuality) {
    if (source.isNull() || targetSize.isEmpty()) {
        return source;
    }
    
    // Use optimized scaling for better performance
    return getOptimizedScaling(source, targetSize, m_isInteractiveMode);
}

QImage Zview::getOptimizedScaling(const QImage &source, const QSize &targetSize, bool isInteractive) {
    if (source.isNull() || targetSize.isEmpty()) {
        return source;
    }
    
    // PERFORMANCE-FIRST APPROACH: Use fast algorithms during interaction
    if (isInteractive || m_usePerformanceMode) {
        // Fast mode: Use Qt's optimized SmoothTransformation (still high quality)
        return source.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    
    // HIGH-QUALITY MODE: Use professional algorithms when not interactive
    switch (m_scalingQuality) {
        case ScalingQuality::DRAFT:
            return source.scaled(targetSize, Qt::KeepAspectRatio, Qt::FastTransformation);
        case ScalingQuality::GOOD:
            return source.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        case ScalingQuality::BETTER:
            return source.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        case ScalingQuality::BEST:
            // Only use advanced algorithms for final output
            return applyBicubicInterpolation(source, targetSize);
        case ScalingQuality::PROFESSIONAL:
            // Use the most advanced algorithm for professional quality
            if (targetSize.width() > source.width() || targetSize.height() > source.height()) {
                return applyMitchellNetravaliFilter(source, targetSize);
            } else {
                return applyLanczos3Resampling(source, targetSize);
            }
        case ScalingQuality::AUTO:
        default:
            // Intelligent selection based on size and performance needs
            double scaleFactor = (double)targetSize.width() / source.width();
            
            if (scaleFactor > 2.0) {
                // Large upscaling: Use professional algorithm
                return applyMitchellNetravaliFilter(source, targetSize);
            } else if (scaleFactor < 0.5) {
                // Large downscaling: Use Lanczos-3
                return applyLanczos3Resampling(source, targetSize);
            } else {
                // Moderate scaling: Use fast smooth transformation
                return source.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            }
    }
}

QImage Zview::applyBicubicInterpolation(const QImage &source, const QSize &targetSize) {
    if (source.isNull() || targetSize.isEmpty()) {
        return source;
    }
    
    // Professional bicubic interpolation implementation
    QImage srcImage = source.format() == QImage::Format_ARGB32 ? source : source.convertToFormat(QImage::Format_ARGB32);
    QImage result(targetSize, QImage::Format_ARGB32);
    
    const int srcWidth = srcImage.width();
    const int srcHeight = srcImage.height();
    const int dstWidth = targetSize.width();
    const int dstHeight = targetSize.height();
    
    const double xRatio = (double)srcWidth / dstWidth;
    const double yRatio = (double)srcHeight / dstHeight;
    
    // Professional bicubic interpolation with Mitchell-Netravali weights
    for (int y = 0; y < dstHeight; ++y) {
        QRgb* dstLine = reinterpret_cast<QRgb*>(result.scanLine(y));
        const double srcY = y * yRatio;
        const int srcYInt = (int)srcY;
        const double yFrac = srcY - srcYInt;
        
        for (int x = 0; x < dstWidth; ++x) {
            const double srcX = x * xRatio;
            const int srcXInt = (int)srcX;
            const double xFrac = srcX - srcXInt;
            
            // Sample 4x4 neighborhood for professional bicubic
            double r = 0, g = 0, b = 0, a = 0;
            double totalWeight = 0;
            
            for (int dy = -1; dy <= 2; ++dy) {
                for (int dx = -1; dx <= 2; ++dx) {
                    int sampleX = qBound(0, srcXInt + dx, srcWidth - 1);
                    int sampleY = qBound(0, srcYInt + dy, srcHeight - 1);
                    
                    QRgb pixel = srcImage.pixel(sampleX, sampleY);
                    
                    // Use Mitchell-Netravali kernel for professional quality
                    double weightX = mitchellNetravaliKernel(xFrac - dx);
                    double weightY = mitchellNetravaliKernel(yFrac - dy);
                    double weight = weightX * weightY;
                    
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                    a += qAlpha(pixel) * weight;
                    totalWeight += weight;
                }
            }
            
            if (totalWeight > 0) {
                r /= totalWeight;
                g /= totalWeight;
                b /= totalWeight;
                a /= totalWeight;
            }
            
            dstLine[x] = qRgba(qBound(0, (int)r, 255), qBound(0, (int)g, 255), 
                              qBound(0, (int)b, 255), qBound(0, (int)a, 255));
        }
    }
    
    return result;
}

QImage Zview::applyLanczosResampling(const QImage &source, const QSize &targetSize) {
    // Redirect to professional Lanczos-3 implementation
    return applyLanczos3Resampling(source, targetSize);
}

QImage Zview::applyLanczos3Resampling(const QImage &source, const QSize &targetSize) {
    if (source.isNull() || targetSize.isEmpty()) {
        return source;
    }
    
    // Professional Lanczos-3 resampling (industry standard)
    QImage srcImage = source.format() == QImage::Format_ARGB32 ? source : source.convertToFormat(QImage::Format_ARGB32);
    QImage result(targetSize, QImage::Format_ARGB32);
    
    const int srcWidth = srcImage.width();
    const int srcHeight = srcImage.height();
    const int dstWidth = targetSize.width();
    const int dstHeight = targetSize.height();
    
    const double xRatio = (double)srcWidth / dstWidth;
    const double yRatio = (double)srcHeight / dstHeight;
    
    // Lanczos-3 has support of 3 pixels
    const int support = 3;
    
    for (int y = 0; y < dstHeight; ++y) {
        QRgb* dstLine = reinterpret_cast<QRgb*>(result.scanLine(y));
        const double srcY = y * yRatio;
        const int srcYInt = (int)srcY;
        const double yFrac = srcY - srcYInt;
        
        for (int x = 0; x < dstWidth; ++x) {
            const double srcX = x * xRatio;
            const int srcXInt = (int)srcX;
            const double xFrac = srcX - srcXInt;
            
            // Sample 6x6 neighborhood for Lanczos-3
            double r = 0, g = 0, b = 0, a = 0;
            double totalWeight = 0;
            
            for (int dy = -support; dy <= support; ++dy) {
                for (int dx = -support; dx <= support; ++dx) {
                    int sampleX = qBound(0, srcXInt + dx, srcWidth - 1);
                    int sampleY = qBound(0, srcYInt + dy, srcHeight - 1);
                    
                    QRgb pixel = srcImage.pixel(sampleX, sampleY);
                    
                    // Professional Lanczos-3 kernel
                    double weightX = lanczosKernel(xFrac - dx, 3);
                    double weightY = lanczosKernel(yFrac - dy, 3);
                    double weight = weightX * weightY;
                    
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                    a += qAlpha(pixel) * weight;
                    totalWeight += weight;
                }
            }
            
            if (totalWeight > 0) {
                r /= totalWeight;
                g /= totalWeight;
                b /= totalWeight;
                a /= totalWeight;
            }
            
            dstLine[x] = qRgba(qBound(0, (int)r, 255), qBound(0, (int)g, 255), 
                              qBound(0, (int)b, 255), qBound(0, (int)a, 255));
        }
    }
    
    return result;
}

QImage Zview::applyMitchellNetravaliFilter(const QImage &source, const QSize &targetSize) {
    if (source.isNull() || targetSize.isEmpty()) {
        return source;
    }
    
    // Mitchell-Netravali filter - excellent for upscaling (used by professional software)
    QImage srcImage = source.format() == QImage::Format_ARGB32 ? source : source.convertToFormat(QImage::Format_ARGB32);
    QImage result(targetSize, QImage::Format_ARGB32);
    
    const int srcWidth = srcImage.width();
    const int srcHeight = srcImage.height();
    const int dstWidth = targetSize.width();
    const int dstHeight = targetSize.height();
    
    const double xRatio = (double)srcWidth / dstWidth;
    const double yRatio = (double)srcHeight / dstHeight;
    
    // Mitchell-Netravali has support of 2 pixels
    const int support = 2;
    
    for (int y = 0; y < dstHeight; ++y) {
        QRgb* dstLine = reinterpret_cast<QRgb*>(result.scanLine(y));
        const double srcY = y * yRatio;
        const int srcYInt = (int)srcY;
        const double yFrac = srcY - srcYInt;
        
        for (int x = 0; x < dstWidth; ++x) {
            const double srcX = x * xRatio;
            const int srcXInt = (int)srcX;
            const double xFrac = srcX - srcXInt;
            
            // Sample 4x4 neighborhood for Mitchell-Netravali
            double r = 0, g = 0, b = 0, a = 0;
            double totalWeight = 0;
            
            for (int dy = -support; dy <= support; ++dy) {
                for (int dx = -support; dx <= support; ++dx) {
                    int sampleX = qBound(0, srcXInt + dx, srcWidth - 1);
                    int sampleY = qBound(0, srcYInt + dy, srcHeight - 1);
                    
                    QRgb pixel = srcImage.pixel(sampleX, sampleY);
                    
                    // Professional Mitchell-Netravali kernel
                    double weightX = mitchellNetravaliKernel(xFrac - dx);
                    double weightY = mitchellNetravaliKernel(yFrac - dy);
                    double weight = weightX * weightY;
                    
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                    a += qAlpha(pixel) * weight;
                    totalWeight += weight;
                }
            }
            
            if (totalWeight > 0) {
                r /= totalWeight;
                g /= totalWeight;
                b /= totalWeight;
                a /= totalWeight;
            }
            
            dstLine[x] = qRgba(qBound(0, (int)r, 255), qBound(0, (int)g, 255), 
                              qBound(0, (int)b, 255), qBound(0, (int)a, 255));
        }
    }
    
    return result;
}

QImage Zview::applySuperSamplingAntiAliasing(const QImage &source, float scaleFactor) {
    if (source.isNull() || scaleFactor <= 1.0f) {
        return source;
    }
    
    // Super-sampling anti-aliasing (SSAA) - render at higher resolution then downsample
    QSize superSize(source.width() * scaleFactor, source.height() * scaleFactor);
    QImage superSampled = source.scaled(superSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    
    // Apply edge-preserving smoothing to the super-sampled image
    return applyEdgePreservingSmoothing(superSampled);
}

QImage Zview::applyEdgePreservingSmoothing(const QImage &source) {
    if (source.isNull()) {
        return source;
    }
    
    // Edge-preserving smoothing filter (reduces aliasing while preserving sharp edges)
    QImage result = source.convertToFormat(QImage::Format_ARGB32);
    
    for (int y = 1; y < result.height() - 1; ++y) {
        QRgb* line = reinterpret_cast<QRgb*>(result.scanLine(y));
        const QRgb* prevLine = reinterpret_cast<const QRgb*>(source.constScanLine(y - 1));
        const QRgb* currLine = reinterpret_cast<const QRgb*>(source.constScanLine(y));
        const QRgb* nextLine = reinterpret_cast<const QRgb*>(source.constScanLine(y + 1));
        
        for (int x = 1; x < result.width() - 1; ++x) {
            QRgb center = currLine[x];
            
            // Calculate edge strength using Sobel operator
            int gx = (qRed(prevLine[x+1]) + 2*qRed(currLine[x+1]) + qRed(nextLine[x+1])) -
                     (qRed(prevLine[x-1]) + 2*qRed(currLine[x-1]) + qRed(nextLine[x-1]));
            int gy = (qRed(prevLine[x-1]) + 2*qRed(prevLine[x]) + qRed(prevLine[x+1])) -
                     (qRed(nextLine[x-1]) + 2*qRed(nextLine[x]) + qRed(nextLine[x+1]));
            
            double edgeStrength = sqrt(gx*gx + gy*gy) / 255.0;
            
            if (edgeStrength < 0.1) {
                // Low edge strength - apply smoothing
                int r = 0, g = 0, b = 0, a = 0;
                
                // 3x3 Gaussian kernel
                int weights[9] = {1, 2, 1, 2, 4, 2, 1, 2, 1};
                int totalWeight = 16;
                
                QRgb neighbors[9] = {
                    prevLine[x-1], prevLine[x], prevLine[x+1],
                    currLine[x-1], currLine[x], currLine[x+1],
                    nextLine[x-1], nextLine[x], nextLine[x+1]
                };
                
                for (int i = 0; i < 9; ++i) {
                    r += qRed(neighbors[i]) * weights[i];
                    g += qGreen(neighbors[i]) * weights[i];
                    b += qBlue(neighbors[i]) * weights[i];
                    a += qAlpha(neighbors[i]) * weights[i];
                }
                
                line[x] = qRgba(r / totalWeight, g / totalWeight, b / totalWeight, a / totalWeight);
            } else {
                // High edge strength - preserve original
                line[x] = center;
            }
        }
    }
    
    return result;
}

// Professional kernel functions (industry standard implementations)
double Zview::lanczosKernel(double x, int a) {
    if (x == 0) return 1.0;
    if (qAbs(x) >= a) return 0.0;
    
    double pi_x = M_PI * x;
    double pi_x_a = pi_x / a;
    
    return (sin(pi_x) / pi_x) * (sin(pi_x_a) / pi_x_a);
}

double Zview::mitchellNetravaliKernel(double x, double B, double C) {
    x = qAbs(x);
    
    if (x < 1.0) {
        return ((12 - 9*B - 6*C) * x*x*x + (-18 + 12*B + 6*C) * x*x + (6 - 2*B)) / 6.0;
    } else if (x < 2.0) {
        return ((-B - 6*C) * x*x*x + (6*B + 30*C) * x*x + (-12*B - 48*C) * x + (8*B + 24*C)) / 6.0;
    }
    
    return 0.0;
}

double Zview::bSplineKernel(double x) {
    x = qAbs(x);
    
    if (x < 1.0) {
        return (2.0/3.0) - x*x + 0.5*x*x*x;
    } else if (x < 2.0) {
        x = 2.0 - x;
        return x*x*x / 6.0;
    }
    
    return 0.0;
}

void Zview::setGlobalScalingQuality(ScalingQuality quality) {
    m_scalingQuality = quality;
    qDebug() << "Scaling quality set to:" << (int)quality;
}

QImage Zview::applyAntiAliasing(const QImage &source) {
    if (source.isNull()) {
        return source;
    }
    
    // Apply professional edge-preserving anti-aliasing
    return applyEdgePreservingSmoothing(source);
}

void Zview::enableHighQualityRendering(bool enable) {
    // Set professional rendering quality
    if (enable) {
        setGlobalScalingQuality(ScalingQuality::AUTO);
        m_usePerformanceMode = false;
        setAttribute(Qt::WA_OpaquePaintEvent, false);
    } else {
        setGlobalScalingQuality(ScalingQuality::GOOD);
        m_usePerformanceMode = true;
    }
    
    // Force update to apply new rendering settings
    update();
}

void Zview::enableInteractiveMode(bool enable) {
    m_isInteractiveMode = enable;
    
    if (enable) {
        // Start interactive mode - use fast algorithms
        m_usePerformanceMode = true;
        
        // Set up timer to upgrade quality after interaction stops
        if (!m_qualityUpgradeTimer) {
            m_qualityUpgradeTimer = new QTimer(this);
            m_qualityUpgradeTimer->setSingleShot(true);
            connect(m_qualityUpgradeTimer, &QTimer::timeout, this, &Zview::upgradeQualityAfterDelay);
        }
        
        // Start timer for quality upgrade (500ms after interaction stops)
        m_qualityUpgradeTimer->start(500);
    } else {
        // End interactive mode - allow high quality
        if (m_qualityUpgradeTimer) {
            m_qualityUpgradeTimer->stop();
        }
        upgradeQualityAfterDelay();
    }
}

void Zview::upgradeQualityAfterDelay() {
    // Upgrade to high quality after interaction stops
    m_isInteractiveMode = false;
    m_usePerformanceMode = false;
    
    // Trigger a high-quality re-render
    update();
    
    qDebug() << "Quality upgraded to high-quality mode";
}

#include "zview.moc"


