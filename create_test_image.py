import PIL.Image as Image
import PIL.ImageDraw as ImageDraw

# Create a simple test image with patterns to make crop movement testing easier
img = Image.new('RGB', (800, 600), color='lightblue')
draw = ImageDraw.Draw(img)

# Add some grid patterns to make movement visible
for i in range(0, 800, 50):
    draw.line([(i, 0), (i, 600)], fill='gray', width=1)
for i in range(0, 600, 50):
    draw.line([(0, i), (800, i)], fill='gray', width=1)

# Add some colored rectangles to make regions distinct
draw.rectangle([100, 100, 200, 200], fill='red')
draw.rectangle([300, 150, 400, 250], fill='green')
draw.rectangle([500, 200, 600, 300], fill='blue')
draw.rectangle([250, 350, 350, 450], fill='yellow')

# Add some text
draw.text((50, 50), "Crop Test Image", fill='black')
draw.text((50, 500), "Move crop region by dragging inside it", fill='black')

img.save('c:/Users/<USER>/Desktop/Zview/test_crop.png')
print("Test image created: test_crop.png")
