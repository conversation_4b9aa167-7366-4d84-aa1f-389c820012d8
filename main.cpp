#include "zview.h"

#include <QApplication>
#include <QFileInfo>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    Zview w;
    
    // Check if a file path was passed as command line argument (for "open with" functionality)
    if (argc > 1) {
        QString filePath = QString::fromLocal8Bit(argv[1]);
        QFileInfo fileInfo(filePath);
          // Verify the file exists and load it
        if (fileInfo.exists() && fileInfo.isFile()) {
            w.openFile(filePath);
        }
    }
    
    w.show();
    return a.exec();
}
