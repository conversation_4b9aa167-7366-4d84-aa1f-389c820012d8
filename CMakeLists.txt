cmake_minimum_required(VERSION 3.19)
project(Zview LANGUAGES CXX)

# Set C++ standard and runtime library
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 6.5 REQUIRED COMPONENTS Core Widgets OpenGL OpenGLWidgets)

qt_standard_project_setup()

# Add include directory for mpv headers
include_directories(${CMAKE_SOURCE_DIR}/lib)

# Add library directory for mpv
link_directories(${CMAKE_BINARY_DIR})
link_directories(${CMAKE_SOURCE_DIR}/build)

qt_add_executable(Zview
    WIN32 MACOSX_BUNDLE
    main.cpp
    zview.cpp
    zview.h
    zview.ui
    MpvWrapper.cpp
    MpvWrapper.h
    HeicImageLoader.cpp
    HeicImageLoader.h
    SimpleCache.cpp
    SimpleCache.h
)

target_link_libraries(Zview
    PRIVATE
        Qt::Core
        Qt::Widgets
        Qt::OpenGL
        Qt::OpenGLWidgets
        mpv
)

# Add Windows-specific libraries for HEIC support
if(WIN32)
    target_link_libraries(Zview PRIVATE windowscodecs ole32)
endif()

include(GNUInstallDirs)

install(TARGETS Zview
    BUNDLE  DESTINATION .
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

qt_generate_deploy_app_script(
    TARGET Zview
    OUTPUT_SCRIPT deploy_script
    NO_UNSUPPORTED_PLATFORM_ERROR
)
install(SCRIPT ${deploy_script})
