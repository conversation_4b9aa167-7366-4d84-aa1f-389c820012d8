# Enhanced Zview Configuration File
# This file contains settings for the advanced video playback features

[General]
# Enable enhanced features
EnableAdvancedPlayback=true
EnableJKLScrubbing=true
EnableVisualPreviews=true
EnableFrameIndexing=true
EnableSmartCaching=true

[Hardware]
# Hardware acceleration settings
# Options: Auto, NVIDIA, AMD, Intel, DXVA2, D3D11, None
HardwareAcceleration=Auto
EnableGPUDecoding=true
MaxDecodingThreads=4

[Performance]
# Buffer and performance settings
BufferStrategy=Adaptive
BufferSize=30
EnableFrameDropping=true
EnableSmartBuffering=true
EnableMultithreading=true

[Cache]
# Smart caching configuration
MaxCacheSize=500
CacheDirectory=cache
ProxyQuality=720
ThumbnailSize=200
PreloadDistance=5
EnableProxyGeneration=true

[FrameIndexing]
# Frame indexing settings
GeneratePreviews=true
PreviewWidth=160
PreviewHeight=90
KeyFrameInterval=10.0
EnableBackgroundIndexing=true

[Scrubbing]
# Visual scrubbing configuration
EnableSmoothScrubbing=true
ShowTimecode=true
ShowKeyFrameMarkers=true
AnimationDuration=200
MaxCachedPreviews=50

[Controls]
# Keyboard and control settings
EnableKeyboardShortcuts=true
JKLMaxSpeed=8.0
FrameStepMode=Single
EnableSpeedControl=true

[Display]
# Visual appearance settings
ShowPerformanceMetrics=false
ShowBufferHealth=true
ShowFrameRate=false
EnableColorCorrection=false

# Color correction settings (when enabled)
Brightness=0.0
Contrast=1.0
Saturation=1.0

[Advanced]
# Advanced technical settings
OptimalMipLevel=Auto
EnableSeamlessCubemap=true
UseUniformBuffers=true
EnableFramebuffer=true
TextureFiltering=Linear

# Memory management
MaxTextureSize=8192
EnableNPOTTextures=true
EnableTextureCompression=false

[Logging]
# Debug and logging settings
EnableDebugLogging=false
LogPerformanceMetrics=false
LogCacheOperations=false
LogFrameIndexing=false
