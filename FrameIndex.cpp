#include "FrameIndex.h"
#include <algorithm>
#include <fstream>
#include <iostream>
#include <cstring>

// External C API for FFmpeg-like functionality (simplified)
extern "C" {
    // Simplified structures for video processing
    struct VideoContext {
        void* format_ctx;
        void* codec_ctx;
        int video_stream_index;
        double duration;
        double frame_rate;
        int width, height;
    };
    
    // Function declarations (would be implemented using FFmpeg or similar)
    int open_video_file(const char* filename, VideoContext* ctx);
    int get_next_frame(VideoContext* ctx, void** frame_data, int* frame_size, double* timestamp, int* is_keyframe);
    int seek_to_position(VideoContext* ctx, double timestamp);
    void close_video_file(VideoContext* ctx);
    int get_frame_at_time(VideoContext* ctx, double timestamp, void** frame_data, int* frame_size);
}

// SeekTable Implementation
SeekTable::SeekTable() : m_duration(0.0) {}

SeekTable::~SeekTable() {
    clear();
}

bool SeekTable::buildFromFile(const std::string& filePath) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    clear();
    m_filePath = filePath;
    
    VideoContext ctx = {};
    if (open_video_file(filePath.c_str(), &ctx) != 0) {
        return false;
    }
    
    m_duration = ctx.duration;
    
    // Index all frames
    void* frame_data = nullptr;
    int frame_size = 0;
    double timestamp = 0.0;
    int is_keyframe = 0;
    int64_t file_pos = 0;
    
    while (get_next_frame(&ctx, &frame_data, &frame_size, &timestamp, &is_keyframe) == 0) {
        FrameIndexEntry entry(timestamp, file_pos, is_keyframe != 0, frame_size, ctx.width, ctx.height);
        m_frames.push_back(entry);
        
        if (is_keyframe) {
            m_keyFrames.push_back(m_frames.size() - 1);
        }
        
        file_pos += frame_size;
    }
    
    close_video_file(&ctx);
    buildTimeIndex();
    
    return true;
}

FrameIndexEntry SeekTable::findNearestKeyFrame(double timestamp) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_keyFrames.empty()) {
        return FrameIndexEntry();
    }
    
    // Binary search for nearest keyframe
    auto it = std::lower_bound(m_keyFrames.begin(), m_keyFrames.end(), timestamp,
        [this](size_t keyframe_idx, double target_time) {
            return m_frames[keyframe_idx].timestamp < target_time;
        });
    
    if (it == m_keyFrames.end()) {
        return m_frames[m_keyFrames.back()];
    }
    
    if (it == m_keyFrames.begin()) {
        return m_frames[m_keyFrames.front()];
    }
    
    // Check if previous keyframe is closer
    auto prev_it = it - 1;
    double curr_diff = m_frames[*it].timestamp - timestamp;
    double prev_diff = timestamp - m_frames[*prev_it].timestamp;
    
    if (prev_diff <= curr_diff) {
        return m_frames[*prev_it];
    } else {
        return m_frames[*it];
    }
}

FrameIndexEntry SeekTable::findFrameAtTime(double timestamp) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_frames.empty()) {
        return FrameIndexEntry();
    }
    
    auto it = std::lower_bound(m_frames.begin(), m_frames.end(), timestamp,
        [](const FrameIndexEntry& entry, double target_time) {
            return entry.timestamp < target_time;
        });
    
    if (it == m_frames.end()) {
        return m_frames.back();
    }
    
    if (it == m_frames.begin()) {
        return *it;
    }
    
    // Check if previous frame is closer
    auto prev_it = it - 1;
    double curr_diff = it->timestamp - timestamp;
    double prev_diff = timestamp - prev_it->timestamp;
    
    return (prev_diff <= curr_diff) ? *prev_it : *it;
}

std::vector<FrameIndexEntry> SeekTable::getKeyFramesInRange(double startTime, double endTime) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<FrameIndexEntry> result;
    
    for (size_t keyframe_idx : m_keyFrames) {
        const FrameIndexEntry& entry = m_frames[keyframe_idx];
        if (entry.timestamp >= startTime && entry.timestamp <= endTime) {
            result.push_back(entry);
        }
    }
    
    return result;
}

FrameIndexEntry SeekTable::getFrameForScrubbing(double timestamp) const {
    // For scrubbing, prefer keyframes for better performance
    FrameIndexEntry keyFrame = findNearestKeyFrame(timestamp);
    FrameIndexEntry exactFrame = findFrameAtTime(timestamp);
    
    // If exact frame is close enough to keyframe, use keyframe for better performance
    double keyFrameDiff = std::abs(keyFrame.timestamp - timestamp);
    double exactFrameDiff = std::abs(exactFrame.timestamp - timestamp);
    
    // Prefer keyframe if it's within 0.5 seconds
    if (keyFrameDiff <= 0.5 && keyFrameDiff <= exactFrameDiff * 2.0) {
        return keyFrame;
    }
    
    return exactFrame;
}

double SeekTable::getAverageFrameRate() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_frames.size() < 2 || m_duration <= 0.0) {
        return 0.0;
    }
    
    return static_cast<double>(m_frames.size()) / m_duration;
}

void SeekTable::buildTimeIndex() {
    m_timeIndex.clear();
    for (size_t i = 0; i < m_frames.size(); ++i) {
        m_timeIndex[m_frames[i].timestamp] = i;
    }
}

void SeekTable::clear() {
    m_frames.clear();
    m_keyFrames.clear();
    m_timeIndex.clear();
    m_duration = 0.0;
    m_filePath.clear();
}

size_t SeekTable::getMemoryUsage() const {
    return m_frames.size() * sizeof(FrameIndexEntry) + 
           m_keyFrames.size() * sizeof(size_t) +
           m_timeIndex.size() * (sizeof(double) + sizeof(size_t));
}

// FrameIndexer Implementation
FrameIndexer::FrameIndexer() 
    : m_state(IndexingState::Idle)
    , m_maxMemoryUsage(100 * 1024 * 1024) // 100MB default
    , m_backgroundIndexing(true) {
}

FrameIndexer::~FrameIndexer() {
    stopIndexing();
}

void FrameIndexer::setOptions(const IndexingOptions& options) {
    m_options = options;
}

bool FrameIndexer::startIndexing(const std::string& filePath) {
    if (m_state == IndexingState::Indexing) {
        return false; // Already indexing
    }
    
    m_currentFile = filePath;
    m_state = IndexingState::Indexing;
    m_progress = 0.0;
    m_stopRequested = false;
    
    if (m_backgroundIndexing) {
        m_indexingThread = std::thread(&FrameIndexer::indexingWorker, this);
        return true;
    } else {
        indexingWorker();
        return m_state == IndexingState::Complete;
    }
}

void FrameIndexer::stopIndexing() {
    m_stopRequested = true;
    if (m_indexingThread.joinable()) {
        m_indexingThread.join();
    }
    m_state = IndexingState::Idle;
}

bool FrameIndexer::isIndexingComplete() const {
    return m_state == IndexingState::Complete;
}

void FrameIndexer::indexingWorker() {
    m_indexingActive = true;
    
    try {
        // Try to load cached index first
        if (m_options.enableCaching && loadCachedIndex()) {
            m_state = IndexingState::Complete;
            m_progress = 1.0;
            m_indexingActive = false;
            return;
        }
        
        // Build seek table
        if (!m_seekTable.buildFromFile(m_currentFile)) {
            m_lastError = "Failed to build seek table";
            m_state = IndexingState::Error;
            m_indexingActive = false;
            return;
        }
        
        m_progress = 0.5;
        
        // Generate previews if requested
        if (m_options.generatePreviews) {
            double duration = m_seekTable.getDuration();
            double interval = std::max(1.0, duration / 100.0); // 100 previews maximum
            
            for (double time = 0.0; time < duration; time += interval) {
                if (m_stopRequested) break;
                
                PreviewFrame preview;
                if (generatePreviewAtTime(time, preview)) {
                    std::lock_guard<std::mutex> lock(m_previewMutex);
                    m_previewCache[time] = std::move(preview);
                }
                
                m_progress = 0.5 + (time / duration) * 0.4;
            }
        }
        
        // Build scrub track
        buildScrubTrack();
        m_progress = 1.0;
        
        // Save cached index
        if (m_options.enableCaching) {
            saveCachedIndex();
        }
        
        m_state = IndexingState::Complete;
        
    } catch (const std::exception& e) {
        m_lastError = e.what();
        m_state = IndexingState::Error;
    }
    
    m_indexingActive = false;
}

FrameIndexer::PreviewFrame FrameIndexer::getPreviewAtTime(double timestamp) const {
    std::lock_guard<std::mutex> lock(m_previewMutex);
    
    // Find closest cached preview
    auto it = m_previewCache.lower_bound(timestamp);
    if (it != m_previewCache.end()) {
        return it->second;
    }
    
    if (!m_previewCache.empty()) {
        return m_previewCache.rbegin()->second;
    }
    
    return PreviewFrame();
}

bool FrameIndexer::generatePreviewAtTime(double timestamp, PreviewFrame& preview) {
    VideoContext ctx = {};
    if (open_video_file(m_currentFile.c_str(), &ctx) != 0) {
        return false;
    }
    
    void* frame_data = nullptr;
    int frame_size = 0;
    
    // Seek to timestamp and get frame
    if (seek_to_position(&ctx, timestamp) == 0 && 
        get_frame_at_time(&ctx, timestamp, &frame_data, &frame_size) == 0) {
        
        // Convert frame to RGB thumbnail (simplified)
        preview.timestamp = timestamp;
        preview.width = m_options.previewWidth;
        preview.height = m_options.previewHeight;
        preview.imageData.resize(preview.width * preview.height * 4); // RGBA
        
        // TODO: Implement actual image scaling and format conversion
        // This would typically use libswscale or similar
        std::fill(preview.imageData.begin(), preview.imageData.end(), 128);
        
        close_video_file(&ctx);
        return true;
    }
    
    close_video_file(&ctx);
    return false;
}

void FrameIndexer::buildScrubTrack() {
    std::lock_guard<std::mutex> lock(m_dataMutex);
    
    m_scrubTrack.clear();
    
    double duration = m_seekTable.getDuration();
    if (duration <= 0.0) return;
    
    // Generate scrub frames at regular intervals
    int trackWidth = 100; // Default track width
    double interval = duration / trackWidth;
    
    for (int i = 0; i < trackWidth; ++i) {
        double timestamp = i * interval;
        FrameIndexEntry entry = m_seekTable.getFrameForScrubbing(timestamp);
        
        ScrubFrame scrubFrame;
        scrubFrame.timestamp = timestamp;
        scrubFrame.isKeyFrame = entry.isKeyFrame;
        scrubFrame.thumbnailWidth = 32;
        scrubFrame.thumbnailHeight = 18;
        scrubFrame.thumbnailData.resize(32 * 18 * 4); // RGBA
        
        // Generate micro-thumbnail (would be actual video frame)
        std::fill(scrubFrame.thumbnailData.begin(), scrubFrame.thumbnailData.end(), 
                 entry.isKeyFrame ? 255 : 128);
        
        m_scrubTrack.push_back(scrubFrame);
    }
}

std::vector<FrameIndexer::ScrubFrame> FrameIndexer::generateScrubTrack(int trackWidth, double videoDuration) const {
    std::vector<ScrubFrame> track;
    track.reserve(trackWidth);
    
    double interval = videoDuration / trackWidth;
    
    for (int i = 0; i < trackWidth; ++i) {
        double timestamp = i * interval;
        ScrubFrame frame = getScrubFrameAtPosition(static_cast<double>(i) / trackWidth);
        track.push_back(frame);
    }
    
    return track;
}

FrameIndexer::ScrubFrame FrameIndexer::getScrubFrameAtPosition(double normalizedPosition) const {
    std::lock_guard<std::mutex> lock(m_dataMutex);
    
    if (m_scrubTrack.empty()) {
        return ScrubFrame();
    }
    
    size_t index = static_cast<size_t>(normalizedPosition * (m_scrubTrack.size() - 1));
    index = std::min(index, m_scrubTrack.size() - 1);
    
    return m_scrubTrack[index];
}

// ScrubController Implementation
ScrubController::ScrubController(FrameIndexer* indexer) 
    : m_indexer(indexer)
    , m_lastUpdate(std::chrono::steady_clock::now()) {
}

void ScrubController::handleJKey() {
    switch (m_state.mode) {
        case ScrubMode::Stopped:
        case ScrubMode::PlayForward:
            m_state.mode = ScrubMode::PlayBackward;
            m_state.playbackSpeed = -1.0;
            m_state.isPlaying = true;
            break;
        case ScrubMode::PlayBackward:
            m_state.playbackSpeed *= 2.0; // Increase reverse speed
            if (m_state.playbackSpeed < -8.0) {
                m_state.playbackSpeed = -8.0; // Cap at -8x
            }
            break;
        case ScrubMode::FastForward:
            m_state.mode = ScrubMode::PlayBackward;
            m_state.playbackSpeed = -1.0;
            break;
        case ScrubMode::FastBackward:
            m_state.playbackSpeed *= 2.0;
            if (m_state.playbackSpeed < -8.0) {
                m_state.playbackSpeed = -8.0;
            }
            break;
        case ScrubMode::FrameByFrame:
            stepFrame(-1);
            return;
    }
    
    updatePosition();
}

void ScrubController::handleKKey() {
    m_state.mode = ScrubMode::Stopped;
    m_state.playbackSpeed = 0.0;
    m_state.isPlaying = false;
}

void ScrubController::handleLKey() {
    switch (m_state.mode) {
        case ScrubMode::Stopped:
        case ScrubMode::PlayBackward:
            m_state.mode = ScrubMode::PlayForward;
            m_state.playbackSpeed = 1.0;
            m_state.isPlaying = true;
            break;
        case ScrubMode::PlayForward:
            m_state.playbackSpeed *= 2.0; // Increase forward speed
            if (m_state.playbackSpeed > 8.0) {
                m_state.playbackSpeed = 8.0; // Cap at 8x
            }
            break;
        case ScrubMode::FastBackward:
            m_state.mode = ScrubMode::PlayForward;
            m_state.playbackSpeed = 1.0;
            break;
        case ScrubMode::FastForward:
            m_state.playbackSpeed *= 2.0;
            if (m_state.playbackSpeed > 8.0) {
                m_state.playbackSpeed = 8.0;
            }
            break;
        case ScrubMode::FrameByFrame:
            stepFrame(1);
            return;
    }
    
    updatePosition();
}

void ScrubController::stepFrame(int frames) {
    double frameDuration = getFrameDuration();
    double newTime = m_state.currentTime + (frames * frameDuration);
    
    // Clamp to video duration
    double duration = m_indexer->getSeekTable().getDuration();
    newTime = std::max(0.0, std::min(newTime, duration));
    
    m_state.currentTime = newTime;
    m_state.mode = ScrubMode::FrameByFrame;
}

void ScrubController::stepToKeyFrame(bool forward) {
    if (!m_indexer) return;
    
    const SeekTable& seekTable = m_indexer->getSeekTable();
    
    if (forward) {
        // Find next keyframe
        auto keyFrames = seekTable.getKeyFramesInRange(m_state.currentTime + 0.001, 
                                                      seekTable.getDuration());
        if (!keyFrames.empty()) {
            m_state.currentTime = keyFrames.front().timestamp;
        }
    } else {
        // Find previous keyframe
        auto keyFrames = seekTable.getKeyFramesInRange(0.0, m_state.currentTime - 0.001);
        if (!keyFrames.empty()) {
            m_state.currentTime = keyFrames.back().timestamp;
        }
    }
}

void ScrubController::setPlaybackSpeed(double speed) {
    m_state.playbackSpeed = speed;
    m_state.isPlaying = (speed != 0.0);
    
    if (speed > 0.0) {
        m_state.mode = (speed > 1.0) ? ScrubMode::FastForward : ScrubMode::PlayForward;
    } else if (speed < 0.0) {
        m_state.mode = (speed < -1.0) ? ScrubMode::FastBackward : ScrubMode::PlayBackward;
    } else {
        m_state.mode = ScrubMode::Stopped;
    }
}

void ScrubController::seekToTime(double timestamp) {
    double duration = m_indexer->getSeekTable().getDuration();
    m_state.currentTime = std::max(0.0, std::min(timestamp, duration));
}

void ScrubController::seekToPosition(double normalizedPosition) {
    double duration = m_indexer->getSeekTable().getDuration();
    double timestamp = normalizedPosition * duration;
    seekToTime(timestamp);
}

void ScrubController::updatePosition() {
    if (!m_state.isPlaying) return;
    
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastUpdate);
    double deltaTime = elapsed.count() / 1000.0;
    
    m_state.currentTime += deltaTime * m_state.playbackSpeed;
    
    // Clamp to video bounds
    double duration = m_indexer->getSeekTable().getDuration();
    if (m_state.currentTime < 0.0) {
        m_state.currentTime = 0.0;
        handleKKey(); // Stop at beginning
    } else if (m_state.currentTime > duration) {
        m_state.currentTime = duration;
        handleKKey(); // Stop at end
    }
      m_lastUpdate = now;
}

// Stub implementations for missing external C functions
extern "C" {
    int open_video_file(const char* filename, VideoContext* ctx) {
        // TODO: Implement with FFmpeg or similar video library
        if (!filename || !ctx) return -1;
        
        // Stub implementation - set default values
        ctx->format_ctx = nullptr;
        ctx->codec_ctx = nullptr;
        ctx->video_stream_index = 0;
        ctx->duration = 120.0; // Default 2 minutes
        ctx->frame_rate = 30.0;
        ctx->width = 1920;
        ctx->height = 1080;
        
        return 0; // Success
    }
    
    int get_next_frame(VideoContext* ctx, void** frame_data, int* frame_size, double* timestamp, int* is_keyframe) {
        // TODO: Implement frame extraction
        if (!ctx || !frame_data || !frame_size || !timestamp || !is_keyframe) return -1;
        
        // Stub implementation
        *frame_data = nullptr;
        *frame_size = 0;
        *timestamp = 0.0;
        *is_keyframe = 1;
        
        return -1; // End of stream (no more frames)
    }
    
    int seek_to_position(VideoContext* ctx, double timestamp) {
        // TODO: Implement seeking
        if (!ctx) return -1;
        return 0; // Success
    }
    
    void close_video_file(VideoContext* ctx) {
        // TODO: Implement cleanup
        if (ctx) {
            ctx->format_ctx = nullptr;
            ctx->codec_ctx = nullptr;
        }
    }
    
    int get_frame_at_time(VideoContext* ctx, double timestamp, void** frame_data, int* frame_size) {
        // TODO: Implement frame extraction at specific time
        if (!ctx || !frame_data || !frame_size) return -1;
        
        // Stub implementation
        *frame_data = nullptr;
        *frame_size = 0;
        
        return -1; // No frame available
    }
}

// Stub implementations for missing FrameIndexer methods
bool FrameIndexer::loadCachedIndex() {
    // TODO: Implement cache loading
    return false;
}

void FrameIndexer::saveCachedIndex() {
    // TODO: Implement cache saving
}
