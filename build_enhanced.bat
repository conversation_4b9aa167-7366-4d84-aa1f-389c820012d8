@echo off
echo ========================================
echo Building Enhanced Zview Video Player
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "CMakeLists.txt" (
    echo Error: CMakeLists.txt not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Create build directory if it doesn't exist
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

cd build

echo Configuring with CMake...
cmake -G "Visual Studio 17 2022" -A x64 ..

if %errorlevel% neq 0 (
    echo Error: CMake configuration failed!
    echo.
    echo Make sure you have:
    echo - Visual Studio 2022 with C++ tools
    echo - Qt 6.5+ installed
    echo - CMake 3.19+ installed
    echo.
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if %errorlevel% neq 0 (
    echo Error: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: build\Release\Zview.exe
echo.
echo Enhanced Features Available:
echo - GPU-accelerated decoding and rendering
echo - J-K-L scrubbing controls (professional editing style)
echo - Smart caching with proxy generation
echo - Frame indexing for fast seeking
echo - Visual preview scrubbing
echo - Hardware acceleration auto-detection
echo - Advanced performance monitoring
echo.
echo Keyboard Shortcuts:
echo - J/K/L: Professional scrubbing controls
echo - Space: Play/Pause
echo - Arrow keys: Frame stepping
echo - 0-9: Seek to percentage
echo - T: Toggle J-K-L mode
echo - I: Start frame indexing
echo - P: Toggle visual previews
echo - F: Fullscreen
echo.
pause
