#ifndef HEICIMAGELOADER_H
#define HEICIMAGELOADER_H

#include <QPixmap>
#include <QString>
#include <QDebug>

class HeicImageLoader
{
public:
    // Try to load HEIC/HEIF image using Qt first, then fallback methods
    static QPixmap loadHeicImage(const QString &filePath);
    
    // Check if file is a HEIC/HEIF format
    static bool isHeicFile(const QString &filePath);

private:
    // Fallback loader using system APIs (Windows WIC on Windows)
    static QPixmap loadHeicFallback(const QString &filePath);
};

#endif // HEICIMAGELOADER_H