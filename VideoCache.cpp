#include "VideoCache.h"
#include <QStandardPaths>
#include <QCoreApplication>
#include <QDebug>
#include <QProcess>
#include <QFileInfo>
#include <QCryptographicHash>
#include <QDirIterator>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QRegularExpression>
#include <QElapsedTimer>
#include <QtMath>
#include <algorithm>

VideoCache::VideoCache(QObject *parent)
    : QObject(parent)
    , m_maxCacheSize(5 * 1024 * 1024 * 1024) // 5GB default
    , m_strategy(ADAPTIVE)
    , m_maxProxyAge(30) // 30 days
    , m_prefetchEnabled(true)
    , m_segmentDuration(30) // 30 seconds
    , m_cacheHits(0)
    , m_cacheMisses(0)
    , m_isGeneratingProxy(false)
    , m_currentProxyProcess(nullptr)
{
    // Initialize cache directory
    m_cacheDirectory = QStandardPaths::writableLocation(QStandardPaths::CacheLocation) + "/zview_cache";
    ensureCacheDirectory();
    
    // Initialize settings
    m_settings = new QSettings(this);
    loadSettings();
    
    // Initialize timers
    m_proxyProcessTimer = new QTimer(this);
    m_proxyProcessTimer->setInterval(1000); // Check every second
    connect(m_proxyProcessTimer, &QTimer::timeout, this, &VideoCache::processProxyQueue);
    m_proxyProcessTimer->start();
    
    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(CLEANUP_INTERVAL_MINUTES * 60 * 1000);
    connect(m_cleanupTimer, &QTimer::timeout, this, &VideoCache::cleanupExpiredEntries);
    m_cleanupTimer->start();
    
    m_optimizationTimer = new QTimer(this);
    m_optimizationTimer->setInterval(OPTIMIZATION_INTERVAL_MINUTES * 60 * 1000);
    connect(m_optimizationTimer, &QTimer::timeout, this, &VideoCache::optimizeCache);
    m_optimizationTimer->start();
    
    // Initialize statistics
    m_statsStartTime = QDateTime::currentDateTime();
    
    qDebug() << "VideoCache initialized with directory:" << m_cacheDirectory;
    qDebug() << "Max cache size:" << (m_maxCacheSize / (1024.0 * 1024.0 * 1024.0)) << "GB";
}

VideoCache::~VideoCache()
{
    saveSettings();
    
    // Clean up any running proxy generation
    if (m_currentProxyProcess && m_currentProxyProcess->state() != QProcess::NotRunning) {
        m_currentProxyProcess->kill();
        m_currentProxyProcess->waitForFinished(5000);
    }
}

void VideoCache::setCacheDirectory(const QString &path)
{
    m_cacheDirectory = path;
    ensureCacheDirectory();
}

void VideoCache::setMaxCacheSize(qint64 maxSize)
{
    m_maxCacheSize = maxSize;
    
    // Immediately optimize if we're over the new limit
    if (getCurrentCacheSize() > m_maxCacheSize) {
        optimizeCache();
    }
}

void VideoCache::setCacheStrategy(CacheStrategy strategy)
{
    m_strategy = strategy;
}

void VideoCache::setMaxProxyAge(int days)
{
    m_maxProxyAge = days;
}

bool VideoCache::isSegmentCached(const QString &filePath, qint64 startTime, qint64 endTime)
{
    QMutexLocker locker(&m_cacheMutex);
    
    QString key = generateCacheKey(filePath, startTime, endTime);
    bool exists = m_cacheEntries.contains(key);
    
    if (exists) {
        // Update access statistics
        CacheEntry &entry = m_cacheEntries[key];
        entry.lastAccessed = QDateTime::currentDateTime();
        entry.accessCount++;
        m_cacheHits++;
        
        // Verify the cached file still exists
        if (!QFileInfo::exists(entry.cacheKey)) {
            m_cacheEntries.remove(key);
            m_cacheMisses++;
            return false;
        }
    } else {
        m_cacheMisses++;
    }
    
    return exists;
}

QString VideoCache::getCachedSegmentPath(const QString &filePath, qint64 startTime, qint64 endTime)
{
    QMutexLocker locker(&m_cacheMutex);
    
    QString key = generateCacheKey(filePath, startTime, endTime);
    if (m_cacheEntries.contains(key)) {
        CacheEntry &entry = m_cacheEntries[key];
        entry.lastAccessed = QDateTime::currentDateTime();
        entry.accessCount++;
        return entry.cacheKey;
    }
    
    return QString();
}

void VideoCache::cacheVideoSegment(const QString &filePath, qint64 startTime, qint64 endTime)
{
    // This would typically extract a segment using FFmpeg
    // For now, we'll create a placeholder implementation
    QString key = generateCacheKey(filePath, startTime, endTime);
    QString outputPath = m_cacheDirectory + "/" + key + ".mp4";
    
    // Check if already cached
    if (isSegmentCached(filePath, startTime, endTime)) {
        return;
    }
    
    // Create FFmpeg command to extract segment
    QStringList arguments;
    arguments << "-i" << filePath
              << "-ss" << QString::number(startTime / 1000.0, 'f', 3)
              << "-t" << QString::number((endTime - startTime) / 1000.0, 'f', 3)
              << "-c" << "copy"
              << "-avoid_negative_ts" << "make_zero"
              << "-y" << outputPath;
    
    QProcess *process = new QProcess(this);
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, key, filePath, startTime, endTime, outputPath, process](int exitCode) {
                if (exitCode == 0 && QFileInfo::exists(outputPath)) {
                    // Add to cache
                    QMutexLocker locker(&m_cacheMutex);
                    CacheEntry entry;
                    entry.filePath = filePath;
                    entry.cacheKey = outputPath;
                    entry.startTime = startTime;
                    entry.endTime = endTime;
                    entry.fileSize = QFileInfo(outputPath).size();
                    entry.lastAccessed = QDateTime::currentDateTime();
                    entry.created = QDateTime::currentDateTime();
                    entry.accessCount = 1;
                    entry.isProxy = false;
                    
                    m_cacheEntries[key] = entry;
                    
                    qDebug() << "Cached video segment:" << outputPath;
                }
                process->deleteLater();
            });
    
    process->start("ffmpeg", arguments);
}

void VideoCache::generateProxy(const QString &filePath, ProxyQuality quality)
{
    QString proxyPath = getProxyPath(filePath, quality);
    
    if (hasProxy(filePath, quality)) {
        emit proxyGenerationCompleted(filePath, proxyPath);
        return;
    }
    
    ProxyGenerationTask task;
    task.sourceFile = filePath;
    task.outputPath = proxyPath;
    task.priority = 0; // High priority for synchronous requests
    
    // Set quality parameters
    switch (quality) {
    case PROXY_LOW:
        task.width = 854;
        task.height = 480;
        task.bitrate = 1000; // 1 Mbps
        break;
    case PROXY_MEDIUM:
        task.width = 1280;
        task.height = 720;
        task.bitrate = 2500; // 2.5 Mbps
        break;
    case PROXY_HIGH:
        task.width = 1920;
        task.height = 1080;
        task.bitrate = 5000; // 5 Mbps
        break;
    }
    
    startProxyGeneration(task);
}

QString VideoCache::getProxyPath(const QString &filePath, ProxyQuality quality)
{
    QString key = generateProxyKey(filePath, quality);
    QString qualityStr;
    
    switch (quality) {
    case PROXY_LOW: qualityStr = "low"; break;
    case PROXY_MEDIUM: qualityStr = "medium"; break;
    case PROXY_HIGH: qualityStr = "high"; break;
    }
    
    return m_cacheDirectory + "/proxies/" + key + "_" + qualityStr + ".mp4";
}

bool VideoCache::hasProxy(const QString &filePath, ProxyQuality quality)
{
    QString proxyPath = getProxyPath(filePath, quality);
    return QFileInfo::exists(proxyPath);
}

void VideoCache::generateProxyAsync(const QString &filePath, ProxyQuality quality, int priority)
{
    QMutexLocker locker(&m_proxyQueueMutex);
    
    // Check if already in queue
    for (const auto &task : m_proxyQueue) {
        if (task.sourceFile == filePath && 
            ((quality == PROXY_LOW && task.width == 854) ||
             (quality == PROXY_MEDIUM && task.width == 1280) ||
             (quality == PROXY_HIGH && task.width == 1920))) {
            return; // Already queued
        }
    }
    
    ProxyGenerationTask task;
    task.sourceFile = filePath;
    task.outputPath = getProxyPath(filePath, quality);
    task.priority = priority;
    
    // Set quality parameters
    switch (quality) {
    case PROXY_LOW:
        task.width = 854;
        task.height = 480;
        task.bitrate = 1000;
        break;
    case PROXY_MEDIUM:
        task.width = 1280;
        task.height = 720;
        task.bitrate = 2500;
        break;
    case PROXY_HIGH:
        task.width = 1920;
        task.height = 1080;
        task.bitrate = 5000;
        break;
    }
    
    // Insert in priority order
    auto it = m_proxyQueue.begin();
    while (it != m_proxyQueue.end() && it->priority <= priority) {
        ++it;
    }
    m_proxyQueue.insert(it, task);
}

void VideoCache::prefetchUpcoming(const QString &filePath, double currentTime, double duration)
{
    if (!m_prefetchEnabled) return;
    
    // Prefetch the next few segments
    double segmentStart = currentTime;
    int segmentCount = 3; // Prefetch 3 segments ahead
    
    for (int i = 0; i < segmentCount && segmentStart < duration; ++i) {
        double segmentEnd = qMin(segmentStart + m_segmentDuration, duration);
        
        qint64 startMs = qint64(segmentStart * 1000);
        qint64 endMs = qint64(segmentEnd * 1000);
        
        if (!isSegmentCached(filePath, startMs, endMs)) {
            // Cache this segment asynchronously
            QTimer::singleShot(i * 1000, [this, filePath, startMs, endMs]() {
                cacheVideoSegment(filePath, startMs, endMs);
            });
        }
        
        segmentStart = segmentEnd;
    }
}

void VideoCache::setPrefetchStrategy(bool enabled, int segmentDuration)
{
    m_prefetchEnabled = enabled;
    m_segmentDuration = segmentDuration;
}

void VideoCache::optimizeCache()
{
    QMutexLocker locker(&m_cacheMutex);
    
    qint64 currentSize = getCurrentCacheSize();
    if (currentSize <= m_maxCacheSize) {
        return; // No optimization needed
    }
    
    qint64 targetSize = m_maxCacheSize * 0.8; // Target 80% of max size
    qint64 toFree = currentSize - targetSize;
    qint64 freed = 0;
    
    // Create list of entries sorted by eviction priority
    QList<QString> keysToRemove;
    
    switch (m_strategy) {
    case LRU: {
        // Sort by last accessed time (oldest first)
        QList<QPair<QDateTime, QString>> entries;
        for (auto it = m_cacheEntries.begin(); it != m_cacheEntries.end(); ++it) {
            entries.append({it->lastAccessed, it.key()});
        }
        std::sort(entries.begin(), entries.end());
        
        for (const auto &entry : entries) {
            if (freed >= toFree) break;
            keysToRemove.append(entry.second);
            freed += m_cacheEntries[entry.second].fileSize;
        }
        break;
    }
    case LFU: {
        // Sort by access count (least frequent first)
        QList<QPair<int, QString>> entries;
        for (auto it = m_cacheEntries.begin(); it != m_cacheEntries.end(); ++it) {
            entries.append({it->accessCount, it.key()});
        }
        std::sort(entries.begin(), entries.end());
        
        for (const auto &entry : entries) {
            if (freed >= toFree) break;
            keysToRemove.append(entry.second);
            freed += m_cacheEntries[entry.second].fileSize;
        }
        break;
    }
    case ADAPTIVE: {
        // Adaptive strategy considering both recency and frequency
        QList<QPair<double, QString>> entries;
        QDateTime now = QDateTime::currentDateTime();
        
        for (auto it = m_cacheEntries.begin(); it != m_cacheEntries.end(); ++it) {
            double hoursSinceAccess = it->lastAccessed.secsTo(now) / 3600.0;
            double score = it->accessCount / (1.0 + hoursSinceAccess); // Lower score = higher eviction priority
            entries.append({score, it.key()});
        }
        std::sort(entries.begin(), entries.end());
        
        for (const auto &entry : entries) {
            if (freed >= toFree) break;
            keysToRemove.append(entry.second);
            freed += m_cacheEntries[entry.second].fileSize;
        }
        break;
    }
    }
    
    // Remove selected entries
    for (const QString &key : keysToRemove) {
        if (m_cacheEntries.contains(key)) {
            removeFile(m_cacheEntries[key].cacheKey);
            m_cacheEntries.remove(key);
        }
    }
    
    emit cacheOptimized(freed);
    qDebug() << "Cache optimized, freed" << (freed / (1024.0 * 1024.0)) << "MB";
}

qint64 VideoCache::getCurrentCacheSize()
{
    return calculateDirectorySize(m_cacheDirectory);
}

int VideoCache::getCacheHitRate()
{
    int total = m_cacheHits + m_cacheMisses;
    return total > 0 ? (m_cacheHits * 100) / total : 0;
}

void VideoCache::clearOldEntries()
{
    QMutexLocker locker(&m_cacheMutex);
    
    QDateTime cutoff = QDateTime::currentDateTime().addDays(-m_maxProxyAge);
    QList<QString> keysToRemove;
    
    for (auto it = m_cacheEntries.begin(); it != m_cacheEntries.end(); ++it) {
        if (it->created < cutoff) {
            keysToRemove.append(it.key());
        }
    }
    
    for (const QString &key : keysToRemove) {
        removeFile(m_cacheEntries[key].cacheKey);
        m_cacheEntries.remove(key);
    }
    
    qDebug() << "Cleared" << keysToRemove.size() << "old cache entries";
}

QList<CacheEntry> VideoCache::getCacheStatistics()
{
    QMutexLocker locker(&m_cacheMutex);
    return m_cacheEntries.values();
}

void VideoCache::loadSettings()
{
    m_settings->beginGroup("VideoCache");
    
    m_maxCacheSize = m_settings->value("maxCacheSize", 5LL * 1024 * 1024 * 1024).toLongLong();
    m_strategy = static_cast<CacheStrategy>(m_settings->value("strategy", ADAPTIVE).toInt());
    m_maxProxyAge = m_settings->value("maxProxyAge", 30).toInt();
    m_prefetchEnabled = m_settings->value("prefetchEnabled", true).toBool();
    m_segmentDuration = m_settings->value("segmentDuration", 30).toInt();
    
    QString customCacheDir = m_settings->value("cacheDirectory").toString();
    if (!customCacheDir.isEmpty()) {
        m_cacheDirectory = customCacheDir;
    }
    
    m_settings->endGroup();
}

void VideoCache::saveSettings()
{
    m_settings->beginGroup("VideoCache");
    
    m_settings->setValue("maxCacheSize", m_maxCacheSize);
    m_settings->setValue("strategy", static_cast<int>(m_strategy));
    m_settings->setValue("maxProxyAge", m_maxProxyAge);
    m_settings->setValue("prefetchEnabled", m_prefetchEnabled);
    m_settings->setValue("segmentDuration", m_segmentDuration);
    m_settings->setValue("cacheDirectory", m_cacheDirectory);
    
    m_settings->endGroup();
    m_settings->sync();
}

void VideoCache::cleanupExpiredEntries()
{
    clearOldEntries();
    cleanupCorruptedFiles();
}

void VideoCache::updateAccessPattern(const QString &filePath, double currentTime)
{
    QMutexLocker locker(&m_cacheMutex);
    
    // Record access pattern for prediction
    if (!m_accessPatterns.contains(filePath)) {
        m_accessPatterns[filePath] = QList<double>();
    }
    
    QList<double> &pattern = m_accessPatterns[filePath];
    pattern.append(currentTime);
    
    // Keep only recent patterns
    if (pattern.size() > MAX_ACCESS_PATTERN_SIZE) {
        pattern.removeFirst();
    }
    
    // Update prediction model
    updatePredictionModel(filePath, currentTime);
    
    // Trigger prefetching based on predictions
    QList<double> predictions = predictNextAccess(filePath, currentTime);
    for (double predictedTime : predictions) {
        // Prefetch segments around predicted access points
        double segmentStart = qMax(0.0, predictedTime - m_segmentDuration / 2.0);
        double segmentEnd = predictedTime + m_segmentDuration / 2.0;
        
        qint64 startMs = qint64(segmentStart * 1000);
        qint64 endMs = qint64(segmentEnd * 1000);
        
        if (!isSegmentCached(filePath, startMs, endMs)) {
            QTimer::singleShot(100, [this, filePath, startMs, endMs]() {
                cacheVideoSegment(filePath, startMs, endMs);
            });
        }
    }
}

void VideoCache::processProxyQueue()
{
    if (m_isGeneratingProxy || m_proxyQueue.isEmpty()) {
        return;
    }
    
    QMutexLocker locker(&m_proxyQueueMutex);
    ProxyGenerationTask task = m_proxyQueue.dequeue();
    locker.unlock();
    
    startProxyGeneration(task);
}

void VideoCache::handleProxyGenerationFinished()
{
    m_isGeneratingProxy = false;
    
    if (m_currentProxyProcess) {
        m_currentProxyProcess->deleteLater();
        m_currentProxyProcess = nullptr;
    }
}

QString VideoCache::generateCacheKey(const QString &filePath, qint64 startTime, qint64 endTime)
{
    QString data = filePath + QString::number(startTime) + QString::number(endTime);
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Md5).toHex();
}

QString VideoCache::generateProxyKey(const QString &filePath, ProxyQuality quality)
{
    QString data = filePath + QString::number(static_cast<int>(quality));
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Md5).toHex();
}

void VideoCache::evictLeastUsed()
{
    // This is handled by optimizeCache()
    optimizeCache();
}

void VideoCache::updateCacheStats()
{
    int newHitRate = getCacheHitRate();
    emit cacheHitRateChanged(newHitRate);
}

bool VideoCache::isValidCacheEntry(const CacheEntry &entry)
{
    return QFileInfo::exists(entry.cacheKey) && QFileInfo(entry.cacheKey).isFile();
}

QString VideoCache::getProxySettings(ProxyQuality quality)
{
    switch (quality) {
    case PROXY_LOW:
        return "scale=854:480,format=yuv420p";
    case PROXY_MEDIUM:
        return "scale=1280:720,format=yuv420p";
    case PROXY_HIGH:
        return "scale=1920:1080,format=yuv420p";
    }
    return "scale=1280:720,format=yuv420p";
}

void VideoCache::startProxyGeneration(const ProxyGenerationTask &task)
{
    if (m_isGeneratingProxy) {
        return;
    }
    
    m_isGeneratingProxy = true;
    emit proxyGenerationStarted(task.sourceFile);
    
    // Ensure proxy directory exists
    QDir proxyDir(QFileInfo(task.outputPath).absolutePath());
    if (!proxyDir.exists()) {
        proxyDir.mkpath(".");
    }
    
    QString ffmpegCmd = buildFFmpegCommand(task);
    
    m_currentProxyProcess = new QProcess(this);
    
    connect(m_currentProxyProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, task](int exitCode, QProcess::ExitStatus exitStatus) {
                if (exitCode == 0 && QFileInfo::exists(task.outputPath)) {
                    // Add proxy to cache entries
                    QMutexLocker locker(&m_cacheMutex);
                    QString key = generateProxyKey(task.sourceFile, 
                        task.width == 854 ? PROXY_LOW : 
                        task.width == 1280 ? PROXY_MEDIUM : PROXY_HIGH);
                    
                    CacheEntry entry;
                    entry.filePath = task.sourceFile;
                    entry.cacheKey = task.outputPath;
                    entry.fileSize = QFileInfo(task.outputPath).size();
                    entry.lastAccessed = QDateTime::currentDateTime();
                    entry.created = QDateTime::currentDateTime();
                    entry.accessCount = 0;
                    entry.isProxy = true;
                    entry.proxyPath = task.outputPath;
                    
                    m_cacheEntries[key] = entry;
                    
                    emit proxyGenerationCompleted(task.sourceFile, task.outputPath);
                    qDebug() << "Proxy generation completed:" << task.outputPath;
                } else {
                    emit proxyGenerationFailed(task.sourceFile, "FFmpeg process failed");
                    qDebug() << "Proxy generation failed for:" << task.sourceFile;
                }
                
                handleProxyGenerationFinished();
            });
    
    connect(m_currentProxyProcess, &QProcess::readyReadStandardError, [this, task]() {
        if (m_currentProxyProcess) {
            QByteArray data = m_currentProxyProcess->readAllStandardError();
            QString output = QString::fromUtf8(data);
            
            // Parse FFmpeg progress output to emit progress signals
            QRegularExpression timeRegex(R"(time=(\d+):(\d+):(\d+\.\d+))");
            QRegularExpressionMatch match = timeRegex.match(output);
            if (match.hasMatch()) {
                double hours = match.captured(1).toDouble();
                double minutes = match.captured(2).toDouble();
                double seconds = match.captured(3).toDouble();
                double currentTime = hours * 3600 + minutes * 60 + seconds;
                
                // Estimate progress (this would need duration info for accuracy)
                int progress = qMin(99, qMax(0, int(currentTime / task.duration * 100)));
                emit proxyGenerationProgress(task.sourceFile, progress);
            }
        }
    });
    
    // Parse FFmpeg command and start process
    QStringList args = ffmpegCmd.split(' ', Qt::SkipEmptyParts);
    QString program = args.takeFirst();
    
    m_currentProxyProcess->start(program, args);
    
    // Set timeout
    QTimer::singleShot(PROXY_GENERATION_TIMEOUT_MINUTES * 60 * 1000, [this, task]() {
        if (m_currentProxyProcess && m_currentProxyProcess->state() == QProcess::Running) {
            m_currentProxyProcess->kill();
            emit proxyGenerationFailed(task.sourceFile, "Timeout");
        }
    });
}

QString VideoCache::buildFFmpegCommand(const ProxyGenerationTask &task)
{
    QStringList cmd;
    cmd << "ffmpeg"
        << "-i" << task.sourceFile
        << "-vf" << QString("scale=%1:%2").arg(task.width).arg(task.height)
        << "-c:v" << "libx264"
        << "-preset" << "medium"
        << "-crf" << "23"
        << "-b:v" << QString("%1k").arg(task.bitrate)
        << "-c:a" << "aac"
        << "-b:a" << "128k"
        << "-movflags" << "+faststart"
        << "-y" << task.outputPath;
    
    return cmd.join(" ");
}

void VideoCache::ensureCacheDirectory()
{
    QDir cacheDir(m_cacheDirectory);
    if (!cacheDir.exists()) {
        cacheDir.mkpath(".");
        cacheDir.mkpath("proxies");
    }
}

void VideoCache::cleanupCorruptedFiles()
{
    QMutexLocker locker(&m_cacheMutex);
    
    QList<QString> keysToRemove;
    
    for (auto it = m_cacheEntries.begin(); it != m_cacheEntries.end(); ++it) {
        if (!isValidCacheEntry(it.value())) {
            keysToRemove.append(it.key());
        }
    }
    
    for (const QString &key : keysToRemove) {
        m_cacheEntries.remove(key);
    }
    
    if (!keysToRemove.isEmpty()) {
        qDebug() << "Cleaned up" << keysToRemove.size() << "corrupted cache entries";
    }
}

qint64 VideoCache::calculateDirectorySize(const QString &path)
{
    qint64 size = 0;
    QDirIterator it(path, QDirIterator::Subdirectories);
    
    while (it.hasNext()) {
        it.next();
        QFileInfo fileInfo = it.fileInfo();
        if (fileInfo.isFile()) {
            size += fileInfo.size();
        }
    }
    
    return size;
}

void VideoCache::removeFile(const QString &filePath)
{
    QFile::remove(filePath);
}

void VideoCache::analyzeAccessPattern(const QString &filePath)
{
    // This could implement more sophisticated pattern analysis
    // For now, we keep it simple in updateAccessPattern
}

void VideoCache::updatePredictionModel(const QString &filePath, double accessTime)
{
    // Simple prediction model based on recent access patterns
    if (!m_accessPatterns.contains(filePath) || m_accessPatterns[filePath].size() < 3) {
        return;
    }
    
    const QList<double> &pattern = m_accessPatterns[filePath];
    
    // Calculate average access interval
    double totalInterval = 0;
    int intervalCount = 0;
    
    for (int i = 1; i < pattern.size(); ++i) {
        totalInterval += pattern[i] - pattern[i-1];
        intervalCount++;
    }
    
    if (intervalCount > 0) {
        double avgInterval = totalInterval / intervalCount;
        
        // Predict next few access points
        QList<double> predictions;
        for (int i = 1; i <= 3; ++i) {
            predictions.append(accessTime + avgInterval * i);
        }
        
        m_predictionModels[filePath] = predictions;
    }
}

QList<double> VideoCache::predictNextAccess(const QString &filePath, double currentTime)
{
    if (m_predictionModels.contains(filePath)) {
        return m_predictionModels[filePath];
    }
    
    return QList<double>();
}
