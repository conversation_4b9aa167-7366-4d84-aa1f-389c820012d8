#ifndef MPVWRAPPER_H
#define MPVWRAPPER_H

#include <QObject>
#include <QString>
#include <QOpenGLContext>
#include <QOpenGLFunctions>
#include <QTimer>
#include <QElapsedTimer>
#include <QtGlobal>

// Forward declare mpv structures to avoid including mpv headers here
struct mpv_handle;
struct mpv_render_context;

class MpvWrapper : public QObject
{
    Q_OBJECT
public:
    explicit MpvWrapper(QObject *parent = nullptr);
    ~MpvWrapper();

    void loadFile(const QString &filePath);    void play();    void pause();
    void stop();
    void setLoop(bool enabled); // Enable/disable looping
    void setVolume(int volume); // Set volume (0-100)
    bool isInitialized() const;
    
    // Modern OpenGL rendering API
    bool initializeRenderContext(QOpenGLContext *glContext);
    void renderFrame(int width, int height, int fbo = 0);
    bool hasNewFrame() const;
    void acknowledgeFrame();      // Seeking functionality
    void seek(double position);
    void updatePositionInstant(double position); // For instant visual feedback during seeking
    double getPosition() const;
    double getDuration() const;
    bool isPaused() const;
    
    // Manual duration adjustment
    void adjustDuration(double newDuration);    QString getCurrentFilePath() const { return m_currentFilePath; }

    // Ultra-fast scrubbing functionality
    void enableScrubbingMode(bool enabled);
    void setScrubbingPosition(double position);
    void flushScrubbingSeeks(); // Force immediate execution of pending seeks
    void optimizeForScrubbing(); // Configure MPV for optimal scrubbing performance
    void restoreAudio(); // Restore audio after scrubbing or optimization

signals:
    void frameReady();
    void positionChanged(double position);
    void durationChanged(double duration);
    void seekCompleted();

public slots:
    void processEventsImmediate(); // Make this a public slot for QMetaObject::invokeMethod

private:
    static void on_mpv_render_update(void *ctx);
    static void on_mpv_wakeup(void *ctx);  // Immediate callback for MPV events
    void updatePosition();  // Timer slot for position updates - will be replaced with events
    void processEvents();   // Process MPV events for real-time updates
    
    mpv_handle *mpv;
    mpv_render_context *mpv_render_ctx;
    bool m_initialized;
    bool m_renderInitialized;
    bool m_hasNewFrame;
    QString m_currentFilePath;
    QTimer *m_positionTimer;
    QTimer *m_eventTimer;   // Timer for processing MPV events// Internal tracking for position and duration simulation
    double m_simulatedPosition;
    double m_simulatedDuration;
    bool m_isPaused;
    qint64 m_lastUpdateTime;
};

#endif // MPVWRAPPER_H