#ifndef VIDEOCACHE_H
#define VIDEOCACHE_H

#include <QObject>
#include <QHash>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QDateTime>
#include <QDir>
#include <QString>
#include <QQueue>
#include <QFileInfo>
#include <QSettings>
#include <QProcess>

struct CacheEntry {
    QString filePath;
    QString cacheKey;
    qint64 startTime;     // in milliseconds
    qint64 endTime;       // in milliseconds
    qint64 fileSize;
    QDateTime lastAccessed;
    QDateTime created;
    int accessCount;
    bool isProxy;         // true for proxy files, false for original segments
    QString proxyPath;    // path to proxy file if this is a proxy entry
    
    CacheEntry() : startTime(0), endTime(0), fileSize(0), accessCount(0), isProxy(false) {}
};

struct ProxyGenerationTask {
    QString sourceFile;
    QString outputPath;
    int width;
    int height;
    int bitrate;
    double startTime;
    double duration;
    int priority;  // 0 = highest priority
    
    ProxyGenerationTask() : width(0), height(0), bitrate(0), startTime(0), duration(0), priority(5) {}
};

class VideoCache : public QObject
{
    Q_OBJECT

public:
    enum CacheStrategy {
        LRU,           // Least Recently Used
        LFU,           // Least Frequently Used
        ADAPTIVE       // Adaptive based on access patterns
    };

    enum ProxyQuality {
        PROXY_LOW,     // 480p, low bitrate - for fast scrubbing
        PROXY_MEDIUM,  // 720p, medium bitrate - balanced
        PROXY_HIGH     // 1080p, high bitrate - high quality preview
    };

    explicit VideoCache(QObject *parent = nullptr);
    ~VideoCache();

    // Cache management
    void setCacheDirectory(const QString &path);
    void setMaxCacheSize(qint64 maxSize);  // in bytes
    void setCacheStrategy(CacheStrategy strategy);
    void setMaxProxyAge(int days);
    
    // Cache operations
    bool isSegmentCached(const QString &filePath, qint64 startTime, qint64 endTime);
    QString getCachedSegmentPath(const QString &filePath, qint64 startTime, qint64 endTime);
    void cacheVideoSegment(const QString &filePath, qint64 startTime, qint64 endTime);
    
    // Proxy generation
    void generateProxy(const QString &filePath, ProxyQuality quality = PROXY_MEDIUM);
    QString getProxyPath(const QString &filePath, ProxyQuality quality);
    bool hasProxy(const QString &filePath, ProxyQuality quality);
    void generateProxyAsync(const QString &filePath, ProxyQuality quality, int priority = 5);
    
    // Prefetching
    void prefetchUpcoming(const QString &filePath, double currentTime, double duration);
    void setPrefetchStrategy(bool enabled, int segmentDuration = 30); // 30 seconds per segment
    
    // Cache analytics and optimization
    void optimizeCache();
    qint64 getCurrentCacheSize();
    int getCacheHitRate();
    void clearOldEntries();
    QList<CacheEntry> getCacheStatistics();
    
    // Settings
    void loadSettings();
    void saveSettings();

public slots:
    void cleanupExpiredEntries();
    void updateAccessPattern(const QString &filePath, double currentTime);

signals:
    void proxyGenerationStarted(const QString &filePath);
    void proxyGenerationProgress(const QString &filePath, int percentage);
    void proxyGenerationCompleted(const QString &filePath, const QString &proxyPath);
    void proxyGenerationFailed(const QString &filePath, const QString &error);
    void cacheOptimized(qint64 freedSpace);
    void cacheHitRateChanged(int newRate);

private slots:
    void processProxyQueue();
    void handleProxyGenerationFinished();

private:
    // Core cache management
    QString generateCacheKey(const QString &filePath, qint64 startTime, qint64 endTime);
    QString generateProxyKey(const QString &filePath, ProxyQuality quality);
    void evictLeastUsed();
    void updateCacheStats();
    bool isValidCacheEntry(const CacheEntry &entry);
    
    // Proxy generation helpers
    QString getProxySettings(ProxyQuality quality);
    void startProxyGeneration(const ProxyGenerationTask &task);
    QString buildFFmpegCommand(const ProxyGenerationTask &task);
    
    // File management
    void ensureCacheDirectory();
    void cleanupCorruptedFiles();
    qint64 calculateDirectorySize(const QString &path);
    void removeFile(const QString &filePath);
    
    // Access pattern analysis
    void analyzeAccessPattern(const QString &filePath);
    void updatePredictionModel(const QString &filePath, double accessTime);
    QList<double> predictNextAccess(const QString &filePath, double currentTime);

private:
    // Cache storage
    QHash<QString, CacheEntry> m_cacheEntries;
    QMutex m_cacheMutex;
    
    // Configuration
    QString m_cacheDirectory;
    qint64 m_maxCacheSize;
    CacheStrategy m_strategy;
    int m_maxProxyAge;
    bool m_prefetchEnabled;
    int m_segmentDuration;
    
    // Statistics
    int m_cacheHits;
    int m_cacheMisses;
    QDateTime m_statsStartTime;
    
    // Proxy generation
    QQueue<ProxyGenerationTask> m_proxyQueue;
    QMutex m_proxyQueueMutex;
    QTimer *m_proxyProcessTimer;
    bool m_isGeneratingProxy;
    QProcess *m_currentProxyProcess;
    
    // Access pattern tracking
    QHash<QString, QList<double>> m_accessPatterns;  // File -> list of access times
    QHash<QString, QList<double>> m_predictionModels; // File -> predicted access times
    
    // Cleanup and optimization
    QTimer *m_cleanupTimer;
    QTimer *m_optimizationTimer;
    
    // Settings
    QSettings *m_settings;
    
    // Constants
    static const int CLEANUP_INTERVAL_MINUTES = 30;
    static const int OPTIMIZATION_INTERVAL_MINUTES = 60;
    static const int MAX_ACCESS_PATTERN_SIZE = 1000;
    static const int PROXY_GENERATION_TIMEOUT_MINUTES = 30;
};

#endif // VIDEOCACHE_H
