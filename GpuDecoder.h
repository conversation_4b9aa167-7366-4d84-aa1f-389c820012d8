#ifndef GPUDECODER_H
#define GPUDECODER_H

#include <QtCore/QObject>
#include <QtOpenGL/QOpenGLContext>
#include <QtOpenGL/QOpenGLFunctions>
#include <QtCore/QThread>
#include <QtCore/QMutex>
#include <QtCore/QWaitCondition>
#include <QtCore/QQueue>
#include <QtCore/QTimer>
#include <QtCore/QAtomicInt>
#include <QtCore/QStringList>
#include <memory>

// Forward declarations
struct AVCodecContext;
struct AVFrame;
struct AVPacket;
struct AVFormatContext;
struct AVBufferRef;

class GpuDecoder : public QObject
{
    Q_OBJECT
    
public:
    enum class HardwareType {
        None,
        NVENC,      // NVIDIA NVENC
        AMF,        // AMD AMF
        QSV,        // Intel QuickSync
        VAAPI,      // Linux VA-API
        DXVA2,      // DirectX Video Acceleration 2.0
        D3D11VA     // Direct3D 11 Video Acceleration
    };
    
    enum class DecoderState {
        Idle,
        Initializing,
        Decoding,
        Seeking,
        Error
    };
    
    struct DecodedFrame {
        std::shared_ptr<AVFrame> frame;
        double timestamp;
        int64_t pts;
        bool isKeyFrame;
        uint32_t textureId;
        int width, height;
    };
    
    struct DecoderCapabilities {
        HardwareType preferredHardware;
        QStringList supportedFormats;
        int maxWidth, maxHeight;
        bool supportsHEVC;
        bool supportsAV1;
        bool supportsVP9;
        bool supports10Bit;
        bool supportsHDR;
    };

public:
    explicit GpuDecoder(QObject *parent = nullptr);
    ~GpuDecoder();
    
    // Initialization
    bool initialize(QOpenGLContext *context);
    bool loadFile(const QString &filePath);
    DecoderCapabilities getCapabilities() const;
    
    // Decoding control
    void startDecoding();
    void pauseDecoding();
    void stopDecoding();
    void seek(double timestamp);
    void flush();
    
    // Frame access
    DecodedFrame getNextFrame();
    bool hasFrameReady() const;
    double getCurrentTimestamp() const;
    double getDuration() const;
    
    // Hardware acceleration
    HardwareType getActiveHardwareType() const { return m_activeHardware; }
    bool isHardwareAccelerated() const { return m_activeHardware != HardwareType::None; }
    
    // Performance metrics
    double getDecodingFPS() const { return m_decodingFPS; }
    int getQueueSize() const { return m_frameQueue.size(); }
    int getDroppedFrames() const { return m_droppedFrames; }
    
    // State
    DecoderState getState() const { return m_state; }
    QString getLastError() const { return m_lastError; }

signals:
    void frameReady();
    void seekCompleted(double timestamp);
    void errorOccurred(const QString &error);
    void hardwareAccelerationChanged(HardwareType type);
    void decodingStatsChanged(double fps, int queueSize, int droppedFrames);

private slots:
    void processDecoding();
    void updateStats();

private:
    // Hardware acceleration setup
    bool setupHardwareAcceleration();
    bool initializeNVENC();
    bool initializeAMF();
    bool initializeQSV();
    bool initializeDXVA2();
    bool initializeD3D11VA();
    AVBufferRef* createHardwareDeviceContext(HardwareType type);
    
    // Decoding pipeline
    bool initializeDecoder();
    void cleanupDecoder();
    bool decodeNextPacket();
    void processFrame(AVFrame *frame);
    void uploadFrameToGPU(AVFrame *frame);
    
    // Threading
    void startDecodingThread();
    void stopDecodingThread();
    
    // Synchronization
    mutable QMutex m_mutex;
    QWaitCondition m_condition;
    QThread *m_decodingThread;
    QAtomicInt m_stopRequested;
    
    // FFmpeg components
    AVFormatContext *m_formatContext;
    AVCodecContext *m_codecContext;
    AVBufferRef *m_hwDeviceContext;
    int m_videoStreamIndex;
    
    // Frame management
    QQueue<DecodedFrame> m_frameQueue;
    static const int MAX_FRAME_QUEUE_SIZE = 30;
    
    // OpenGL context
    QOpenGLContext *m_glContext;
    QOpenGLFunctions *m_gl;
    
    // State management
    DecoderState m_state;
    HardwareType m_activeHardware;
    QString m_currentFilePath;
    QString m_lastError;
    
    // Performance tracking
    QTimer *m_statsTimer;
    double m_decodingFPS;
    int m_droppedFrames;
    qint64 m_lastFrameTime;
    int m_frameCount;
    
    // Seeking
    double m_seekTarget;
    bool m_seekRequested;
    
    // Video properties
    double m_duration;
    double m_currentTimestamp;
    int m_width, m_height;
    double m_frameRate;
};

#endif // GPUDECODER_H
