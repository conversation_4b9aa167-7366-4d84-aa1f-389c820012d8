#ifndef ENHANCEDPLAYBACKENGINE_H
#define ENHANCEDPLAYBACKENGINE_H

#include <QObject>
#include <QOpenGLContext>
#include <QImage>
#include <QSize>
#include <QString>
#include <QTimer>
#include <QHash>
#include "MpvWrapper.h"
#include "FrameIndex.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <queue>
#include <chrono>

class EnhancedPlaybackEngine : public QObject
{
    Q_OBJECT

public:
    enum class PlaybackMode {
        Normal,         // Standard playback
        Optimized,      // GPU-accelerated with smart caching
        Preview,        // Low-latency preview mode for scrubbing
        HighQuality     // Maximum quality mode
    };
    
    enum class BufferStrategy {
        Adaptive,       // Adjust buffer size based on content
        Conservative,   // Small buffer for low latency
        Aggressive,     // Large buffer for smooth playback
        Streaming       // Optimized for network streaming
    };
    
    struct PlaybackState {
        bool isPlaying = false;
        bool isPaused = false;
        double currentTime = 0.0;
        double duration = 0.0;
        double playbackSpeed = 1.0;
        int bufferedFrames = 0;
        double bufferHealth = 0.0;  // 0.0 to 1.0
        bool isBuffering = false;
        bool hasVideo = false;
        PlaybackMode mode = PlaybackMode::Normal;
    };
    
    struct PerformanceMetrics {
        double frameRate = 0.0;
        double droppedFrameRate = 0.0;
        int queueSize = 0;
        double decodingLatency = 0.0;
        double renderingLatency = 0.0;
        bool isHardwareAccelerated = false;
        QString hardwareType;
        size_t memoryUsage = 0;
    };

public:
    explicit EnhancedPlaybackEngine(QObject *parent = nullptr);
    ~EnhancedPlaybackEngine();
    
    // Initialization
    bool initialize(QOpenGLContext *glContext);
    bool loadFile(const QString &filePath);
    
    // Playback control
    void play();
    void pause();
    void stop();
    void togglePlayPause();
    
    // Enhanced seeking
    void seek(double timestamp);
    void seekToPosition(double normalizedPosition); // 0.0 to 1.0
    void seekToKeyFrame(double timestamp, bool backward = false);
    void seekRelative(double seconds);
    
    // Speed control
    void setPlaybackSpeed(double speed);
    double getPlaybackSpeed() const;
    
    // J-K-L scrubbing support
    void enableJKLScrubbing(bool enable);
    void handleJKey(); // Step backward / slow reverse
    void handleKKey(); // Pause / stop
    void handleLKey(); // Step forward / fast forward
    
    // Frame stepping
    void stepFrame(int frames = 1);
    void stepToNextKeyFrame();
    void stepToPreviousKeyFrame();
    
    // Playback modes
    void setPlaybackMode(PlaybackMode mode);
    PlaybackMode getPlaybackMode() const { return m_playbackState.mode; }
    
    // Buffer management
    void setBufferStrategy(BufferStrategy strategy);
    void setBufferSize(int frames);
    void preloadRange(double startTime, double endTime);
    
    // Visual preview for scrubbing
    QImage getPreviewAtTime(double timestamp, const QSize &size = QSize(160, 90));
    QImage getCurrentFrame();
    
    // Frame indexing
    bool buildFrameIndex(bool background = true);
    bool isFrameIndexReady() const;
    double getIndexingProgress() const;
    
    // State access
    PlaybackState getPlaybackState() const { return m_playbackState; }
    PerformanceMetrics getPerformanceMetrics() const { return m_metrics; }
    
    // Rendering
    void renderFrame(int width, int height, int fbo = 0);
    bool hasNewFrame() const;
    void acknowledgeFrame();
    
    // Volume control
    void setVolume(int volume);
    int getVolume() const;
    
    // Loop control
    void setLoop(bool enabled);
    bool isLoopEnabled() const;
    
    // Error handling
    QString getLastError() const { return m_lastError; }

signals:
    void frameReady();
    void positionChanged(double position);
    void durationChanged(double duration);
    void playStateChanged(bool playing);
    void bufferStateChanged(bool buffering, double health);
    void performanceMetricsUpdated(const PerformanceMetrics &metrics);
    void frameIndexReady();
    void previewGenerated(double timestamp, const QImage &preview);
    void errorOccurred(const QString &error);

private slots:
    void onFrameReady();
    void onPositionChanged(double position);
    void onDurationChanged(double duration);
    void updateMetrics();
    void processFrameQueue();

private:
    // Core components
    std::unique_ptr<MpvWrapper> m_mpvPlayer;
    std::unique_ptr<FrameIndexer> m_frameIndexer;
    std::unique_ptr<ScrubController> m_scrubController;
    
    // OpenGL context
    QOpenGLContext *m_glContext;
    
    // State management
    PlaybackState m_playbackState;
    PerformanceMetrics m_metrics;
    BufferStrategy m_bufferStrategy;
    QString m_currentFilePath;
    QString m_lastError;
    
    // Frame management
    struct FrameData {
        double timestamp;
        QImage frame;
        bool isKeyFrame;
        std::chrono::steady_clock::time_point decodeTime;
    };
    
    std::queue<FrameData> m_frameQueue;
    std::mutex m_frameQueueMutex;
    static const int MAX_FRAME_QUEUE_SIZE = 30;
    
    // Threading
    std::thread m_processingThread;
    std::atomic<bool> m_stopProcessing{false};
    
    // Performance tracking
    QTimer *m_metricsTimer;
    std::chrono::steady_clock::time_point m_lastMetricsUpdate;
    int m_frameCount;
    int m_droppedFrames;
    
    // Preview generation
    mutable std::mutex m_previewMutex;
    QHash<double, QImage> m_previewCache;
    
    // J-K-L scrubbing
    bool m_jklEnabled;
    std::chrono::steady_clock::time_point m_lastJKLUpdate;
    
    // Buffer management
    int m_targetBufferSize;
    double m_bufferHealthThreshold;
    
    // Helper methods
    void initializeFrameIndexer();
    void startProcessingThread();
    void stopProcessingThread();
    void updatePlaybackState();
    void calculateBufferHealth();
    QImage extractFrameFromMpv();
    void generatePreviewAsync(double timestamp);
    void cleanupOldPreviews();
    void optimizeBufferStrategy();
    void handleBufferUnderrun();
    
    // Scrubbing helpers
    void updateScrubPosition();
    double getOptimalSeekPosition(double targetTime);
};

// Performance optimizer for different hardware configurations
class PlaybackOptimizer
{
public:
    struct SystemCapabilities {
        bool hasHardwareDecoding = false;
        QString hardwareType;
        int cpuCores = 0;
        size_t totalMemory = 0;
        size_t availableMemory = 0;
        bool hasGPUMemory = false;
        size_t gpuMemory = 0;
        QString gpuVendor;
    };
    
    struct OptimizationProfile {
        EnhancedPlaybackEngine::BufferStrategy bufferStrategy;
        int bufferSize;
        EnhancedPlaybackEngine::PlaybackMode recommendedMode;
        bool enableFrameIndexing;
        bool enablePreviewGeneration;
        int previewCacheSize;
        double seekAccuracy; // Seconds
    };

public:
    static SystemCapabilities detectSystemCapabilities();
    static OptimizationProfile generateOptimizationProfile(const SystemCapabilities &caps);
    static void applyOptimizations(EnhancedPlaybackEngine *engine, const OptimizationProfile &profile);
    
private:
    static size_t getAvailableMemory();
    static QString detectGPUVendor();
    static bool testHardwareDecoding();
};

#endif // ENHANCEDPLAYBACKENGINE_H
