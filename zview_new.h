#ifndef ZVIEW_H
#define ZVIEW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QStringList>
#include <QKeyEvent>

// Forward declarations
class ImageCanvas;
class VideoCanvas;
class ToolPanel;

QT_BEGIN_NAMESPACE
namespace Ui {
class Zview;
}
QT_END_NAMESPACE

class Zview : public QMainWindow
{
    Q_OBJECT

public:
    Zview(QWidget *parent = nullptr);
    ~Zview();
    
    // Public method for opening files (used by main.cpp for command line arguments)
    void openFile(const QString &filePath);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    // Image canvas slots
    void onImageLoaded(const QString &filePath);
    void onImageLoadFailed(const QString &filePath);
    void onZoomChanged(float zoomFactor);
    
    // Video canvas slots
    void onVideoLoaded(const QString &filePath);
    void onVideoLoadFailed(const QString &filePath);
    void onVideoPositionChanged(double position);
    void onVideoDurationChanged(double duration);
    void onVideoPlayStateChanged(bool playing);
    void onVideoVolumeChanged(int volume);
    
    // Tool panel slots
    void onPlayPauseClicked();
    void onStopClicked();
    void onPositionChanged(double position);
    void onVolumeChanged(int volume);
    void onPreviousClicked();
    void onNextClicked();
    void onSetAPointClicked();
    void onSetBPointClicked();
    void onClearABLoopClicked();
    void onZoomInClicked();
    void onZoomOutClicked();
    void onResetZoomClicked();
    void onFitToWindowClicked();
    void onActualSizeClicked();
    void onFullscreenToggled();
    
    // Control visibility
    void onControlVisibilityTimer();

private:
    void setupUI();
    void setupConnections();
    void loadImageFile(const QString &filePath);
    void loadVideoFile(const QString &filePath);
    bool isVideoFile(const QString &filePath);
    bool isImageFile(const QString &filePath);
    void showControls();
    void hideControls();
    void updateWindowTitle(const QString &filePath);
    void setRoundedMask();
    void repositionControls();
    
    Ui::Zview *ui;
    
    // Main UI components
    QWidget *m_centralWidget = nullptr;
    QVBoxLayout *m_mainLayout = nullptr;
    QStackedWidget *m_stackedWidget = nullptr;
    
    // Canvas components
    ImageCanvas *m_imageCanvas = nullptr;
    VideoCanvas *m_videoCanvas = nullptr;
    ToolPanel *m_toolPanel = nullptr;
    
    // State
    enum class ViewMode {
        Image,
        Video
    };
    ViewMode m_currentMode = ViewMode::Image;
    QString m_currentFilePath;
    
    // Control visibility
    QTimer *m_controlVisibilityTimer = nullptr;
    bool m_controlsVisible = false;
    
    // Window management
    int m_cornerRadius = 15;
    bool m_isFullscreen = false;
    QRect m_normalGeometry;
};

#endif // ZVIEW_H


};
#endif // ZVIEW_H
