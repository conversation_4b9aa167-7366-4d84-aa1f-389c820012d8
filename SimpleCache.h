#ifndef SIMPLECACHE_H
#define SIMPLECACHE_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QHash>
#include <QMutex>
#include <QQueue>
#include <QProcess>
#include <QSettings>

// Simplified cache entry for basic functionality
struct SimpleCacheEntry {
    QString filePath;
    QString cachedPath;
    double startTime;
    double endTime;
    qint64 fileSize;
    int accessCount;
    
    SimpleCacheEntry() : startTime(0), endTime(0), fileSize(0), accessCount(0) {}
};

// Simplified proxy job for basic proxy generation
struct SimpleProxyJob {
    QString sourceFile;
    QString outputFile;
    int width;
    int height;
    int bitrate;
    bool isLowRes;
    
    SimpleProxyJob() : width(640), height(360), bitrate(500), isLowRes(true) {}
};

class SimpleProxyManager : public QObject
{
    Q_OBJECT

public:
    explicit SimpleProxyManager(QObject *parent = nullptr);
    ~SimpleProxyManager();    // Basic proxy generation (single proxy approach)
    void generateScrubbingProxy(const QString &filePath);
    
    // Proxy availability
    bool hasScrubbingProxy(const QString &filePath);
    QString getScrubbingProxyPath(const QString &filePath);
    
    // For backward compatibility
    bool hasPreviewProxy(const QString &filePath) { return false; }  // Always use original for preview
    QString getPreviewProxyPath(const QString &filePath) { return QString(); }  // Always use original

signals:
    void proxyGenerationStarted(const QString &sourceFile, const QString &outputFile);
    void proxyGenerationProgress(const QString &sourceFile, int percentage);
    void proxyGenerationCompleted(const QString &sourceFile, const QString &outputFile);
    void proxyGenerationFailed(const QString &sourceFile, const QString &error);

private slots:
    void processQueue();

private:
    QString generateProxyPath(const QString &sourceFile, bool isLowRes);
    void startProxyGeneration(const SimpleProxyJob &job);

    QQueue<SimpleProxyJob> m_jobQueue;
    QTimer *m_processTimer;
    QString m_proxyDirectory;
    bool m_isGenerating;
    SimpleProxyJob m_currentJob;
};

class SimpleSmartCache : public QObject
{
    Q_OBJECT

public:
    explicit SimpleSmartCache(QObject *parent = nullptr);
    ~SimpleSmartCache();

    // Basic interface
    void setProxyManager(SimpleProxyManager *proxyManager);
    void enableSmartCaching(bool enabled);
    void setSegmentDuration(int seconds);
    void setMaxCacheSize(int megabytes);
    void setCacheStrategy(int strategy);
    
    // Video events
    void onVideoLoaded(const QString &filePath);
    void onPositionChanged(double position);
    void onSeekStarted();
    void onSeekFinished();
    void setScrubPosition(double position);
    void preloadVideo(const QString &filePath);

private:
    void cacheAroundPosition(const QString &filePath, double position);
    QString generateCacheKey(const QString &filePath, double startTime, double endTime);

    SimpleProxyManager *m_proxyManager;    QHash<QString, SimpleCacheEntry> m_cacheEntries;
    QMutex m_cacheMutex;
    
    bool m_smartCachingEnabled;
    bool m_enabled;
    int m_segmentDuration;
    int m_maxCacheSize;
    int m_cacheStrategy;
    
    QString m_currentFile;
    QString m_currentVideoFile;
    double m_currentPosition;
};

#endif // SIMPLECACHE_H
