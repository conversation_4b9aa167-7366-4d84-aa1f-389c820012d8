# Scrubbing Problem Debugging Guide

## Current Implementation Status
- ✅ Single proxy approach implemented
- ✅ Automatic proxy switching (proxy for scrubbing, original for preview)
- ✅ Enhanced debug output added
- ✅ Build successful

## Debug Output Added
The application now logs:
1. **Proxy availability**: When scrubbing starts, shows if proxy exists
2. **Proxy switching**: Logs when switching between proxy and original
3. **Scrubbing actions**: Logs each scrubbing position update
4. **Mode transitions**: Logs when entering/exiting scrubbing mode

## Testing Steps

### 1. Load a Video File
- Open Zview application
- Load any video file (MP4, AVI, etc.)
- Check debug output for proxy generation progress

### 2. Test Basic Scrubbing
- Click and drag the seek slider
- **Expected behavior**: 
  - Should see "Scrubbing mode activated" in debug
  - Should see proxy switching messages (if proxy exists)
  - Video should update position smoothly
- **Check debug output for**:
  - Proxy availability messages
  - Scrubbing position updates

### 3. Test Proxy Switching
- Wait for proxy generation to complete
- Try scrubbing again
- **Expected behavior**:
  - Should switch to proxy during scrubbing
  - Should switch back to original when released
- **Check debug output for**:
  - "Switched to scrubbing proxy: [path]"
  - "Switched back to original video for preview"

### 4. Test Without Proxy
- Load a new video (before proxy generation completes)
- Try scrubbing immediately
- **Expected behavior**:
  - Should scrub using original video
  - Should see "No scrubbing proxy available" message

## Common Issues to Check

### Issue 1: Scrubbing Not Responsive
**Possible Causes**:
- MPV not initialized properly
- Slider events not connected
- Video not loaded correctly

**Debug Checks**:
- Look for "Scrubbing to position: X seconds" messages
- Check if m_isScrubbingMode is being set correctly

### Issue 2: Proxy Not Generated
**Possible Causes**:
- Proxy directory not accessible
- Video file path issues
- Proxy generation failing silently

**Debug Checks**:
- Look for proxy generation started/completed messages
- Check if Documents/Zview_Proxies directory exists
- Verify proxy file paths in debug output

### Issue 3: Proxy Switching Not Working
**Possible Causes**:
- Proxy file doesn't exist when scrubbing starts
- File path comparison issues
- MPV file loading failures

**Debug Checks**:
- Look for "getScrubbingProxyPath" messages
- Check proxy file existence
- Verify proxy vs original file paths

### Issue 4: Performance Problems
**Possible Causes**:
- Too frequent position updates
- Large video files
- Insufficient MPV optimization

**Debug Checks**:
- Count frequency of scrubbing position updates
- Check video file size and resolution
- Monitor system resource usage

## Quick Fixes to Try

### Fix 1: Verify Proxy Directory
```
Check: C:\\Users\\<USER>\\Documents\\Zview_Proxies\\
Should contain: [videoname]_[hash]_scrub.mp4 files
```

### Fix 2: Reduce Scrubbing Frequency
If scrubbing is too sensitive, modify the throttling:
```cpp
// In sliderMoved handler, increase the time threshold
if (value == lastValue && (currentTime - lastTime) < 10) { // Increase from 1ms to 10ms
```

### Fix 3: Force Proxy Generation
If proxies aren't generating, try:
```cpp
// Add immediate proxy generation trigger
m_proxyManager->generateScrubbingProxy(filePath);
```

## Next Steps
1. Run the application with debug output
2. Load a video and test scrubbing
3. Share the debug output to identify the specific issue
4. Apply targeted fixes based on findings

The enhanced debug output will help pinpoint exactly where the scrubbing problem occurs.
