# Crop Handle to Crop Area Cursor Fix

## Issue Description
When hovering the mouse cursor from a crop handle (showing double arrow resize cursor) into the crop inside area, the mouse cursor would not change to the move cursor (four-directional arrows). The resize cursor would persist even when clearly inside the crop area.

## Root Cause Analysis

### The Problem
The issue was in the handle detection sensitivity in the `getCropHandleAtPosition()` function:

1. **Handle Size**: Each handle is 12x12 pixels
2. **Detection Area**: The function used `handleGeom.adjusted(-4, -4, 4, 4)` which expanded the detection area by 4 pixels in **all directions**
3. **Total Detection Area**: This created a 20x20 pixel detection area (12 + 8 = 20)
4. **Overlap Problem**: For corner and edge handles positioned at crop boundaries, the expanded detection area extended **4 pixels into the crop area**

### Visualization of the Problem
```
Crop Rectangle: (100, 100, 200, 200)
Corner Handle at (94, 94) with size 12x12 = bounds (94, 94, 106, 106)
Old Detection Area: (90, 90, 110, 110) - extends 4 pixels into crop area!
```

This created a "dead zone" where the cursor would remain in resize mode instead of switching to move mode.

## Solution Implemented

### 1. Smart Asymmetric Handle Detection
Modified `getCropHandleAtPosition()` to use **asymmetric expansion** that extends outward from the crop area but not inward:

```cpp
// Corner handles - expand outward only
if (i == 0) { // Top-left corner
    expandedGeom = handleGeom.adjusted(-4, -4, 1, 1);  // Expand left/up, minimal right/down
} else if (i == 1) { // Top-right corner  
    expandedGeom = handleGeom.adjusted(-1, -4, 4, 1);  // Expand right/up, minimal left/down
}
// ... similar for other corners and edges
```

### 2. Improved Crop Area Detection
Increased the crop area detection tolerance from 1 pixel to 2 pixels to compensate for the reduced handle detection areas:

```cpp
// Old: 1 pixel tolerance
QRect expandedCropRect = m_cropRect.adjusted(-1, -1, 1, 1);

// New: 2 pixel tolerance for better UX
QRect expandedCropRect = m_cropRect.adjusted(-2, -2, 2, 2);
```

## Key Benefits

### ✅ **Immediate Cursor Response**
- Cursor now changes immediately when moving from handle to crop area
- No more "dead zones" where resize cursor persists incorrectly

### ✅ **Precise Handle Detection**
- Handles are still easy to grab and use for resizing
- Detection area extends outward for better usability
- No more unwanted handle activation when clicking in crop area

### ✅ **Better User Experience**
- Clear visual feedback about what action will be performed
- Intuitive cursor behavior matches user expectations
- Professional behavior consistent with industry standards

## Testing Instructions

1. **Launch Zview**: Run `.\build\Release\Zview.exe`
2. **Load an image**: Drag and drop any image file
3. **Enable crop mode**: Click the crop button
4. **Draw a crop region**: Click and drag to create a crop rectangle
5. **Test cursor transitions**:
   - Hover over corner handles → See diagonal resize cursors
   - Move from handle into crop center → Cursor should immediately change to four-directional arrows
   - Move between different handles → Each shows appropriate resize cursor
   - Move outside crop area → Shows crosshair for new crop

## Files Modified
- `c:\Users\<USER>\Desktop\Zview\zview.cpp`: Enhanced `getCropHandleAtPosition()` and crop area detection logic

## Status
✅ **COMPLETE**: Cursor hover behavior now works correctly with immediate response when transitioning from handle to crop area.
