# Inverted Crop Overlay Implementation

## Summary
Successfully implemented an **inverted crop overlay** where the area **outside** the crop region is covered with a transparent blue overlay, while the **inside** crop region remains clear and visible. This provides a much more professional and intuitive crop user experience.

## Visual Changes

### Before:
- Crop region had a light blue background with a blue border
- Rest of the image was clearly visible
- Crop region was visually highlighted but competed with image content

### After:
- **Crop region is completely clear**: Shows the original image with no overlay
- **Outside area is dimmed**: Covered with a semi-transparent blue overlay
- **Professional appearance**: Similar to crop tools in photo editing software

## Technical Implementation

### 1. Custom Paint-Based Overlay
Replaced the simple QWidget-based overlay with custom painting in `paintEvent()`:

```cpp
void Zview::paintEvent(QPaintEvent *event)
{
    // First, let OpenGL render the image content
    QOpenGLWidget::paintEvent(event);
    
    // Then overlay the crop mask using QPainter
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw inverted crop overlay if in crop mode
    if (m_cropModeActive && !m_cropRect.isEmpty() && (m_drawingCrop || m_cropOverlay)) {
        // Create inverted overlay: cover everything except the crop region
        QPainterPath fullPath;
        fullPath.addRect(rect()); // Full widget area
        
        QPainterPath cropPath;
        cropPath.addRect(m_cropRect); // Crop region to exclude
        
        // Subtract crop region from full area to create inverted overlay
        QPainterPath overlayPath = fullPath.subtracted(cropPath);
        
        // Fill the overlay area with semi-transparent blue
        painter.fillPath(overlayPath, QColor(0, 120, 215, 80)); // More transparent
        
        // Draw crop region border
        QPen cropPen(QColor(0, 120, 215, 230), 2); // Solid blue border
        painter.setPen(cropPen);
        painter.setBrush(Qt::NoBrush);
        painter.drawRect(m_cropRect);
    }
    
    // Continue with window border drawing...
}
```

### 2. Real-Time Updates
Added `update()` calls throughout the crop functionality to ensure the overlay redraws immediately:

- **Drawing crop regions**: Updates during mouse drag
- **Moving crop regions**: Updates during movement
- **Resizing via handles**: Updates during handle dragging
- **Show/Hide crop tool**: Updates when entering/exiting crop mode

### 3. Invisible Hit Detection Widget
The `m_cropOverlay` QWidget is now transparent and used only for hit detection:

```cpp
// Create a dummy overlay widget for hit testing (invisible)
m_cropOverlay = new QWidget(this);
m_cropOverlay->setStyleSheet("QWidget { background: transparent; }");
m_cropOverlay->setAttribute(Qt::WA_TransparentForMouseEvents, false);
```

This maintains all the existing mouse interaction logic while the visual overlay is handled by custom painting.

### 4. Path Subtraction Algorithm
Uses `QPainterPath::subtracted()` to create the inverted overlay:

1. **Full Path**: Covers the entire widget area
2. **Crop Path**: Covers only the crop region rectangle  
3. **Overlay Path**: Full path minus crop path = everything except crop region
4. **Fill**: Semi-transparent blue overlay on the subtracted path

## Key Benefits

### 1. **Professional Appearance**
- Matches the UX of professional photo editing software
- Clear focus on the selected crop area
- Non-distracting overlay that doesn't compete with image content

### 2. **Better Visual Clarity**
- Crop region shows the original image with perfect clarity
- Outside areas are dimmed but still visible for context
- Strong visual separation between selected and non-selected areas

### 3. **Improved User Experience**
- Immediately obvious what will be included in the crop
- Easy to see the exact boundaries of the selection
- Intuitive understanding of the crop operation

### 4. **Smooth Performance**
- Efficient painting using Qt's optimized path operations
- Only redraws when necessary (when crop region changes)
- No performance impact on normal image viewing

## Color Settings

### Overlay Color:
- **Color**: Blue (`0, 120, 215`) - Matches Windows accent color
- **Opacity**: `80/255` (~31%) - Dims background without hiding it completely
- **Border**: Solid blue at `230/255` opacity - Clear crop boundary

### Customization Options:
The overlay appearance can be easily customized by modifying these values in `paintEvent()`:
- `QColor(0, 120, 215, 80)` - Change color and opacity of dimmed area
- `QColor(0, 120, 215, 230)` - Change border color and opacity
- Border width: `2` pixels - Adjust border thickness

## Testing Instructions

### To Test the Inverted Crop Overlay:
1. **Launch Zview**: Run `.\build\Debug\Zview.exe`
2. **Load an image**: Drag and drop any image file into the application
3. **Enable crop mode**: Click the crop button in the top center toolbox
4. **Draw a crop region**: Click and drag to create a crop rectangle
5. **Observe the overlay**: 
   - ✅ **Inside crop region**: Crystal clear, shows original image
   - ✅ **Outside crop region**: Dimmed with blue semi-transparent overlay
   - ✅ **Border**: Solid blue outline around crop region
6. **Test interactions**: Move and resize the crop region to see real-time overlay updates

## Files Modified
- `c:\Users\<USER>\Desktop\Zview\zview.cpp`: 
  - Modified `paintEvent()` to draw inverted overlay
  - Updated crop creation to use transparent hit detection widget
  - Added `update()` calls for real-time repainting
  - Updated show/hide crop tool functions

## Status
✅ **COMPLETE**: Inverted crop overlay is fully implemented and tested.
✅ **COMPLETE**: Professional appearance with clear crop region and dimmed surroundings.
✅ **COMPLETE**: Real-time updates during all crop operations.
✅ **COMPLETE**: Efficient painting performance with no impact on normal viewing.

The crop tool now provides a much more professional and intuitive visual experience that clearly shows what will be included in the final crop.
