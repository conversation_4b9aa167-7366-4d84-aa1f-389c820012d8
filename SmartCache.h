#ifndef SMARTCACHE_H
#define SMARTCACHE_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QHash>
#include <QMutex>
#include <QElapsedTimer>
#include <QQueue>
#include <memory>

// Forward declarations
class ProxyManager;
class MpvWrapper;

struct CacheSegment {
    QString filePath;
    double startTime;
    double endTime;
    QString cachedPath;
    bool isLoaded;
    QElapsedTimer lastAccessed;
    int hitCount;
    
    CacheSegment() : startTime(0), endTime(0), isLoaded(false), hitCount(0) {
        lastAccessed.start();
    }
};

struct PredictiveCache {
    QString currentFile;
    double currentTime;
    double playbackSpeed;
    QQueue<double> recentSeekPositions;
    QHash<double, int> accessFrequency;  // position -> frequency
    
    PredictiveCache() : currentTime(0), playbackSpeed(1.0) {}
};

class SmartCache : public QObject
{
    Q_OBJECT

public:
    explicit SmartCache(QObject *parent = nullptr);
    ~SmartCache();

    // Main interface
    void setMpvWrapper(MpvWrapper *mpv);
    void setProxyManager(ProxyManager *proxyManager);
    
    // Cache management
    void enableSmartCaching(bool enabled);
    void setSegmentDuration(int seconds);  // Default 30 seconds
    void setMaxCacheSize(int megabytes);   // Default 2GB
    void setCacheStrategy(int strategy);   // 0=LRU, 1=Predictive, 2=Adaptive
    
    // Video loading optimization
    void preloadVideo(const QString &filePath);
    void switchToProxy(const QString &filePath, bool useLowRes = false);
    void switchToOriginal(const QString &filePath);
    
    // Scrubbing optimization
    void enableScrubbing(bool enabled);
    void setScrubPosition(double position);
    void finalizeScrubbing();
    
    // Analytics
    int getCacheHitRate() const;
    int getCurrentCacheSize() const;
    QStringList getCachedFiles() const;

public slots:
    void onVideoLoaded(const QString &filePath);
    void onPositionChanged(double position);
    void onSeekStarted();
    void onSeekFinished();
    void onPlaybackSpeedChanged(double speed);

signals:
    void cacheOptimized();
    void proxyReady(const QString &filePath, const QString &proxyPath);
    void preloadCompleted(const QString &filePath);

private slots:
    void updateCache();
    void analyzeAccessPattern();
    void optimizeCache();

private:
    // Core caching logic
    void cacheAroundPosition(const QString &filePath, double position);
    void predictNextAccess(const QString &filePath, double currentPos);
    void preloadSegments(const QString &filePath, const QList<double> &positions);
    QString getSegmentKey(const QString &filePath, double startTime, double endTime);
    
    // Cache management
    void evictOldSegments();
    void updateAccessStats(const QString &segmentKey);
    bool shouldCache(const QString &filePath, double position);
    
    // Proxy management
    void requestProxyIfNeeded(const QString &filePath);
    void handleProxyGeneration(const QString &filePath);

private:
    // Core components
    MpvWrapper *m_mpvWrapper;
    ProxyManager *m_proxyManager;
    
    // Cache storage
    QHash<QString, CacheSegment> m_cacheSegments;
    QMutex m_cacheMutex;
    
    // Predictive caching
    PredictiveCache m_predictiveData;
    QTimer *m_analysisTimer;
    QTimer *m_cacheUpdateTimer;
    
    // Configuration
    bool m_smartCachingEnabled;
    int m_segmentDuration;      // seconds
    int m_maxCacheSize;         // MB
    int m_cacheStrategy;        // 0=LRU, 1=Predictive, 2=Adaptive
    bool m_scrubbingMode;
    
    // Statistics
    int m_cacheHits;
    int m_cacheMisses;
    
    // Constants
    static const int ANALYSIS_INTERVAL_MS = 5000;      // 5 seconds
    static const int CACHE_UPDATE_INTERVAL_MS = 1000;  // 1 second
    static const int MAX_SEEK_HISTORY = 100;
    static const int PRELOAD_SEGMENTS = 3;             // segments to preload ahead
};

#endif // SMARTCACHE_H
