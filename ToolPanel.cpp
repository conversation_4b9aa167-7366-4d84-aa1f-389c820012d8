#include "ToolPanel.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>
#include <QStyle>
#include <QStyleOption>

ToolPanel::ToolPanel(QWidget *parent)
    : QWidget(parent)
{
    setupUI();
    
    // Auto-hide timer
    m_autoHideTimer = new QTimer(this);
    m_autoHideTimer->setSingleShot(true);
    connect(m_autoHideTimer, &QTimer::timeout, this, &ToolPanel::onAutoHideTimer);
}

ToolPanel::~ToolPanel() = default;

void ToolPanel::setupUI()
{
    setFixedHeight(120);
    setAttribute(Qt::WA_TranslucentBackground);
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(5);
    
    setupSeekBar();
    setupVideoControls();
    setupImageControls();
    
    // Initially hide
    hide();
}

void ToolPanel::setupSeekBar()
{
    m_seekLayout = new QHBoxLayout();
    
    // Time labels
    m_timeLabel = new QLabel("00:00");
    m_timeLabel->setStyleSheet("color: white; font-size: 12px;");
    m_timeLabel->setMinimumWidth(50);
    
    // Seek slider
    m_seekSlider = new QSlider(Qt::Horizontal);
    m_seekSlider->setMinimum(0);
    m_seekSlider->setMaximum(1000);
    m_seekSlider->setValue(0);
    m_seekSlider->setStyleSheet(R"(
        QSlider::groove:horizontal {
            border: 1px solid #bbb;
            background: rgba(255, 255, 255, 0.2);
            height: 4px;
            border-radius: 2px;
        }
        QSlider::handle:horizontal {
            background: #fff;
            border: 1px solid #777;
            width: 16px;
            height: 16px;
            margin: -6px 0;
            border-radius: 8px;
        }
        QSlider::sub-page:horizontal {
            background: #4CAF50;
            border-radius: 2px;
        }
    )");
    
    connect(m_seekSlider, &QSlider::sliderPressed, this, &ToolPanel::onSeekSliderPressed);
    connect(m_seekSlider, &QSlider::sliderReleased, this, &ToolPanel::onSeekSliderReleased);
    connect(m_seekSlider, &QSlider::sliderMoved, this, &ToolPanel::onSeekSliderMoved);
    
    m_durationLabel = new QLabel("00:00");
    m_durationLabel->setStyleSheet("color: white; font-size: 12px;");
    m_durationLabel->setMinimumWidth(50);
    
    m_seekLayout->addWidget(m_timeLabel);
    m_seekLayout->addWidget(m_seekSlider);
    m_seekLayout->addWidget(m_durationLabel);
    
    m_mainLayout->addLayout(m_seekLayout);
}

void ToolPanel::setupVideoControls()
{
    m_controlsLayout = new QHBoxLayout();
    
    // Navigation buttons
    m_prevBtn = new QPushButton("⏮");
    m_prevBtn->setFixedSize(40, 40);
    m_prevBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 16px; }");
    connect(m_prevBtn, &QPushButton::clicked, this, &ToolPanel::previousClicked);
    
    // Play/Pause button
    m_playPauseBtn = new QPushButton("▶");
    m_playPauseBtn->setFixedSize(50, 40);
    m_playPauseBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 20px; }");
    connect(m_playPauseBtn, &QPushButton::clicked, this, &ToolPanel::playPauseClicked);
    
    m_nextBtn = new QPushButton("⏭");
    m_nextBtn->setFixedSize(40, 40);
    m_nextBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 16px; }");
    connect(m_nextBtn, &QPushButton::clicked, this, &ToolPanel::nextClicked);
    
    m_stopBtn = new QPushButton("⏹");
    m_stopBtn->setFixedSize(40, 40);
    m_stopBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 16px; }");
    connect(m_stopBtn, &QPushButton::clicked, this, &ToolPanel::stopClicked);
    
    // A-B Loop controls
    m_aPointBtn = new QPushButton("A");
    m_aPointBtn->setFixedSize(30, 30);
    m_aPointBtn->setStyleSheet("QPushButton { background: rgba(100,100,100,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    connect(m_aPointBtn, &QPushButton::clicked, this, &ToolPanel::setAPointClicked);
    
    m_bPointBtn = new QPushButton("B");
    m_bPointBtn->setFixedSize(30, 30);
    m_bPointBtn->setStyleSheet("QPushButton { background: rgba(100,100,100,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    connect(m_bPointBtn, &QPushButton::clicked, this, &ToolPanel::setBPointClicked);
    
    m_clearABBtn = new QPushButton("Clear");
    m_clearABBtn->setFixedSize(50, 30);
    m_clearABBtn->setStyleSheet("QPushButton { background: rgba(100,100,100,150); color: white; border: none; border-radius: 5px; font-size: 10px; }");
    connect(m_clearABBtn, &QPushButton::clicked, this, &ToolPanel::clearABLoopClicked);
    
    // Volume slider
    m_volumeSlider = new QSlider(Qt::Horizontal);
    m_volumeSlider->setMinimum(0);
    m_volumeSlider->setMaximum(100);
    m_volumeSlider->setValue(50);
    m_volumeSlider->setFixedWidth(80);
    m_volumeSlider->setStyleSheet(m_seekSlider->styleSheet());
    connect(m_volumeSlider, &QSlider::valueChanged, this, &ToolPanel::volumeChanged);
    
    // Fullscreen button
    m_fullscreenBtn = new QPushButton("⛶");
    m_fullscreenBtn->setFixedSize(40, 40);
    m_fullscreenBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 16px; }");
    connect(m_fullscreenBtn, &QPushButton::clicked, this, &ToolPanel::fullscreenToggled);
    
    // Layout
    m_controlsLayout->addWidget(m_prevBtn);
    m_controlsLayout->addWidget(m_playPauseBtn);
    m_controlsLayout->addWidget(m_nextBtn);
    m_controlsLayout->addWidget(m_stopBtn);
    m_controlsLayout->addSpacing(20);
    m_controlsLayout->addWidget(m_aPointBtn);
    m_controlsLayout->addWidget(m_bPointBtn);
    m_controlsLayout->addWidget(m_clearABBtn);
    m_controlsLayout->addStretch();
    m_controlsLayout->addWidget(new QLabel("🔊"));
    m_controlsLayout->addWidget(m_volumeSlider);
    m_controlsLayout->addWidget(m_fullscreenBtn);
    
    m_mainLayout->addLayout(m_controlsLayout);
}

void ToolPanel::setupImageControls()
{
    m_imageControlsLayout = new QHBoxLayout();
    
    // Zoom controls
    m_zoomOutBtn = new QPushButton("🔍-");
    m_zoomOutBtn->setFixedSize(40, 30);
    m_zoomOutBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    connect(m_zoomOutBtn, &QPushButton::clicked, this, &ToolPanel::zoomOutClicked);
    
    m_zoomInBtn = new QPushButton("🔍+");
    m_zoomInBtn->setFixedSize(40, 30);
    m_zoomInBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    connect(m_zoomInBtn, &QPushButton::clicked, this, &ToolPanel::zoomInClicked);
    
    m_resetZoomBtn = new QPushButton("1:1");
    m_resetZoomBtn->setFixedSize(40, 30);
    m_resetZoomBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 10px; }");
    connect(m_resetZoomBtn, &QPushButton::clicked, this, &ToolPanel::resetZoomClicked);
    
    m_fitToWindowBtn = new QPushButton("Fit");
    m_fitToWindowBtn->setFixedSize(40, 30);
    m_fitToWindowBtn->setStyleSheet("QPushButton { background: rgba(0,0,0,150); color: white; border: none; border-radius: 5px; font-size: 10px; }");
    connect(m_fitToWindowBtn, &QPushButton::clicked, this, &ToolPanel::fitToWindowClicked);
    
    // Zoom info
    m_zoomLabel = new QLabel("100%");
    m_zoomLabel->setStyleSheet("color: white; font-size: 12px;");
    m_zoomLabel->setMinimumWidth(50);
    
    // Image info
    m_imageInfoLabel = new QLabel("");
    m_imageInfoLabel->setStyleSheet("color: white; font-size: 10px;");
    
    m_imageControlsLayout->addWidget(m_zoomOutBtn);
    m_imageControlsLayout->addWidget(m_zoomInBtn);
    m_imageControlsLayout->addWidget(m_resetZoomBtn);
    m_imageControlsLayout->addWidget(m_fitToWindowBtn);
    m_imageControlsLayout->addWidget(m_zoomLabel);
    m_imageControlsLayout->addStretch();
    m_imageControlsLayout->addWidget(m_imageInfoLabel);
    
    m_mainLayout->addLayout(m_imageControlsLayout);
}

void ToolPanel::setPlaying(bool playing)
{
    m_isPlaying = playing;
    m_playPauseBtn->setText(playing ? "⏸" : "▶");
}

void ToolPanel::setPosition(double position)
{
    m_currentPosition = position;
    
    if (!m_isSeekingManually && m_currentDuration > 0) {
        int sliderValue = static_cast<int>((position / m_currentDuration) * 1000);
        m_seekSlider->setValue(sliderValue);
    }
    
    updateTimeDisplay();
}

void ToolPanel::setDuration(double duration)
{
    m_currentDuration = duration;
    updateTimeDisplay();
}

void ToolPanel::setVolume(int volume)
{
    m_currentVolume = volume;
    m_volumeSlider->setValue(volume);
}

void ToolPanel::setZoomFactor(float zoom)
{
    m_currentZoom = zoom;
    m_zoomLabel->setText(QString("%1%").arg(static_cast<int>(zoom * 100)));
}

void ToolPanel::setImageInfo(const QString &info)
{
    m_imageInfoLabel->setText(info);
}

void ToolPanel::showControls()
{
    show();
    if (m_autoHideEnabled) {
        m_autoHideTimer->start(AUTO_HIDE_DELAY);
    }
}

void ToolPanel::hideControls()
{
    hide();
    m_autoHideTimer->stop();
}

void ToolPanel::setAutoHide(bool enabled)
{
    m_autoHideEnabled = enabled;
    if (!enabled) {
        m_autoHideTimer->stop();
    }
}

void ToolPanel::setABMarkers(double aPos, double bPos, double duration)
{
    m_aPosition = aPos;
    m_bPosition = bPos;
    m_hasABLoop = (aPos >= 0 && bPos >= 0);
    
    // Update button styles to indicate active A-B points
    if (aPos >= 0) {
        m_aPointBtn->setStyleSheet("QPushButton { background: rgba(76,175,80,200); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    } else {
        m_aPointBtn->setStyleSheet("QPushButton { background: rgba(100,100,100,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    }
    
    if (bPos >= 0) {
        m_bPointBtn->setStyleSheet("QPushButton { background: rgba(76,175,80,200); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    } else {
        m_bPointBtn->setStyleSheet("QPushButton { background: rgba(100,100,100,150); color: white; border: none; border-radius: 5px; font-size: 12px; }");
    }
}

void ToolPanel::clearABMarkers()
{
    setABMarkers(-1.0, -1.0, 0.0);
}

void ToolPanel::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Semi-transparent background
    painter.fillRect(rect(), QColor(0, 0, 0, 150));
    
    // Draw A-B markers on seek bar if they exist
    if (m_hasABLoop && m_currentDuration > 0) {
        QRect seekRect = m_seekSlider->geometry();
        int seekStart = seekRect.left();
        int seekWidth = seekRect.width();
        
        if (m_aPosition >= 0) {
            int aX = seekStart + static_cast<int>((m_aPosition / m_currentDuration) * seekWidth);
            painter.setPen(QPen(QColor(76, 175, 80), 2));
            painter.drawLine(aX, seekRect.top() - 5, aX, seekRect.bottom() + 5);
        }
        
        if (m_bPosition >= 0) {
            int bX = seekStart + static_cast<int>((m_bPosition / m_currentDuration) * seekWidth);
            painter.setPen(QPen(QColor(76, 175, 80), 2));
            painter.drawLine(bX, seekRect.top() - 5, bX, seekRect.bottom() + 5);
        }
    }
    
    QWidget::paintEvent(event);
}

void ToolPanel::mouseMoveEvent(QMouseEvent *event)
{
    if (m_autoHideEnabled) {
        showControls(); // Reset auto-hide timer
    }
    QWidget::mouseMoveEvent(event);
}

void ToolPanel::enterEvent(QEnterEvent *event)
{
    if (m_autoHideEnabled) {
        m_autoHideTimer->stop();
    }
    QWidget::enterEvent(event);
}

void ToolPanel::leaveEvent(QEvent *event)
{
    if (m_autoHideEnabled) {
        m_autoHideTimer->start(AUTO_HIDE_DELAY);
    }
    QWidget::leaveEvent(event);
}

void ToolPanel::onAutoHideTimer()
{
    hideControls();
}

void ToolPanel::onSeekSliderPressed()
{
    m_isSeekingManually = true;
}

void ToolPanel::onSeekSliderReleased()
{
    m_isSeekingManually = false;
    
    if (m_currentDuration > 0) {
        double position = (static_cast<double>(m_seekSlider->value()) / 1000.0) * m_currentDuration;
        emit positionChanged(position);
    }
}

void ToolPanel::onSeekSliderMoved(int value)
{
    if (m_currentDuration > 0) {
        double position = (static_cast<double>(value) / 1000.0) * m_currentDuration;
        m_currentPosition = position;
        updateTimeDisplay();
    }
}

void ToolPanel::updateTimeDisplay()
{
    m_timeLabel->setText(formatTime(m_currentPosition));
    m_durationLabel->setText(formatTime(m_currentDuration));
}

QString ToolPanel::formatTime(double seconds) const
{
    int totalSeconds = static_cast<int>(seconds);
    int minutes = totalSeconds / 60;
    int secs = totalSeconds % 60;
    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(secs, 2, 10, QChar('0'));
}

#include "ToolPanel.moc"
