#ifndef VIDEOCANVAS_H
#define VIDEOCANVAS_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QTimer>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QKeyEvent>

// Forward declarations
class MpvWrapper;

class VideoCanvas : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    explicit VideoCanvas(QWidget *parent = nullptr);
    ~VideoCanvas();

    // Video control
    void loadVideo(const QString &filePath);
    void play();
    void pause();
    void stop();
    void togglePlayPause();
    
    // Playback properties
    void setVolume(int volume);
    int getVolume() const { return m_volume; }
    void setLoop(bool enabled);
    bool isLooping() const { return m_isLooping; }
    
    // Seeking
    void seek(double position);
    double getPosition() const;
    double getDuration() const;
    bool isPaused() const;
    
    // A-B Loop functionality
    void setAPoint();
    void setBPoint();
    void clearABLoop();
    void toggleABLoop();
    bool hasABLoop() const { return m_hasAPoint && m_hasBPoint; }
    
    // Status
    bool hasVideo() const { return m_hasVideo; }
    QString getCurrentVideoPath() const { return m_currentVideoPath; }

signals:
    void videoLoaded(const QString &filePath);
    void videoLoadFailed(const QString &filePath);
    void positionChanged(double position);
    void durationChanged(double duration);
    void playStateChanged(bool playing);
    void volumeChanged(int volume);

protected:
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private slots:
    void onFrameReady();
    void onPositionChanged(double position);
    void onDurationChanged(double duration);
    void checkABLoop();

private:
    void initializeMpv();
    void cleanupMpv();
    bool isVideoFile(const QString &filePath);
    
    // MPV player
    MpvWrapper *m_mpvPlayer = nullptr;
    bool m_hasVideo = false;
    QString m_currentVideoPath;
    
    // Playback state
    int m_volume = 50;
    bool m_isLooping = true;
    
    // A-B Loop
    bool m_hasAPoint = false;
    bool m_hasBPoint = false;
    double m_abLoopStart = 0.0;
    double m_abLoopEnd = 0.0;
    QTimer *m_abLoopTimer = nullptr;
    
    // Frame update
    QTimer *m_frameTimer = nullptr;
};

#endif // VIDEOCANVAS_H
