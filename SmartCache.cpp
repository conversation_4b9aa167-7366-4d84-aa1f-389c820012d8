#include "SmartCache.h"
#include "MpvWrapper.h"
#include "ProxyManager.h"
#include <QDebug>
#include <QFileInfo>
#include <QElapsedTimer>
#include <QtMath>
#include <algorithm>

SmartCache::SmartCache(QObject *parent)
    : QObject(parent)
    , m_mpvWrapper(nullptr)
    , m_proxyManager(nullptr)
    , m_smartCachingEnabled(true)
    , m_segmentDuration(30)
    , m_maxCacheSize(2048)  // 2GB in MB
    , m_cacheStrategy(2)    // Adaptive by default
    , m_scrubbingMode(false)
    , m_cacheHits(0)
    , m_cacheMisses(0)
{
    // Initialize timers
    m_analysisTimer = new QTimer(this);
    connect(m_analysisTimer, &QTimer::timeout, this, &SmartCache::analyzeAccessPattern);
    m_analysisTimer->setInterval(ANALYSIS_INTERVAL_MS);
    m_analysisTimer->start();
    
    m_cacheUpdateTimer = new QTimer(this);
    connect(m_cacheUpdateTimer, &QTimer::timeout, this, &SmartCache::updateCache);
    m_cacheUpdateTimer->setInterval(CACHE_UPDATE_INTERVAL_MS);
    m_cacheUpdateTimer->start();
    
    qDebug() << "SmartCache initialized";
}

SmartCache::~SmartCache()
{
    // Cleanup is handled by Qt's parent-child system
}

void SmartCache::setMpvWrapper(MpvWrapper *mpv)
{
    m_mpvWrapper = mpv;
    
    if (m_mpvWrapper) {
        connect(m_mpvWrapper, &MpvWrapper::positionChanged, 
                this, &SmartCache::onPositionChanged);
        connect(m_mpvWrapper, &MpvWrapper::durationChanged,
                this, [this](double duration) {
                    // Initialize predictive caching for new video
                    if (!m_predictiveData.currentFile.isEmpty()) {
                        requestProxyIfNeeded(m_predictiveData.currentFile);
                    }
                });
    }
}

void SmartCache::setProxyManager(ProxyManager *proxyManager)
{
    m_proxyManager = proxyManager;
    
    if (m_proxyManager) {
        connect(m_proxyManager, &ProxyManager::proxyGenerationCompleted,
                this, [this](const QString &filePath, const QString &proxyPath) {
                    emit proxyReady(filePath, proxyPath);
                    qDebug() << "Proxy ready for smart caching:" << proxyPath;
                });
    }
}

void SmartCache::enableSmartCaching(bool enabled)
{
    m_smartCachingEnabled = enabled;
    
    if (enabled) {
        m_analysisTimer->start();
        m_cacheUpdateTimer->start();
        qDebug() << "Smart caching enabled";
    } else {
        m_analysisTimer->stop();
        m_cacheUpdateTimer->stop();
        qDebug() << "Smart caching disabled";
    }
}

void SmartCache::setSegmentDuration(int seconds)
{
    m_segmentDuration = qMax(10, qMin(300, seconds));  // Clamp between 10s and 5min
}

void SmartCache::setMaxCacheSize(int megabytes)
{
    m_maxCacheSize = qMax(100, megabytes);  // Minimum 100MB
    
    // Trigger optimization if we're over the new limit
    if (getCurrentCacheSize() > m_maxCacheSize) {
        optimizeCache();
    }
}

void SmartCache::setCacheStrategy(int strategy)
{
    m_cacheStrategy = qMax(0, qMin(2, strategy));
}

void SmartCache::preloadVideo(const QString &filePath)
{
    if (!m_smartCachingEnabled || !m_mpvWrapper) {
        return;
    }
    
    // Request proxy generation for smooth scrubbing
    requestProxyIfNeeded(filePath);
    
    // Cache the first few segments
    QList<double> positions;
    for (int i = 0; i < PRELOAD_SEGMENTS; ++i) {
        positions.append(i * m_segmentDuration);
    }
    
    preloadSegments(filePath, positions);
    
    qDebug() << "Preloading video:" << filePath;
}

void SmartCache::switchToProxy(const QString &filePath, bool useLowRes)
{
    if (!m_proxyManager || !m_mpvWrapper) {
        return;
    }
    
    QString proxyPath;
    if (useLowRes && m_proxyManager->hasScrubbingProxy(filePath)) {
        proxyPath = m_proxyManager->getScrubbingProxyPath(filePath);
    } else if (!useLowRes && m_proxyManager->hasPreviewProxy(filePath)) {
        proxyPath = m_proxyManager->getPreviewProxyPath(filePath);
    }
    
    if (!proxyPath.isEmpty()) {
        double currentPosition = m_mpvWrapper->getPosition();
        m_mpvWrapper->loadFile(proxyPath);
        
        // Restore position
        QTimer::singleShot(100, [this, currentPosition]() {
            if (m_mpvWrapper) {
                m_mpvWrapper->seek(currentPosition);
            }
        });
        
        qDebug() << "Switched to proxy:" << proxyPath;
    }
}

void SmartCache::switchToOriginal(const QString &filePath)
{
    if (!m_mpvWrapper) {
        return;
    }
    
    double currentPosition = m_mpvWrapper->getPosition();
    m_mpvWrapper->loadFile(filePath);
    
    // Restore position
    QTimer::singleShot(100, [this, currentPosition]() {
        if (m_mpvWrapper) {
            m_mpvWrapper->seek(currentPosition);
        }
    });
    
    qDebug() << "Switched back to original:" << filePath;
}

void SmartCache::enableScrubbing(bool enabled)
{
    m_scrubbingMode = enabled;
    
    if (enabled && !m_predictiveData.currentFile.isEmpty()) {
        // Switch to low-res proxy for scrubbing if available
        switchToProxy(m_predictiveData.currentFile, true);
    }
}

void SmartCache::setScrubPosition(double position)
{
    if (m_scrubbingMode) {
        // Update predictive data for scrubbing
        m_predictiveData.recentSeekPositions.enqueue(position);
        if (m_predictiveData.recentSeekPositions.size() > MAX_SEEK_HISTORY / 10) {
            m_predictiveData.recentSeekPositions.dequeue();
        }
        
        // Cache around scrub position
        if (!m_predictiveData.currentFile.isEmpty()) {
            cacheAroundPosition(m_predictiveData.currentFile, position);
        }
    }
}

void SmartCache::finalizeScrubbing()
{
    m_scrubbingMode = false;
    
    // Switch back to original or high-quality proxy
    if (!m_predictiveData.currentFile.isEmpty()) {
        switchToOriginal(m_predictiveData.currentFile);
    }
}

int SmartCache::getCacheHitRate() const
{
    int total = m_cacheHits + m_cacheMisses;
    return total > 0 ? (m_cacheHits * 100) / total : 0;
}

int SmartCache::getCurrentCacheSize() const
{
    QMutexLocker locker(&m_cacheMutex);
    
    int totalSize = 0;
    for (const CacheSegment &segment : m_cacheSegments) {
        if (segment.isLoaded && QFileInfo::exists(segment.cachedPath)) {
            totalSize += QFileInfo(segment.cachedPath).size() / (1024 * 1024);  // Convert to MB
        }
    }
    
    return totalSize;
}

QStringList SmartCache::getCachedFiles() const
{
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList files;
    for (const CacheSegment &segment : m_cacheSegments) {
        if (!files.contains(segment.filePath)) {
            files.append(segment.filePath);
        }
    }
    
    return files;
}

void SmartCache::onVideoLoaded(const QString &filePath)
{
    m_predictiveData.currentFile = filePath;
    m_predictiveData.currentTime = 0;
    m_predictiveData.recentSeekPositions.clear();
    m_predictiveData.accessFrequency.clear();
    
    preloadVideo(filePath);
    
    qDebug() << "Video loaded in smart cache:" << filePath;
}

void SmartCache::onPositionChanged(double position)
{
    if (!m_smartCachingEnabled) {
        return;
    }
    
    m_predictiveData.currentTime = position;
    
    // Update access frequency
    int positionBucket = int(position / m_segmentDuration) * m_segmentDuration;
    m_predictiveData.accessFrequency[positionBucket]++;
    
    // Predictive caching
    if (!m_scrubbingMode) {
        predictNextAccess(m_predictiveData.currentFile, position);
    }
}

void SmartCache::onSeekStarted()
{
    enableScrubbing(true);
}

void SmartCache::onSeekFinished()
{
    finalizeScrubbing();
}

void SmartCache::onPlaybackSpeedChanged(double speed)
{
    m_predictiveData.playbackSpeed = speed;
    
    // Adjust caching strategy based on playback speed
    if (qAbs(speed) > 2.0) {
        // High-speed playback, cache more aggressively
        m_cacheUpdateTimer->setInterval(CACHE_UPDATE_INTERVAL_MS / 2);
    } else {
        m_cacheUpdateTimer->setInterval(CACHE_UPDATE_INTERVAL_MS);
    }
}

void SmartCache::updateCache()
{
    if (!m_smartCachingEnabled || m_predictiveData.currentFile.isEmpty()) {
        return;
    }
    
    // Cache around current position
    cacheAroundPosition(m_predictiveData.currentFile, m_predictiveData.currentTime);
    
    // Optimize cache size if needed
    if (getCurrentCacheSize() > m_maxCacheSize) {
        optimizeCache();
    }
}

void SmartCache::analyzeAccessPattern()
{
    if (!m_smartCachingEnabled || m_predictiveData.currentFile.isEmpty()) {
        return;
    }
    
    // Analyze recent seek patterns
    if (m_predictiveData.recentSeekPositions.size() >= 3) {
        // Look for patterns in seeking behavior
        QList<double> positions = m_predictiveData.recentSeekPositions.toList();
        std::sort(positions.begin(), positions.end());
        
        // Cache positions that are frequently accessed
        for (double pos : positions) {
            int bucket = int(pos / m_segmentDuration) * m_segmentDuration;
            if (m_predictiveData.accessFrequency[bucket] > 2) {
                cacheAroundPosition(m_predictiveData.currentFile, bucket);
            }
        }
    }
}

void SmartCache::optimizeCache()
{
    QMutexLocker locker(&m_cacheMutex);
    
    if (m_cacheSegments.isEmpty()) {
        return;
    }
    
    // Sort segments by eviction priority based on strategy
    QList<QString> keysToEvict;
    
    switch (m_cacheStrategy) {
    case 0: {  // LRU
        QList<QPair<qint64, QString>> segments;
        for (auto it = m_cacheSegments.begin(); it != m_cacheSegments.end(); ++it) {
            segments.append({it->lastAccessed.elapsed(), it.key()});
        }
        std::sort(segments.begin(), segments.end(), std::greater<QPair<qint64, QString>>());
        
        int toRemove = qMax(1, m_cacheSegments.size() / 4);  // Remove 25%
        for (int i = 0; i < toRemove && i < segments.size(); ++i) {
            keysToEvict.append(segments[i].second);
        }
        break;
    }
    case 1: {  // Predictive
        // Keep segments that are likely to be accessed soon
        double currentPos = m_predictiveData.currentTime;
        for (auto it = m_cacheSegments.begin(); it != m_cacheSegments.end(); ++it) {
            const CacheSegment &segment = it.value();
            double distance = qAbs(segment.startTime - currentPos);
            
            // Evict segments that are far from current position and rarely accessed
            if (distance > m_segmentDuration * 5 && segment.hitCount < 2) {
                keysToEvict.append(it.key());
            }
        }
        break;
    }
    case 2: {  // Adaptive
        // Combine recency, frequency, and predictive factors
        QList<QPair<double, QString>> segments;
        double currentPos = m_predictiveData.currentTime;
        
        for (auto it = m_cacheSegments.begin(); it != m_cacheSegments.end(); ++it) {
            const CacheSegment &segment = it.value();
            
            double recencyScore = 1.0 / (1.0 + segment.lastAccessed.elapsed() / 1000.0);
            double frequencyScore = segment.hitCount;
            double proximityScore = 1.0 / (1.0 + qAbs(segment.startTime - currentPos));
            
            double totalScore = recencyScore + frequencyScore + proximityScore;
            segments.append({totalScore, it.key()});
        }
        
        std::sort(segments.begin(), segments.end());
        
        int toRemove = qMax(1, m_cacheSegments.size() / 3);  // Remove 33%
        for (int i = 0; i < toRemove && i < segments.size(); ++i) {
            keysToEvict.append(segments[i].second);
        }
        break;
    }
    }
    
    // Evict selected segments
    for (const QString &key : keysToEvict) {
        if (m_cacheSegments.contains(key)) {
            const CacheSegment &segment = m_cacheSegments[key];
            QFile::remove(segment.cachedPath);
            m_cacheSegments.remove(key);
        }
    }
    
    emit cacheOptimized();
    qDebug() << "Cache optimized, evicted" << keysToEvict.size() << "segments";
}

void SmartCache::cacheAroundPosition(const QString &filePath, double position)
{
    // Cache segments around the given position
    for (int i = -1; i <= 1; ++i) {  // Cache previous, current, and next segment
        double segmentStart = qMax(0.0, position + i * m_segmentDuration);
        double segmentEnd = segmentStart + m_segmentDuration;
        
        QString segmentKey = getSegmentKey(filePath, segmentStart, segmentEnd);
        
        if (!m_cacheSegments.contains(segmentKey)) {
            // Create cache entry
            CacheSegment segment;
            segment.filePath = filePath;
            segment.startTime = segmentStart;
            segment.endTime = segmentEnd;
            segment.cachedPath = QString("/tmp/cache_%1_%2_%3.mp4")
                                   .arg(QFileInfo(filePath).baseName())
                                   .arg(int(segmentStart))
                                   .arg(int(segmentEnd));
            segment.isLoaded = false;
            segment.hitCount = 0;
            
            m_cacheSegments[segmentKey] = segment;
            
            // TODO: Actually extract and cache the segment using FFmpeg
            // For now, just mark as conceptually cached
            qDebug() << "Would cache segment:" << segmentStart << "-" << segmentEnd << "of" << filePath;
        } else {
            // Update access stats
            updateAccessStats(segmentKey);
        }
    }
}

void SmartCache::predictNextAccess(const QString &filePath, double currentPos)
{
    // Simple prediction: cache ahead based on playback direction and speed
    double direction = m_predictiveData.playbackSpeed >= 0 ? 1.0 : -1.0;
    double speed = qAbs(m_predictiveData.playbackSpeed);
    
    // Predict positions based on current playback
    QList<double> predictedPositions;
    for (int i = 1; i <= PRELOAD_SEGMENTS; ++i) {
        double predictedPos = currentPos + direction * i * m_segmentDuration * speed;
        if (predictedPos >= 0) {
            predictedPositions.append(predictedPos);
        }
    }
    
    // Also predict based on access frequency
    for (auto it = m_predictiveData.accessFrequency.begin(); 
         it != m_predictiveData.accessFrequency.end(); ++it) {
        if (it.value() > 3 && qAbs(it.key() - currentPos) < m_segmentDuration * 10) {
            predictedPositions.append(it.key());
        }
    }
    
    preloadSegments(filePath, predictedPositions);
}

void SmartCache::preloadSegments(const QString &filePath, const QList<double> &positions)
{
    for (double position : positions) {
        cacheAroundPosition(filePath, position);
    }
}

QString SmartCache::getSegmentKey(const QString &filePath, double startTime, double endTime)
{
    return QString("%1_%2_%3").arg(filePath).arg(int(startTime)).arg(int(endTime));
}

void SmartCache::evictOldSegments()
{
    // This is handled by optimizeCache()
    optimizeCache();
}

void SmartCache::updateAccessStats(const QString &segmentKey)
{
    QMutexLocker locker(&m_cacheMutex);
    
    if (m_cacheSegments.contains(segmentKey)) {
        CacheSegment &segment = m_cacheSegments[segmentKey];
        segment.hitCount++;
        segment.lastAccessed.restart();
        m_cacheHits++;
    } else {
        m_cacheMisses++;
    }
}

bool SmartCache::shouldCache(const QString &filePath, double position)
{
    // Always cache if smart caching is enabled and we have space
    return m_smartCachingEnabled && getCurrentCacheSize() < m_maxCacheSize;
}

void SmartCache::requestProxyIfNeeded(const QString &filePath)
{
    if (!m_proxyManager) {
        return;
    }
    
    // Request both scrubbing and preview proxies
    if (!m_proxyManager->hasScrubbingProxy(filePath)) {
        m_proxyManager->generateScrubbingProxy(filePath);
    }
    
    if (!m_proxyManager->hasPreviewProxy(filePath)) {
        m_proxyManager->generatePreviewProxy(filePath);
    }
}

void SmartCache::handleProxyGeneration(const QString &filePath)
{
    // This is handled by the connected signal from ProxyManager
    qDebug() << "Handling proxy generation for:" << filePath;
}
