#include "ProxyManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QCryptographicHash>
#include <QDebug>
#include <QDateTime>
#include <QRegularExpression>
#include <QApplication>

ProxyCache* ProxyCache::s_instance = nullptr;

ProxyManager::ProxyManager(QObject *parent)
    : QObject(parent)
    , m_maxConcurrentJobs(2)  // Default to 2 concurrent proxy generation jobs
{
    // Initialize proxy directory in Documents folder
    m_proxyDirectory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/Zview_Proxies";
    ensureProxyDirectory();
    
    // Initialize settings
    m_settings = new QSettings(this);
    
    // Try to find FFmpeg
    m_ffmpegPath = "ffmpeg";  // Assume it's in PATH by default
    
    // Initialize processing timer
    m_processTimer = new QTimer(this);
    connect(m_processTimer, &QTimer::timeout, this, &ProxyManager::processQueue);
    m_processTimer->setInterval(1000);  // Check every second
    m_processTimer->start();
    
    qDebug() << "ProxyManager initialized with directory:" << m_proxyDirectory;
}

ProxyManager::~ProxyManager()
{
    // Cancel all active jobs
    for (auto it = m_activeJobs.begin(); it != m_activeJobs.end(); ++it) {
        QProcess *process = it.key();
        if (process->state() != QProcess::NotRunning) {
            process->kill();
            process->waitForFinished(3000);
        }
        process->deleteLater();
    }
    m_activeJobs.clear();
}

void ProxyManager::generateScrubbingProxy(const QString &filePath)
{
    generateProxyWithSettings(filePath, SCRUBBING_PROXY_WIDTH, SCRUBBING_PROXY_HEIGHT, 
                             SCRUBBING_PROXY_BITRATE, 0);  // High priority
}

void ProxyManager::generateProxyWithSettings(const QString &filePath, int width, int height, int bitrate, int priority)
{
    QMutexLocker locker(&m_queueMutex);
    
    // Single proxy approach - always generate scrubbing proxy
    bool isLowRes = true;  // Always low-res for scrubbing
    QString outputPath = generateProxyPath(filePath, isLowRes);
    
    // Check if proxy already exists
    if (QFileInfo::exists(outputPath)) {
        ProxyCache::instance()->addProxy(filePath, outputPath, isLowRes);
        emit proxyGenerationCompleted(filePath, outputPath);
        return;
    }
    
    // Check if already in queue
    for (const ProxyJob &job : m_jobQueue) {
        if (job.sourceFilePath == filePath) {
            return;  // Already queued (no need to check isLowRes since we only have one type)
        }
    }
    
    // Check if currently being processed
    for (auto it = m_activeJobs.begin(); it != m_activeJobs.end(); ++it) {
        const ProxyJob &job = it.value();
        if (job.sourceFilePath == filePath) {
            return;  // Already being processed
        }
    }
    
    // Add to queue
    ProxyJob job;
    job.sourceFilePath = filePath;
    job.outputFilePath = outputPath;
    job.width = width;
    job.height = height;
    job.bitrate = bitrate;
    job.isLowRes = isLowRes;
    job.priority = priority;
    job.queued = QDateTime::currentDateTime();
    
    // Insert in priority order
    auto it = m_jobQueue.begin();
    while (it != m_jobQueue.end() && it->priority <= priority) {
        ++it;
    }
    m_jobQueue.insert(it, job);
    
    emit queueSizeChanged(m_jobQueue.size());
    qDebug() << "Queued proxy generation for:" << filePath << "Size:" << width << "x" << height;
}

bool ProxyManager::hasScrubbingProxy(const QString &filePath)
{
    return ProxyCache::instance()->hasProxy(filePath, true) || 
           QFileInfo::exists(generateProxyPath(filePath, true));
}

QString ProxyManager::getScrubbingProxyPath(const QString &filePath)
{
    QString cachedPath = ProxyCache::instance()->getProxy(filePath, true);
    if (!cachedPath.isEmpty() && QFileInfo::exists(cachedPath)) {
        return cachedPath;
    }
    
    QString proxyPath = generateProxyPath(filePath, true);
    if (QFileInfo::exists(proxyPath)) {
        ProxyCache::instance()->addProxy(filePath, proxyPath, true);
        return proxyPath;
    }
    
    return QString();
}

void ProxyManager::clearQueue()
{
    QMutexLocker locker(&m_queueMutex);
    m_jobQueue.clear();
    emit queueSizeChanged(0);
}

void ProxyManager::cancelJob(const QString &filePath)
{
    QMutexLocker locker(&m_queueMutex);
    
    // Remove from queue
    auto it = m_jobQueue.begin();
    while (it != m_jobQueue.end()) {
        if (it->sourceFilePath == filePath) {
            it = m_jobQueue.erase(it);
        } else {
            ++it;
        }
    }
    
    // Cancel active jobs
    for (auto activeIt = m_activeJobs.begin(); activeIt != m_activeJobs.end(); ++activeIt) {
        if (activeIt.value().sourceFilePath == filePath) {
            QProcess *process = activeIt.key();
            if (process->state() != QProcess::NotRunning) {
                process->kill();
            }
            break;
        }
    }
    
    emit queueSizeChanged(m_jobQueue.size());
}

int ProxyManager::getQueueSize()
{
    QMutexLocker locker(&m_queueMutex);
    return m_jobQueue.size();
}

QStringList ProxyManager::getQueuedFiles()
{
    QMutexLocker locker(&m_queueMutex);
    QStringList files;
    for (const ProxyJob &job : m_jobQueue) {
        files.append(job.sourceFilePath);
    }
    return files;
}

void ProxyManager::setProxyDirectory(const QString &path)
{
    m_proxyDirectory = path;
    ensureProxyDirectory();
}

void ProxyManager::setMaxConcurrentJobs(int maxJobs)
{
    m_maxConcurrentJobs = qMax(1, maxJobs);
}

void ProxyManager::setFFmpegPath(const QString &path)
{
    m_ffmpegPath = path;
}

void ProxyManager::cleanupOldProxies(int daysOld)
{
    QDir dir(m_proxyDirectory);
    QDateTime cutoff = QDateTime::currentDateTime().addDays(-daysOld);
    
    QStringList filters;
    filters << "*.mp4" << "*.mov" << "*.avi";
    
    QFileInfoList files = dir.entryInfoList(filters, QDir::Files);
    int removed = 0;
    
    for (const QFileInfo &fileInfo : files) {
        if (fileInfo.lastModified() < cutoff) {
            if (QFile::remove(fileInfo.absoluteFilePath())) {
                removed++;
                // Remove from cache
                QString sourceFile;  // We'd need to reverse-lookup this
                ProxyCache::instance()->removeProxy(sourceFile, true);
                ProxyCache::instance()->removeProxy(sourceFile, false);
            }
        }
    }
    
    qDebug() << "Cleaned up" << removed << "old proxy files";
}

qint64 ProxyManager::getProxyDirectorySize()
{
    QDir dir(m_proxyDirectory);
    qint64 size = 0;
    
    const QFileInfoList files = dir.entryInfoList(QDir::Files | QDir::Readable, QDir::Size);
    for (const QFileInfo &fileInfo : files) {
        size += fileInfo.size();
    }
    
    return size;
}

void ProxyManager::processQueue()
{
    // Start new jobs if we have capacity
    while (m_activeJobs.size() < m_maxConcurrentJobs && !m_jobQueue.isEmpty()) {
        startNextJob();
    }
}

void ProxyManager::onProxyProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    QProcess *process = qobject_cast<QProcess*>(sender());
    if (!process || !m_activeJobs.contains(process)) {
        return;
    }
    
    ProxyJob job = m_activeJobs.take(process);
    
    if (exitCode == 0 && exitStatus == QProcess::NormalExit) {
        // Success
        if (QFileInfo::exists(job.outputFilePath)) {
            ProxyCache::instance()->addProxy(job.sourceFilePath, job.outputFilePath, job.isLowRes);
            emit proxyGenerationCompleted(job.sourceFilePath, job.outputFilePath);
            qDebug() << "Proxy generation completed:" << job.outputFilePath;
        } else {
            emit proxyGenerationFailed(job.sourceFilePath, "Output file not created");
        }
    } else {
        // Failure
        QString error = QString("FFmpeg exited with code %1").arg(exitCode);
        emit proxyGenerationFailed(job.sourceFilePath, error);
        qDebug() << "Proxy generation failed:" << job.sourceFilePath << error;
    }
    
    process->deleteLater();
    emit queueSizeChanged(m_jobQueue.size());
}

void ProxyManager::onProxyProcessError(QProcess::ProcessError error)
{
    QProcess *process = qobject_cast<QProcess*>(sender());
    if (!process || !m_activeJobs.contains(process)) {
        return;
    }
    
    ProxyJob job = m_activeJobs.take(process);
    
    QString errorString;
    switch (error) {
    case QProcess::FailedToStart:
        errorString = "Failed to start FFmpeg";
        break;
    case QProcess::Crashed:
        errorString = "FFmpeg crashed";
        break;
    case QProcess::Timedout:
        errorString = "FFmpeg timed out";
        break;
    default:
        errorString = "Unknown error";
        break;
    }
    
    emit proxyGenerationFailed(job.sourceFilePath, errorString);
    process->deleteLater();
    emit queueSizeChanged(m_jobQueue.size());
}

void ProxyManager::onProxyProcessOutput()
{
    QProcess *process = qobject_cast<QProcess*>(sender());
    if (!process || !m_activeJobs.contains(process)) {
        return;
    }
    
    ProxyJob job = m_activeJobs[process];
    QByteArray data = process->readAllStandardError();
    QString output = QString::fromUtf8(data);
    
    updateProgress(output, job);
}

QString ProxyManager::generateProxyPath(const QString &sourceFile, bool isLowRes)
{
    QString key = generateProxyKey(sourceFile);
    // Single proxy approach - always generate scrubbing proxy
    QString suffix = "_scrub";  // Only scrubbing proxies are generated
    return m_proxyDirectory + "/" + key + suffix + ".mp4";
}

QString ProxyManager::generateProxyKey(const QString &sourceFile)
{
    QFileInfo fileInfo(sourceFile);
    QString data = fileInfo.absoluteFilePath() + QString::number(fileInfo.lastModified().toSecsSinceEpoch());
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Md5).toHex();
}

void ProxyManager::startNextJob()
{
    if (m_jobQueue.isEmpty() || m_activeJobs.size() >= m_maxConcurrentJobs) {
        return;
    }
    
    QMutexLocker locker(&m_queueMutex);
    ProxyJob job = m_jobQueue.dequeue();
    locker.unlock();
    
    // Create FFmpeg process
    QProcess *process = new QProcess(this);
    
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &ProxyManager::onProxyProcessFinished);
    connect(process, &QProcess::errorOccurred, this, &ProxyManager::onProxyProcessError);
    connect(process, &QProcess::readyReadStandardError, this, &ProxyManager::onProxyProcessOutput);
    
    // Build FFmpeg arguments
    QStringList args = buildFFmpegArgs(job);
    
    m_activeJobs[process] = job;
    
    emit proxyGenerationStarted(job.sourceFilePath, job.outputFilePath);
    
    process->start(m_ffmpegPath, args);
    
    if (!process->waitForStarted(5000)) {
        emit proxyGenerationFailed(job.sourceFilePath, "Failed to start FFmpeg");
        m_activeJobs.remove(process);
        process->deleteLater();
    }
    
    emit queueSizeChanged(m_jobQueue.size());
}

void ProxyManager::ensureProxyDirectory()
{
    QDir dir(m_proxyDirectory);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
}

QStringList ProxyManager::buildFFmpegArgs(const ProxyJob &job)
{
    QStringList args;
    
    args << "-i" << job.sourceFilePath
         << "-vf" << QString("scale=%1:%2:force_original_aspect_ratio=decrease,pad=%1:%2:(ow-iw)/2:(oh-ih)/2")
                      .arg(job.width).arg(job.height)
         << "-c:v" << "libx264"
         << "-preset" << "medium"
         << "-crf" << "23"
         << "-maxrate" << QString("%1k").arg(job.bitrate)
         << "-bufsize" << QString("%1k").arg(job.bitrate * 2)
         << "-c:a" << "aac"
         << "-b:a" << "128k"
         << "-ar" << "44100"
         << "-movflags" << "+faststart"
         << "-pix_fmt" << "yuv420p"
         << "-f" << "mp4"
         << "-y" << job.outputFilePath;
    
    return args;
}

void ProxyManager::updateProgress(const QString &output, const ProxyJob &job)
{
    // Parse FFmpeg output for progress information
    QRegularExpression timeRegex(R"(time=(\d+):(\d+):(\d+\.\d+))");
    QRegularExpressionMatch match = timeRegex.match(output);
    
    if (match.hasMatch()) {
        double hours = match.captured(1).toDouble();
        double minutes = match.captured(2).toDouble();
        double seconds = match.captured(3).toDouble();
        double progressTime = hours * 3600 + minutes * 60 + seconds;
        
        // We'd need the total duration to calculate accurate percentage
        // For now, just emit a basic progress indication
        static QHash<QString, double> lastProgressTime;
        
        if (progressTime > lastProgressTime[job.sourceFilePath]) {
            lastProgressTime[job.sourceFilePath] = progressTime;
            
            // Rough estimate - assume most videos are less than 2 hours
            int progress = qMin(95, qMax(0, int(progressTime / 7200.0 * 100)));
            emit proxyGenerationProgress(job.sourceFilePath, progress);
        }
    }
}

// ProxyCache implementation
ProxyCache* ProxyCache::instance()
{
    if (!s_instance) {
        s_instance = new ProxyCache();
    }
    return s_instance;
}

void ProxyCache::addProxy(const QString &sourceFile, const QString &proxyFile, bool isLowRes)
{
    QMutexLocker locker(&m_mutex);
    if (isLowRes) {
        m_scrubbingProxies[sourceFile] = proxyFile;
    } else {
        m_previewProxies[sourceFile] = proxyFile;
    }
}

void ProxyCache::removeProxy(const QString &sourceFile, bool isLowRes)
{
    QMutexLocker locker(&m_mutex);
    if (isLowRes) {
        m_scrubbingProxies.remove(sourceFile);
    } else {
        m_previewProxies.remove(sourceFile);
    }
}

QString ProxyCache::getProxy(const QString &sourceFile, bool isLowRes)
{
    QMutexLocker locker(&m_mutex);
    if (isLowRes) {
        return m_scrubbingProxies.value(sourceFile, QString());
    } else {
        return m_previewProxies.value(sourceFile, QString());
    }
}

bool ProxyCache::hasProxy(const QString &sourceFile, bool isLowRes)
{
    QMutexLocker locker(&m_mutex);
    if (isLowRes) {
        return m_scrubbingProxies.contains(sourceFile);
    } else {
        return m_previewProxies.contains(sourceFile);
    }
}

void ProxyCache::clear()
{
    QMutexLocker locker(&m_mutex);
    m_scrubbingProxies.clear();
    m_previewProxies.clear();
}
