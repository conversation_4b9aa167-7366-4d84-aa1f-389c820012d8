# Enhanced Video Player Features

This document describes the advanced video playback features implemented in Zview, including GPU-accelerated decoding, optimized playback engine, smart caching, frame indexing, and visual preview scrubbing.

## Table of Contents

1. [GPU-Accelerated Decoding & Rendering](#gpu-accelerated-decoding--rendering)
2. [Optimized Playback Engine](#optimized-playback-engine)
3. [Smart Caching & Proxy Generation](#smart-caching--proxy-generation)
4. [Frame Indexing and Seek Table Structures](#frame-indexing-and-seek-table-structures)
5. [Visual Preview Scrubbing](#visual-preview-scrubbing)
6. [J-K-L Scrubbing Controls](#j-k-l-scrubbing-controls)
7. [Keyboard Shortcuts](#keyboard-shortcuts)
8. [Configuration Options](#configuration-options)
9. [Performance Monitoring](#performance-monitoring)
10. [Technical Implementation](#technical-implementation)

## GPU-Accelerated Decoding & Rendering

### Features
- **Hardware Acceleration Auto-Detection**: Automatically detects and enables the best available hardware acceleration
- **Multi-Vendor Support**: Supports NVIDIA NVENC/NVDEC, AMD AMF, Intel QuickSync, DXVA2, and D3D11VA
- **GPU-Accelerated Rendering**: Custom OpenGL shaders for optimized video rendering
- **Fallback Support**: Gracefully falls back to software decoding when hardware acceleration is unavailable

### Supported Hardware Types
- `NVIDIA`: NVIDIA NVENC/NVDEC (GeForce, Quadro)
- `AMD`: AMD AMF (Radeon graphics)
- `Intel`: Intel QuickSync (integrated graphics)
- `DXVA2`: DirectX Video Acceleration 2.0 (Windows)
- `D3D11`: Direct3D 11 Video Acceleration (Windows)

### Configuration
```cpp
// Enable automatic hardware acceleration
enableGPUAcceleration(HardwareAcceleration::Auto);

// Or specify a particular type
enableGPUAcceleration(HardwareAcceleration::NVIDIA);
```

## Optimized Playback Engine

### Core Features
- **Adaptive Buffering**: Dynamically adjusts buffer size based on content and system performance
- **Multi-threaded Decoding**: Utilizes multiple CPU cores for optimal performance
- **Frame Dropping**: Intelligently drops frames to maintain smooth playback
- **Smart Seeking**: Uses frame indexing for faster, more accurate seeking

### Buffer Strategies
- `Adaptive`: Automatically adjusts buffer size based on content and system performance
- `Conservative`: Small buffer for low latency applications
- `Aggressive`: Large buffer for smooth playback on slower systems
- `Streaming`: Optimized for network streaming content

### Performance Metrics
The engine continuously monitors:
- Frame rate and dropped frame rate
- Buffer health (0-100%)
- Decoding and rendering latency
- Memory usage
- Hardware acceleration status

## Smart Caching & Proxy Generation

### Caching System
- **Intelligent Thumbnail Generation**: Automatically generates thumbnails for quick preview
- **Proxy File Creation**: Creates lower-resolution proxy files for smooth scrubbing
- **LRU Cache Management**: Efficiently manages memory and disk usage
- **Background Processing**: Non-blocking cache generation

### Proxy Quality Options
- `Low (360p)`: Fast generation, minimal storage
- `Medium (720p)`: Balanced quality and performance
- `High (1080p)`: Best quality for high-resolution content

### Cache Features
- **Persistent Storage**: Caches survive application restarts
- **Memory Management**: Automatic cleanup based on usage patterns
- **Configurable Limits**: Set maximum cache size and memory usage
- **Cache Hit Ratio Tracking**: Monitor cache effectiveness

## Frame Indexing and Seek Table Structures

### Frame Indexing
The frame indexer builds comprehensive seek tables for optimal video navigation:

```cpp
struct FrameIndexEntry {
    double timestamp;           // Frame timestamp in seconds
    int64_t filePosition;      // Byte offset in file
    bool isKeyFrame;           // Is this a keyframe/I-frame?
    int frameSize;             // Size of frame data in bytes
    int width, height;         // Frame dimensions
    int streamIndex;           // Stream index
};
```

### Seek Table Features
- **Keyframe Detection**: Identifies all keyframes for fast seeking
- **Precise Positioning**: Find exact frames by timestamp
- **Range Queries**: Get all keyframes in a time range
- **Optimized Scrubbing**: Special handling for preview generation

### Background Indexing
- Builds frame index in background thread
- Progress monitoring and cancellation support
- Persistent caching of index data
- Automatic re-indexing when needed

## Visual Preview Scrubbing

### Preview Generation
- **Real-time Thumbnails**: Generate video thumbnails on-demand
- **Smooth Scrubbing**: Fluid preview updates during seeking
- **Keyframe Optimization**: Prefer keyframes for better performance
- **Cached Previews**: Intelligent caching of generated previews

### Scrub Modes
- `Hover`: Show preview on mouse hover
- `Drag`: Show preview while dragging seekbar
- `JKL`: Preview updates during J-K-L scrubbing

### Visual Elements
- **Timeline Thumbnails**: Micro-thumbnails along the timeline
- **Keyframe Markers**: Visual indicators for keyframes
- **Timecode Display**: Precise timestamp information
- **Smooth Animations**: Fluid transitions and updates

## J-K-L Scrubbing Controls

### Control Scheme
Based on professional video editing standards:

- **J Key**: Step backward / Reverse playback
  - First press: 1x reverse
  - Additional presses: 2x, 4x, 8x reverse (capped at 8x)
  
- **K Key**: Stop / Pause playback
  - Always stops playback and returns to normal state
  
- **L Key**: Step forward / Forward playback
  - First press: 1x forward
  - Additional presses: 2x, 4x, 8x forward (capped at 8x)

### Speed Control
- **Variable Speed**: Supports 0.25x to 8x playback speeds
- **Smooth Transitions**: Gradual speed changes for better user experience
- **Speed Indicators**: Visual feedback for current playback speed

### Frame-by-Frame Mode
- **Precise Control**: Single frame stepping with arrow keys
- **Keyframe Jumping**: Jump to next/previous keyframes with comma/period keys

## Keyboard Shortcuts

### Playback Controls
- `Space`: Play/Pause toggle
- `J`: Reverse / Increase reverse speed
- `K`: Stop/Pause
- `L`: Forward / Increase forward speed (or toggle loop if J-K-L disabled)
- `←/→`: Frame step backward/forward or previous/next image
- `,/.`: Step to previous/next keyframe
- `0-9`: Seek to percentage (0=start, 1=10%, 2=20%, etc.)

### Speed and Volume
- `+/=`: Increase playback speed (with Shift) or extend duration
- `-`: Decrease playback speed (with Shift) or reduce duration
- `↑/↓`: Volume up/down

### Features
- `F`: Toggle fullscreen
- `R`: Reset zoom (for images)
- `T`: Toggle J-K-L scrubbing mode
- `I`: Start/check frame indexing
- `P`: Toggle visual preview scrubbing

## Configuration Options

### Cache Configuration
```cpp
struct CacheConfig {
    bool enableSmartCaching = true;
    bool enableProxyGeneration = true;
    int maxCacheSize = 500; // MB
    QString cacheDirectory = "./cache";
    int proxyQuality = 720; // 720p proxies
    int thumbnailSize = 200; // 200px thumbnails
    int preloadDistance = 5; // Files to preload ahead/behind
};
```

### Indexing Configuration
```cpp
struct IndexingConfig {
    bool enableFrameIndexing = true;
    bool generatePreviews = true;
    int previewWidth = 160;
    int previewHeight = 90;
    double keyFrameInterval = 10.0; // seconds
    bool enableBackgroundIndexing = true;
};
```

### Optimization Settings
```cpp
struct OptimizationSettings {
    HardwareAcceleration hardwareAccel = HardwareAcceleration::Auto;
    bool enableGPUDecoding = true;
    bool enableSmartBuffering = true;
    int bufferSize = 30; // frames
    bool enableFrameDropping = true;
    bool enableMultithreading = true;
    int maxDecodingThreads = 4;
};
```

## Performance Monitoring

### Real-time Metrics
The system provides comprehensive performance monitoring:

```cpp
struct PlaybackMetrics {
    double frameRate = 0.0;           // Current frame rate
    double droppedFrameRate = 0.0;    // Dropped frames per second
    int bufferSize = 0;               // Current buffer size
    double bufferHealth = 0.0;        // Buffer health (0.0 to 1.0)
    bool isHardwareAccelerated = false;
    QString hardwareType;             // Type of hardware acceleration
    double decodingLatency = 0.0;     // Decoding latency in milliseconds
    double renderingLatency = 0.0;    // Rendering latency in milliseconds
    size_t memoryUsage = 0;           // Memory usage in bytes
};
```

### Performance Optimization
- **Automatic Tuning**: System automatically adjusts settings based on performance
- **Buffer Health Monitoring**: Prevents buffer underruns
- **Memory Management**: Intelligent cleanup and garbage collection
- **Thread Management**: Optimal thread allocation based on system capabilities

## Technical Implementation

### Architecture Overview
```
┌─────────────────────────────────────────────────────────────┐
│                         Zview Application                    │
├─────────────────────────────────────────────────────────────┤
│  Enhanced Playback Engine                                   │
│  ├── GPU Decoder                                            │
│  ├── Frame Indexer                                          │
│  ├── Smart Cache                                            │
│  └── Scrub Controller                                       │
├─────────────────────────────────────────────────────────────┤
│  Existing Components                                         │
│  ├── MpvWrapper (Enhanced)                                  │
│  ├── OpenGL Rendering                                       │
│  └── UI Components                                          │
├─────────────────────────────────────────────────────────────┤
│  System Integration                                          │
│  ├── Hardware Acceleration APIs                             │
│  ├── Multi-threading                                        │
│  └── File System Caching                                    │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

#### FrameIndexer
- Builds comprehensive frame index with seeking optimization
- Generates preview thumbnails for scrubbing
- Persistent caching of index data
- Background processing with progress monitoring

#### Smart Cache
- LRU-based cache management
- Proxy file generation for smooth scrubbing
- Memory and disk usage optimization
- Thread-safe operations

#### Visual Preview Scrubber
- Real-time thumbnail generation
- Smooth preview animations
- Keyframe-aware scrubbing
- Customizable appearance

#### GPU Decoder (Framework)
- Hardware acceleration abstraction layer
- Multi-vendor GPU support
- Fallback to software decoding
- Performance monitoring integration

### Integration Points

#### MpvWrapper Enhancements
The existing MpvWrapper has been enhanced to support:
- Hardware acceleration configuration
- Advanced seeking with frame accuracy
- Buffer management and monitoring
- Performance metrics collection

#### OpenGL Integration
- Custom shaders for video rendering
- GPU texture management
- Hardware-accelerated color correction
- Efficient memory usage

#### Threading Model
- Background frame indexing
- Asynchronous cache operations
- Non-blocking preview generation
- Thread-safe data structures

## Usage Examples

### Basic Setup
```cpp
// Initialize advanced features
zview->initializeAdvancedPlayback();

// Configure for optimal performance
OptimizationSettings settings;
settings.hardwareAccel = HardwareAcceleration::Auto;
settings.enableGPUDecoding = true;
settings.bufferSize = 30;
zview->setOptimizationSettings(settings);

// Enable J-K-L scrubbing
zview->enableJKLScrubbing(true);
```

### Advanced Configuration
```cpp
// Configure smart caching
CacheConfig cacheConfig;
cacheConfig.maxCacheSize = 1000; // 1GB cache
cacheConfig.proxyQuality = 720;
zview->configureSmartCaching(cacheConfig);

// Enable frame indexing
IndexingConfig indexConfig;
indexConfig.generatePreviews = true;
indexConfig.previewWidth = 200;
indexConfig.previewHeight = 112;
zview->enableFrameIndexing(indexConfig);

// Start background indexing
zview->buildFrameIndex(true);
```

### Performance Monitoring
```cpp
// Get current performance metrics
auto metrics = zview->getPlaybackMetrics();
qDebug() << "Frame rate:" << metrics.frameRate;
qDebug() << "Buffer health:" << metrics.bufferHealth;
qDebug() << "Hardware acceleration:" << metrics.isHardwareAccelerated;
qDebug() << "Hardware type:" << metrics.hardwareType;
```

## Future Enhancements

### Planned Features
1. **Network Streaming Optimization**: Enhanced buffering for streaming content
2. **AI-Powered Frame Analysis**: Intelligent scene detection and indexing
3. **Advanced Color Grading**: Real-time color correction and enhancement
4. **Multi-Stream Support**: Handle multiple video/audio streams
5. **VR/360° Video Support**: Specialized handling for immersive content
6. **Cloud Integration**: Remote caching and processing

### Performance Improvements
1. **Metal/Vulkan Support**: Next-generation graphics API integration
2. **Machine Learning**: AI-assisted performance optimization
3. **Advanced Hardware Features**: Latest GPU feature utilization
4. **Cross-Platform Optimization**: Platform-specific optimizations

This enhanced video player provides professional-level features while maintaining ease of use and high performance across different hardware configurations.
