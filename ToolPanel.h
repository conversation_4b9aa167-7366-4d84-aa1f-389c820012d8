#ifndef TOOLPANEL_H
#define TOOLPANEL_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QTimer>
#include <QProgressBar>

class ToolPanel : public QWidget
{
    Q_OBJECT

public:
    explicit ToolPanel(QWidget *parent = nullptr);
    ~ToolPanel();

    // Video controls
    void setPlaying(bool playing);
    void setPosition(double position);
    void setDuration(double duration);
    void setVolume(int volume);
    
    // Image controls
    void setZoomFactor(float zoom);
    void setImageInfo(const QString &info);
    
    // Visibility
    void showControls();
    void hideControls();
    void setAutoHide(bool enabled);
    
    // A-B Loop markers
    void setABMarkers(double aPos, double bPos, double duration);
    void clearABMarkers();

signals:
    // Video control signals
    void playPauseClicked();
    void stopClicked();
    void positionChanged(double position);
    void volumeChanged(int volume);
    void previousClicked();
    void nextClicked();
    
    // A-B Loop signals
    void setAPointClicked();
    void setBPointClicked();
    void clearABLoopClicked();
    
    // Image control signals
    void zoomInClicked();
    void zoomOutClicked();
    void resetZoomClicked();
    void fitToWindowClicked();
    void actualSizeClicked();
    
    // Navigation signals
    void fullscreenToggled();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;

private slots:
    void onAutoHideTimer();
    void onSeekSliderPressed();
    void onSeekSliderReleased();
    void onSeekSliderMoved(int value);

private:
    void setupUI();
    void setupVideoControls();
    void setupImageControls();
    void setupSeekBar();
    void updateTimeDisplay();
    QString formatTime(double seconds) const;
    
    // UI Components
    QVBoxLayout *m_mainLayout = nullptr;
    QHBoxLayout *m_controlsLayout = nullptr;
    QHBoxLayout *m_seekLayout = nullptr;
    QHBoxLayout *m_imageControlsLayout = nullptr;
    
    // Video controls
    QPushButton *m_playPauseBtn = nullptr;
    QPushButton *m_stopBtn = nullptr;
    QPushButton *m_prevBtn = nullptr;
    QPushButton *m_nextBtn = nullptr;
    QSlider *m_seekSlider = nullptr;
    QSlider *m_volumeSlider = nullptr;
    QLabel *m_timeLabel = nullptr;
    QLabel *m_durationLabel = nullptr;
    
    // A-B Loop controls
    QPushButton *m_aPointBtn = nullptr;
    QPushButton *m_bPointBtn = nullptr;
    QPushButton *m_clearABBtn = nullptr;
    QWidget *m_aMarker = nullptr;
    QWidget *m_bMarker = nullptr;
    
    // Image controls
    QPushButton *m_zoomInBtn = nullptr;
    QPushButton *m_zoomOutBtn = nullptr;
    QPushButton *m_resetZoomBtn = nullptr;
    QPushButton *m_fitToWindowBtn = nullptr;
    QPushButton *m_actualSizeBtn = nullptr;
    QLabel *m_zoomLabel = nullptr;
    QLabel *m_imageInfoLabel = nullptr;
    
    // Fullscreen
    QPushButton *m_fullscreenBtn = nullptr;
    
    // State
    bool m_isPlaying = false;
    bool m_isSeekingManually = false;
    double m_currentPosition = 0.0;
    double m_currentDuration = 0.0;
    int m_currentVolume = 50;
    float m_currentZoom = 1.0f;
    
    // Auto-hide functionality
    bool m_autoHideEnabled = true;
    QTimer *m_autoHideTimer = nullptr;
    static const int AUTO_HIDE_DELAY = 3000; // 3 seconds
    
    // A-B Loop markers
    double m_aPosition = -1.0;
    double m_bPosition = -1.0;
    bool m_hasABLoop = false;
};

#endif // TOOLPANEL_H
