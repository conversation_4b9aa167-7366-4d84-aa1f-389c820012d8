#ifndef ZVIEW_H
#define ZVIEW_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLTexture>
#include <QMatrix4x4>
#include <QPaintEvent>
#include <QPainter>
#include <QPainterPath>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QPixmap>
#include <QWheelEvent>
#include <QStringList>
#include <QTimer>
#include <QSlider>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QFileDialog>
#include <QFileInfo>
#include <QColor>
#include <QTransform>
#include <QInputDialog>
#include <QLineEdit>
#include <functional>


// Forward declarations
class MpvWrapper;
class SimpleSmartCache;

QT_BEGIN_NAMESPACE
namespace Ui {
class Zview;
}
QT_END_NAMESPACE

class Zview : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    Zview(QWidget *parent = nullptr);
    ~Zview();
    
    // Public method for opening files (used by main.cpp for command line arguments)
    void openFile(const QString &filePath);

protected:
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;
    void resizeEvent(QResizeEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override; // Advanced scrubbing event handling

private:
    void setRoundedMask();
    void loadImage(const QString &filePath);
    void loadVideo(const QString &filePath);
    bool isVideoFile(const QString &filePath);
    bool isImageFile(const QString &filePath);
    void buildImageList(const QString &currentImagePath);
    void loadNextImage();
    void loadPreviousImage();
    void loadImageAtIndex(int index);
    void resetZoom();
    void setupShaders();
    void setupVertexData();
    void loadImageToTexture();
    void repositionButtons();
    void repositionCropRegion();  // Handle crop region positioning during window changes
    void toggleVideoLoop(); // Toggle video loop on/off      // Crop tool functionality
    void showCropTool();
    void hideCropTool();
    void applyCrop();
    void cancelCrop();
    void updateCropOverlay();
    void createCropHandles();
    void showCropPreview();
    
    // Cursor handling for fast mouse movements
    void updateCursorForPosition();
    
    // Performance optimization methods
    void detectOpenGLCapabilities();
    void setupUniformBuffer();
    void setupFramebuffer();
    void optimizeTextureFormat(QImage &image);
    void generateMipmapLevels(const QImage &image);
    void selectOptimalMipLevel();
    void updateMatrixUniforms(const QMatrix4x4 &model);
    void preloadNextTexture(); // Background texture loading
    void cleanupMipmaps();
    
    // A-B loop functionality
    void setABLoopPoint(bool isAPoint);
    void clearABLoop();
    void checkABLoopPosition(double currentTime);
    void updateABButtonStates();
    void updateSeekbarABMarkers();
    
    // Image Editor functionality
    void showImageEditor();
    void hideImageEditor();
    void setupImageEditorToolbox();
    void repositionImageEditorToolbox();
    void setupLeftImageEditorToolbox();
    void repositionLeftImageEditorToolbox();
    void switchLeftToolboxTab(int tabNumber);
    
    // Image editing operations
    void adjustBrightness(int value);
    void adjustContrast(int value);
    void adjustSaturation(int value);
    void adjustHue(int value);
    void rotateImage(int degrees);
    void flipImageHorizontal();
    void flipImageVertical();
    void applyBlur(int radius);
    void applySharpen(int strength);
    void applyEmboss();
    void applyGrayscale();
    void applySepia();
    void resetImageEdits();
    void applyImageEdits();
    void cancelImageEdits();
    
    // Advanced image editing operations (left toolbox)
    void adjustGamma(int value);
    void adjustExposure(int value);
    void adjustHighlights(int value);
    void adjustShadows(int value);
    void adjustVibrance(int value);
    void adjustTemperature(int value);
    void adjustTint(int value);
    void applyNoiseReduction(int value);
    void applyVintage();
    void applyBlackWhite();
    void applyCrossProcess();
    void applyLomography();
    void applyPolaroid();
    void applyInvert();
    void applyPosterize();
    void applySobelEdge();
    void showCurveAdjustor();
    void applyCurveAdjustment(const QVector<QPoint> &curvePoints);
    void applyCurveAdjustmentOptimized(class ProfessionalCurveWidget *curveWidget);
    QImage applyCurveToImageOptimized(const QImage &image, const QVector<QVector<int>> &lookupTables, 
                                    int mode, int type);
    static void rgbToHsvFast(int r, int g, int b, int &h, int &s, int &v);
    static void hsvToRgbFast(int h, int s, int v, int &r, int &g, int &b);
    QVector<int> createLookupTable(const QVector<QPoint> &curvePoints);
    QVector<int> createOptimizedLookupTable(const QVector<QPoint> &curvePoints);
    int calculateCubicSplineValue(int x, const QVector<QPoint> &points);
    int calculateLinearInterpolation(int x, const QVector<QPoint> &points);
    
    // Professional HSL Color Editor functions
    void setHSLChannel(int channel);
    void adjustHSLHue(int value);
    void adjustHSLSaturation(int value);
    void adjustHSLLuminance(int value);
    
    // Color Balance Wheels functions
    void showShadowColorWheel();
    void showMidtoneColorWheel();
    void showHighlightColorWheel();
    
    // Split Toning functions
    void adjustHighlightTint(int value);
    void adjustShadowTint(int value);
    void adjustSplitToneBalance(int value);
    
    // Professional Enhancement functions
    void adjustClarity(int value);
    void adjustDehaze(int value);
    void autoWhiteBalance();
    
    // Film Emulation functions
    void applyKodakEmulation();
    void applyFujiEmulation();
    void applyIlfordEmulation();
    
    // Cinema Grade functions
    void applyCinemaLog();
    void applyCinemaRec709();
    void applyBleachBypass();
    
    // Professional Masking functions
    void createLuminosityMask();
    void createColorMask();
    void createRangeMask();
    
    // Image editing helper methods
    QImage applyImageFilter(const QImage &image, const QString &filterType, const QVariant &parameter = QVariant());
    void updateImagePreview();
    void scheduleUpdateDebounced(); // Ultra-fast debounced updates
    void saveEditedImage();
    
    // Control visibility methods
    void showControls();
    void hideControls();
    bool isMouseInControlArea(const QPoint &pos);
    void ensureControlHideTimer(); // Ensure timer is created when needed
      // Toolbox hover functionality
    void showToolbox();
    void hideToolbox(bool force = false);
    bool isMouseInToolboxArea(const QPoint &pos);
    void ensureToolboxHideTimer();
    
    // Window resize helpers
    int getResizeEdge(const QPoint &pos);
    void updateCursorForResize(const QPoint &pos);
    
    int m_cornerRadius = 15;
    QPixmap m_currentImage;
    QString m_currentImagePath;
    QStringList m_imageList;
    int m_currentImageIndex = -1;
    
    // Zoom and panning state
    float m_zoomFactor = 1.0f;
    QPointF m_imageOffset = QPointF(0, 0);
    QPoint m_lastMousePos;
    QPoint m_zoomAnchor;
    QPoint m_rightClickStartPos;
    bool m_isRightDragging = false;
    bool m_isSeeking = false;
    double m_lastClickPosition = -1.0; // Store click position for accurate seeking
    
    // Left mouse drag state
    bool m_isLeftDragging = false;
    bool m_actualDragOccurred = false; // Track if actual dragging movement happened
    QPoint m_leftDragStartPos; // Global coordinates for window dragging
    QPoint m_panDragStartPos;  // Local coordinates for image panning
    QPoint m_windowDragStartPos;
    QPoint m_lastWindowPos;
    QPointF m_panStartOffset; // Initial image offset when panning starts
    QPointF m_panImagePoint;  // Image coordinates of the point under cursor when panning starts
    
    // Window resize state
    bool m_isResizing = false;
    int m_resizeEdge = 0; // Bitmask: 1=left, 2=right, 4=top, 8=bottom
    QPoint m_resizeStartPos;
    QRect m_resizeStartGeometry;
    static const int RESIZE_BORDER_WIDTH = 8;
    
    // OpenGL rendering
    QOpenGLShaderProgram *m_shaderProgram = nullptr;
    QOpenGLBuffer m_vertexBuffer;
    QOpenGLBuffer m_elementBuffer;
    QOpenGLVertexArrayObject m_vao;
    QOpenGLTexture *m_texture = nullptr;
    QMatrix4x4 m_projectionMatrix;
    QMatrix4x4 m_viewMatrix;
    bool m_textureNeedsUpdate = false;
    
    // Performance optimization members
    bool m_glInitialized = false;
    int m_maxTextureSize = 0;
    bool m_supportsNPOT = false; // Non-power-of-two textures
    bool m_supportsCompression = false;
    GLuint m_uniformBuffer = 0; // Uniform buffer object for matrices
    GLuint m_framebuffer = 0; // Framebuffer for post-processing
    GLuint m_colorTexture = 0; // Color attachment for framebuffer
    QSize m_lastTextureSize;
    QImage::Format m_optimalFormat = QImage::Format_RGBA8888;
    
    // Texture streaming for large images
    struct TextureLevel {
        QOpenGLTexture* texture = nullptr;
        QSize size;
        float scale = 1.0f;
    };    QVector<TextureLevel> m_mipmapLevels;
    int m_currentMipLevel = 0;
    
    // Video playback support
    QWidget *m_videoWidget = nullptr;
    MpvWrapper *m_mpvPlayer = nullptr;
    bool m_isPlayingVideo = false;
    bool m_isLoopEnabled = true; // Track if looping is enabled
    // Smart caching
    SimpleSmartCache *m_smartCache = nullptr;
    QString m_currentVideoFile;
    
    // Seekbar support
    bool m_seekbarDragging = false;
    bool m_expandedSeekbarDragging = false; // Track dragging in expanded seekbar area
    bool m_wasPlayingBeforeSeek = false; // Track play state before seeking
    QTimer *m_seekbarTimer = nullptr;
    QTimer *m_seekThrottleTimer = nullptr;
    double m_pendingSeekPosition = -1.0;
    
    // A-B loop functionality
    bool m_abLoopEnabled = false;
    double m_abLoopStartTime = -1.0;
    double m_abLoopEndTime = -1.0;
    bool m_hasAPoint = false;
    bool m_hasBPoint = false;
    QWidget *m_aMarker = nullptr;
    QWidget *m_bMarker = nullptr;
    
    // A-B marker dragging
    bool m_draggingAMarker = false;
    bool m_draggingBMarker = false;
    QPoint m_markerDragStartPos;
    bool m_markerSeekInProgress = false; // Prevent seekbar interference during marker dragging
      // Control visibility and hover behavior
    bool m_controlsVisible = false;
    QTimer *m_controlHideTimer = nullptr;
    
    // Toolbox visibility and hover behavior  
    bool m_toolboxVisible = false;
    QTimer *m_toolboxHideTimer = nullptr;
    bool m_ignoreToolboxHover = false; // Temporarily ignore hover after button clicks
    
    // Cursor update handling for fast mouse movements
    QTimer *m_cursorUpdateTimer = nullptr;
    Qt::CursorShape m_currentCursorShape = Qt::ArrowCursor;
    QPoint m_lastCursorUpdatePos;
    bool m_forceCropCursor = false; // Force crop cursor to override other cursor logic
    
    // Crop tool variables    
    bool m_cropModeActive = false;
    QRect m_cropRect;
    QWidget *m_cropOverlay = nullptr;
    QVector<QWidget*> m_cropHandles;
    bool m_draggingCrop = false;
    bool m_drawingCrop = false;
    bool m_movingCrop = false;  // Track crop region moving
    QPoint m_cropDragStart;
    QPoint m_cropDrawStart;
    QPoint m_cropMoveStart;     // Starting point for crop region movement
    QRect m_cropMoveStartRect;  // Original crop rect when movement started
    int m_draggingHandle = -1; // -1=none, 0-7=handle index
    
    // Crop region relative positioning for fullscreen/window transitions
    float m_cropRelativeX = 0.0f;
    float m_cropRelativeY = 0.0f;
    float m_cropRelativeWidth = 0.0f;
    float m_cropRelativeHeight = 0.0f;
    bool m_hasCropRelativePosition = false;
    
    // A-B marker interaction helpers
    bool isPointOnMarker(const QPoint &pos, QWidget *marker);
    void updateMarkerTimeFromPosition(QWidget *marker, const QPoint &pos, bool isAMarker);
    
    // Crop tool helpers
    int getCropHandleAtPosition(const QPoint &pos);
    void updateCropRectFromHandle(int handleIndex, const QPoint &newPos);
    void storeCropRelativePosition(); // Store current crop position as relative coordinates
    QRect getImageBounds(); // Get the actual image area bounds in window coordinates
    
    // A-B loop state
    double m_aPosition = -1.0;
    double m_bPosition = -1.0;
    bool m_hasABLoop = false;
    
    // Image Editor state and UI components
    bool m_imageEditorActive = false;
    QWidget *m_imageEditorToolbox = nullptr;
    QWidget *m_leftImageEditorToolbox = nullptr; // Left side advanced toolbox
    QWidget *m_leftToolboxTab1 = nullptr;  // Basic tools tab
    QWidget *m_leftToolboxTab2 = nullptr;  // Advanced tools tab
    QPushButton *m_leftTab1Btn = nullptr;  // Tab 1 button
    QPushButton *m_leftTab2Btn = nullptr;  // Tab 2 button
    int m_currentLeftTab = 1;              // Current active tab (1 or 2)
    QPixmap m_originalImage; // Backup of original image before edits
    QPixmap m_trueOriginalImage; // True original image that never gets overwritten
    QImage m_trueOriginalImageData; // Raw image data that never gets processed
    QPixmap m_previewImage;  // Current preview with applied edits
    bool m_bypassTextureOptimization = false; // Flag to bypass texture format conversion
    
    // Image editor controls (right toolbox)
    QSlider *m_brightnessSlider = nullptr;
    QSlider *m_contrastSlider = nullptr;
    QSlider *m_saturationSlider = nullptr;
    QSlider *m_hueSlider = nullptr;
    QSlider *m_blurSlider = nullptr;
    QSlider *m_sharpenSlider = nullptr;
    QTimer *m_blurTimer = nullptr;  // Timer for blur performance optimization
    QTimer *m_updateDebounceTimer = nullptr;  // Ultra-fast debounce timer for all updates
    
    // Left toolbox advanced controls
    QSlider *m_gammaSlider = nullptr;
    QSlider *m_exposureSlider = nullptr;
    QSlider *m_highlightsSlider = nullptr;
    QSlider *m_shadowsSlider = nullptr;
    QSlider *m_vibranceSlider = nullptr;
    QSlider *m_temperatureSlider = nullptr;
    QSlider *m_tintSlider = nullptr;
    QSlider *m_noiseReductionSlider = nullptr;
    QPushButton *m_vintageBtn = nullptr;
    QPushButton *m_blackWhiteBtn = nullptr;
    QPushButton *m_crossProcessBtn = nullptr;
    QPushButton *m_lomographyBtn = nullptr;
    QPushButton *m_polaroidBtn = nullptr;
    QPushButton *m_invertBtn = nullptr;
    QPushButton *m_posterizeBtn = nullptr;
    QPushButton *m_sobelEdgeBtn = nullptr;
    QPushButton *m_curveAdjustorBtn = nullptr;
    
    // Professional HSL Color Editor controls
    QPushButton *m_hslAllBtn = nullptr;
    QPushButton *m_hslRedBtn = nullptr;
    QPushButton *m_hslOrangeBtn = nullptr;
    QPushButton *m_hslYellowBtn = nullptr;
    QPushButton *m_hslGreenBtn = nullptr;
    QPushButton *m_hslAquaBtn = nullptr;
    QPushButton *m_hslBlueBtn = nullptr;
    QPushButton *m_hslPurpleBtn = nullptr;
    QPushButton *m_hslMagentaBtn = nullptr;
    QSlider *m_hslHueSlider = nullptr;
    QSlider *m_hslSaturationSlider = nullptr;
    QSlider *m_hslLuminanceSlider = nullptr;
    
    // Color Balance Wheels controls
    QPushButton *m_shadowColorBtn = nullptr;
    QPushButton *m_midtoneColorBtn = nullptr;
    QPushButton *m_highlightColorBtn = nullptr;
    
    // Split Toning controls
    QSlider *m_highlightTintSlider = nullptr;
    QSlider *m_shadowTintSlider = nullptr;
    QSlider *m_splitToneBalanceSlider = nullptr;
    
    // Selective Color controls
    QPushButton *m_selectiveRedBtn = nullptr;
    QPushButton *m_selectiveYellowBtn = nullptr;
    QPushButton *m_selectiveGreenBtn = nullptr;
    QPushButton *m_selectiveCyanBtn = nullptr;
    QPushButton *m_selectiveBlueBtn = nullptr;
    QPushButton *m_selectiveMagentaBtn = nullptr;
    QPushButton *m_selectiveWhiteBtn = nullptr;
    QPushButton *m_selectiveBlackBtn = nullptr;
    
    // Professional Enhancement controls
    QSlider *m_claritySlider = nullptr;
    QSlider *m_dehazeSlider = nullptr;
    QPushButton *m_autoWBBtn = nullptr;
    
    // Film Emulation controls
    QPushButton *m_filmKodakBtn = nullptr;
    QPushButton *m_filmFujiBtn = nullptr;
    QPushButton *m_filmIlfordBtn = nullptr;
    
    // Cinema Grade controls
    QPushButton *m_cinemaLogBtn = nullptr;
    QPushButton *m_cinemaRec709Btn = nullptr;
    QPushButton *m_cinemaBleachBtn = nullptr;
    
    // Professional Masking controls
    QPushButton *m_luminosityMaskBtn = nullptr;
    QPushButton *m_colorMaskBtn = nullptr;
    QPushButton *m_rangeMaskBtn = nullptr;
    
    // Curve adjustment UI
    QWidget *m_curveAdjustorDialog = nullptr;
    QPushButton *m_rotateLeftBtn = nullptr;
    QPushButton *m_rotateRightBtn = nullptr;
    QPushButton *m_flipHorizontalBtn = nullptr;
    QPushButton *m_flipVerticalBtn = nullptr;
    QPushButton *m_grayscaleBtn = nullptr;
    QPushButton *m_sepiaBtn = nullptr;
    QPushButton *m_embossBtn = nullptr;
    QPushButton *m_resetEditsBtn = nullptr;
    QPushButton *m_applyEditsBtn = nullptr;
    QPushButton *m_cancelEditsBtn = nullptr;
    QPushButton *m_saveImageBtn = nullptr;
    
    // Image editing values (right toolbox)
    int m_brightnessValue = 0;
    int m_contrastValue = 0;
    int m_saturationValue = 0;
    int m_hueValue = 0;
    int m_blurValue = 0;
    int m_sharpenValue = 0;
    int m_rotationAngle = 0;
    bool m_isFlippedHorizontal = false;
    bool m_isFlippedVertical = false;
    bool m_grayscaleApplied = false;
    bool m_sepiaApplied = false;
    bool m_embossApplied = false;
    
    // Advanced editing values (left toolbox)
    int m_gammaValue = 0;
    int m_exposureValue = 0;
    int m_highlightsValue = 0;
    int m_shadowsValue = 0;
    int m_vibranceValue = 0;
    int m_temperatureValue = 0;
    int m_tintValue = 0;
    int m_noiseReductionValue = 0;
    bool m_vintageApplied = false;
    bool m_blackWhiteApplied = false;
    bool m_crossProcessApplied = false;
    bool m_lomographyApplied = false;
    bool m_polaroidApplied = false;
    bool m_invertApplied = false;
    bool m_posterizeApplied = false;
    bool m_sobelEdgeApplied = false;
    
    // Professional HSL Color Editor values
    int m_hslHueValue = 0;
    int m_hslSaturationValue = 0;
    int m_hslLuminanceValue = 0;
    int m_currentHSLChannel = 0; // 0=All, 1=Red, 2=Orange, etc.
    
    // Color Balance values
    QColor m_shadowColorBalance = QColor(128, 128, 128);
    QColor m_midtoneColorBalance = QColor(128, 128, 128);
    QColor m_highlightColorBalance = QColor(128, 128, 128);
    
    // Split Toning values
    int m_highlightTintValue = 0;
    int m_shadowTintValue = 0;
    int m_splitToneBalanceValue = 0;
    
    // Professional Enhancement values
    int m_clarityValue = 0;
    int m_dehazeValue = 0;
    
    // Film Emulation states
    bool m_kodakEmulationApplied = false;
    bool m_fujiEmulationApplied = false;
    bool m_ilfordEmulationApplied = false;
    
    // Cinema Grade states
    bool m_cinemaLogApplied = false;
    bool m_cinemaRec709Applied = false;
    bool m_bleachBypassApplied = false;
    
    // Professional Masking states
    bool m_luminosityMaskActive = false;
    bool m_colorMaskActive = false;
    bool m_rangeMaskActive = false;
    
    // Professional Noise Reduction and Artifact Removal
    QImage applyAdvancedNoiseReduction(const QImage &image, int strength = 50);
    QImage applyMedianFilter(const QImage &image, int kernelSize = 3);
    QImage applyGaussianNoiseReduction(const QImage &image, double sigma = 1.0, int kernelSize = 5);
    QImage removeColorArtifacts(const QImage &image, int threshold = 30);
    QImage removeCompressionArtifacts(const QImage &image);
    QImage applyBilateralFilter(const QImage &image, int d = 9, double sigmaColor = 75, double sigmaSpace = 75);
    QImage enhanceImageQuality(const QImage &image);
    QImage correctColorCasting(const QImage &image);
    QImage applyAdaptiveSharpening(const QImage &image, double amount = 0.3);
    
    // Performance Optimized Processing
    QImage processImageTiled(const QImage &image, std::function<QImage(const QImage&)> processor, int tileSize = 512);
    QImage processImageParallel(const QImage &image, std::function<QImage(const QImage&)> processor, int numThreads = 0);
    
    // Professional Performance Optimization (based on Photoshop/DaVinci/Affinity techniques)
    struct ImageCache {
        QImage originalImage;
        QImage previewImage;
        QHash<QString, QImage> processedCache;
        QElapsedTimer lastAccess;
        bool isValid = false;
    };
    
    struct PerformanceMetrics {
        float frameRate = 60.0f;
        float cpuUsage = 0.0f;
        float memoryUsage = 0.0f;
        int activeFilters = 0;
        float renderTime = 0.0f;
    };
    
    // Performance cache and metrics
    ImageCache m_imageCache;
    PerformanceMetrics m_performanceMetrics;
    QHash<QString, QImage> m_performanceCache;
    QElapsedTimer m_renderTimer;
    QTimer* m_metricsTimer = nullptr;
    
    // Multi-resolution pyramid for zoom optimization (like Photoshop)
    struct PyramidLevel {
        QImage image;
        float scaleFactor;
        bool isGenerated = false;
    };
    QVector<PyramidLevel> m_imagePyramid;
    
    // Performance optimization methods
    void initializePerformanceEngine();
    void optimizeForRealTimeEditing();
    QImage getOptimizedPreview(float zoomLevel = 1.0f);
    void cacheCurrentState(const QString& key);
    bool getCachedResult(const QString& key, QImage& result);
    void clearPerformanceCache();
    void updatePerformanceMetrics();
    QString getPerformanceReport() const;
    
    // Non-destructive editing support
    void saveEditingState();
    bool restoreEditingState();
    void createImagePyramid(const QImage& image);
    QImage getImageAtZoomLevel(float zoomLevel);
    
    // GPU acceleration helpers
    bool isGPUAccelerationAvailable() const;
    void enableGPUAcceleration(bool enable);
    void preloadCommonFilters();
    
    // Smart preview system (like DaVinci Resolve's optimized media)
    void generateOptimizedPreview();
    void updatePreviewInBackground();
    QImage getAdaptiveQualityPreview(float zoomLevel);
    
    // Memory management
    void optimizeMemoryUsage();
    void clearUnusedCache();
    size_t getCurrentMemoryUsage() const;
    
    // Professional anti-pixelation and high-quality scaling functions
    QImage applyProfessionalScaling(const QImage &source, const QSize &targetSize, bool highQuality = true);
    QImage applyBicubicInterpolation(const QImage &source, const QSize &targetSize);
    QImage applyLanczosResampling(const QImage &source, const QSize &targetSize);
    QImage applyAntiAliasing(const QImage &source);
    void enableHighQualityRendering(bool enable = true);
    
    // Advanced professional algorithms like Adobe/Affinity
    QImage applyLanczos3Resampling(const QImage &source, const QSize &targetSize);
    QImage applyMitchellNetravaliFilter(const QImage &source, const QSize &targetSize);
    QImage applyBSplineInterpolation(const QImage &source, const QSize &targetSize);
    QImage applySuperSamplingAntiAliasing(const QImage &source, float scaleFactor = 2.0f);
    QImage applyEdgePreservingSmoothing(const QImage &source);
    double lanczosKernel(double x, int a = 3);
    double mitchellNetravaliKernel(double x, double B = 1.0/3.0, double C = 1.0/3.0);
    double bSplineKernel(double x);
    
    // Professional quality settings with performance optimization
    enum class ScalingQuality {
        DRAFT,           // Fast preview (Qt::FastTransformation)
        GOOD,            // Standard quality (Qt::SmoothTransformation)
        BETTER,          // High quality (Enhanced smooth)
        BEST,            // Maximum quality (Professional algorithms)
        PROFESSIONAL,    // Ultra-high quality (Advanced algorithms)
        AUTO             // Adaptive quality based on operation type
    };
    
    void setGlobalScalingQuality(ScalingQuality quality);
    ScalingQuality m_scalingQuality = ScalingQuality::AUTO;
    
    // Performance optimization flags
    bool m_isInteractiveMode = false;
    bool m_usePerformanceMode = true;
    QTimer* m_qualityUpgradeTimer = nullptr;
    
    // Smart performance functions
    QImage getOptimizedScaling(const QImage &source, const QSize &targetSize, bool isInteractive = false);
    void enableInteractiveMode(bool enable);
    void upgradeQualityAfterDelay();
    
    Ui::Zview *ui;
};

#endif // ZVIEW_H
