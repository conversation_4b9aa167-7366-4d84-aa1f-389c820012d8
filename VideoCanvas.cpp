#include "VideoCanvas.h"
#include "MpvWrapper.h"
#include <QFileInfo>
#include <QDebug>
#include <QKeyEvent>

VideoCanvas::VideoCanvas(QWidget *parent)
    : QOpenGLWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
    
    // Initialize MPV
    initializeMpv();
    
    // Setup A-B loop timer
    m_abLoopTimer = new QTimer(this);
    connect(m_abLoopTimer, &QTimer::timeout, this, &VideoCanvas::checkABLoop);
    m_abLoopTimer->setInterval(100); // Check every 100ms
    
    // Frame update timer
    m_frameTimer = new QTimer(this);
    connect(m_frameTimer, &QTimer::timeout, this, QOverload<>::of(&VideoCanvas::update));
    m_frameTimer->setInterval(16); // ~60 FPS
}

VideoCanvas::~VideoCanvas()
{
    cleanupMpv();
}

void VideoCanvas::initializeGL()
{
    initializeOpenGLFunctions();
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    
    // Initialize MPV render context if not already done
    if (m_mpvPlayer && !m_mpvPlayer->isInitialized()) {
        if (m_mpvPlayer->initializeRenderContext(context())) {
            connect(m_mpvPlayer, &MpvWrapper::frameReady, this, &VideoCanvas::onFrameReady);
            connect(m_mpvPlayer, &MpvWrapper::positionChanged, this, &VideoCanvas::onPositionChanged);
            connect(m_mpvPlayer, &MpvWrapper::durationChanged, this, &VideoCanvas::onDurationChanged);
        }
    }
}

void VideoCanvas::paintGL()
{
    glClear(GL_COLOR_BUFFER_BIT);
    
    if (m_mpvPlayer && m_mpvPlayer->isInitialized() && m_hasVideo) {
        // Render MPV frame
        m_mpvPlayer->renderFrame(width(), height(), defaultFramebufferObject());
        m_mpvPlayer->acknowledgeFrame();
    }
}

void VideoCanvas::resizeGL(int width, int height)
{
    glViewport(0, 0, width, height);
}

void VideoCanvas::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        togglePlayPause();
    }
    QOpenGLWidget::mousePressEvent(event);
}

void VideoCanvas::mouseDoubleClickEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // Toggle fullscreen - emit signal for parent to handle
        emit playStateChanged(!isPaused()); // Reuse this signal temporarily
    }
    QOpenGLWidget::mouseDoubleClickEvent(event);
}

void VideoCanvas::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Space:
        togglePlayPause();
        break;
    case Qt::Key_Left:
        seek(getPosition() - 5.0); // Seek back 5 seconds
        break;
    case Qt::Key_Right:
        seek(getPosition() + 5.0); // Seek forward 5 seconds
        break;
    case Qt::Key_Up:
        setVolume(qMin(100, getVolume() + 5));
        break;
    case Qt::Key_Down:
        setVolume(qMax(0, getVolume() - 5));
        break;
    case Qt::Key_A:
        setAPoint();
        break;
    case Qt::Key_B:
        setBPoint();
        break;
    case Qt::Key_L:
        toggleABLoop();
        break;
    default:
        QOpenGLWidget::keyPressEvent(event);
    }
}

void VideoCanvas::loadVideo(const QString &filePath)
{
    if (!QFileInfo::exists(filePath)) {
        emit videoLoadFailed(filePath);
        return;
    }
    
    if (!isVideoFile(filePath)) {
        emit videoLoadFailed(filePath);
        return;
    }
    
    if (!m_mpvPlayer) {
        emit videoLoadFailed(filePath);
        return;
    }
    
    m_currentVideoPath = filePath;
    m_mpvPlayer->loadFile(filePath);
    m_hasVideo = true;
    
    // Start timers
    m_frameTimer->start();
    m_abLoopTimer->start();
    
    emit videoLoaded(filePath);
    update();
}

void VideoCanvas::play()
{
    if (m_mpvPlayer) {
        m_mpvPlayer->play();
        emit playStateChanged(true);
    }
}

void VideoCanvas::pause()
{
    if (m_mpvPlayer) {
        m_mpvPlayer->pause();
        emit playStateChanged(false);
    }
}

void VideoCanvas::stop()
{
    if (m_mpvPlayer) {
        m_mpvPlayer->stop();
        m_hasVideo = false;
        m_frameTimer->stop();
        m_abLoopTimer->stop();
        emit playStateChanged(false);
    }
}

void VideoCanvas::togglePlayPause()
{
    if (isPaused()) {
        play();
    } else {
        pause();
    }
}

void VideoCanvas::setVolume(int volume)
{
    m_volume = qBound(0, volume, 100);
    if (m_mpvPlayer) {
        m_mpvPlayer->setVolume(m_volume);
    }
    emit volumeChanged(m_volume);
}

void VideoCanvas::setLoop(bool enabled)
{
    m_isLooping = enabled;
    if (m_mpvPlayer) {
        m_mpvPlayer->setLoop(enabled);
    }
}

void VideoCanvas::seek(double position)
{
    if (m_mpvPlayer) {
        m_mpvPlayer->seek(position);
    }
}

double VideoCanvas::getPosition() const
{
    return m_mpvPlayer ? m_mpvPlayer->getPosition() : 0.0;
}

double VideoCanvas::getDuration() const
{
    return m_mpvPlayer ? m_mpvPlayer->getDuration() : 0.0;
}

bool VideoCanvas::isPaused() const
{
    return m_mpvPlayer ? m_mpvPlayer->isPaused() : true;
}

void VideoCanvas::setAPoint()
{
    m_abLoopStart = getPosition();
    m_hasAPoint = true;
    qDebug() << "A-Point set at:" << m_abLoopStart;
}

void VideoCanvas::setBPoint()
{
    double currentPos = getPosition();
    if (m_hasAPoint && currentPos > m_abLoopStart) {
        m_abLoopEnd = currentPos;
        m_hasBPoint = true;
        qDebug() << "B-Point set at:" << m_abLoopEnd;
    }
}

void VideoCanvas::clearABLoop()
{
    m_hasAPoint = false;
    m_hasBPoint = false;
    m_abLoopStart = 0.0;
    m_abLoopEnd = 0.0;
    qDebug() << "A-B Loop cleared";
}

void VideoCanvas::toggleABLoop()
{
    if (hasABLoop()) {
        clearABLoop();
    } else {
        // Set A point at current position if none exists
        if (!m_hasAPoint) {
            setAPoint();
        }
    }
}

void VideoCanvas::onFrameReady()
{
    update();
}

void VideoCanvas::onPositionChanged(double position)
{
    emit positionChanged(position);
}

void VideoCanvas::onDurationChanged(double duration)
{
    emit durationChanged(duration);
}

void VideoCanvas::checkABLoop()
{
    if (hasABLoop() && !isPaused()) {
        double currentPos = getPosition();
        if (currentPos >= m_abLoopEnd) {
            seek(m_abLoopStart);
        }
    }
}

void VideoCanvas::initializeMpv()
{
    m_mpvPlayer = new MpvWrapper(this);
}

void VideoCanvas::cleanupMpv()
{
    if (m_frameTimer) {
        m_frameTimer->stop();
    }
    if (m_abLoopTimer) {
        m_abLoopTimer->stop();
    }
    
    delete m_mpvPlayer;
    m_mpvPlayer = nullptr;
}

bool VideoCanvas::isVideoFile(const QString &filePath)
{
    static QStringList videoExtensions = {
        "mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v",
        "3gp", "3g2", "f4v", "f4p", "f4a", "f4b", "ogv", "ogg",
        "MP4", "MKV", "AVI", "MOV", "WMV", "FLV", "WEBM", "M4V"
    };
    
    QFileInfo fileInfo(filePath);
    return videoExtensions.contains(fileInfo.suffix());
}

#include "VideoCanvas.moc"
