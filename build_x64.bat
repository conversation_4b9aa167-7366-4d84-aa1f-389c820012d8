@echo off
echo Building Zview with proper x64 configuration...

REM Set the correct Visual Studio environment for x64
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

REM Navigate to build directory
cd /d "c:\Users\<USER>\Desktop\Zview\build"

REM Clean and rebuild
cmake --build . --config Debug --clean-first

echo Build complete. Check for errors above.
pause
