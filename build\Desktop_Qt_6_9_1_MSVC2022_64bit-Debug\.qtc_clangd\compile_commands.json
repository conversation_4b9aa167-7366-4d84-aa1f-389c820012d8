[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\main.cpp"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\zview.cpp"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/zview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\MpvWrapper.cpp"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/MpvWrapper.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\HeicImageLoader.cpp"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/HeicImageLoader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\SimpleCache.cpp"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/SimpleCache.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\zview.h"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/zview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\MpvWrapper.h"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/MpvWrapper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\HeicImageLoader.h"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/HeicImageLoader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\Zview_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Zview\\lib", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\VulkanSDK\\1.4.313.0\\Include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\Zview\\SimpleCache.h"], "directory": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Zview/SimpleCache.h"}]