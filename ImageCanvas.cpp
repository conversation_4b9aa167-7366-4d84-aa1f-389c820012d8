#include "ImageCanvas.h"
#include "HeicImageLoader.h"
#include <QOpenGLFunctions>
#include <QImageReader>
#include <QFileInfo>
#include <QDir>
#include <QDebug>
#include <QApplication>
#include <cmath>

ImageCanvas::ImageCanvas(QWidget *parent)
    : QOpenGLWidget(parent)
    , m_vertexBuffer(QOpenGLBuffer::VertexBuffer)
    , m_elementBuffer(QOpenGLBuffer::IndexBuffer)
{
    setFocusPolicy(Qt::StrongFocus);
}

ImageCanvas::~ImageCanvas()
{
    makeCurrent();
    delete m_shaderProgram;
    delete m_texture;
    doneCurrent();
}

void ImageCanvas::initializeGL()
{
    initializeOpenGLFunctions();
    
    glClearColor(0.1f, 0.1f, 0.11f, 1.0f);
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    setupShaders();
    setupVertexData();
    updateProjectionMatrix();
}

void ImageCanvas::paintGL()
{
    glClear(GL_COLOR_BUFFER_BIT);
    
    if (m_currentImage.isNull()) {
        return;
    }
    
    // Update texture if needed
    if (m_textureNeedsUpdate || !m_texture) {
        loadImageToTexture();
    }
    
    if (!m_texture) {
        return;
    }
    
    // Calculate transform matrices
    calculateImageTransform();
    
    // Use our shader program
    m_shaderProgram->bind();
    
    // Set uniforms
    m_shaderProgram->setUniformValue("projection", m_projectionMatrix);
    m_shaderProgram->setUniformValue("view", m_viewMatrix);
    m_shaderProgram->setUniformValue("model", m_modelMatrix);
    
    // Bind texture
    m_texture->bind();
    m_shaderProgram->setUniformValue("ourTexture", 0);
    
    // Draw the quad
    m_vao.bind();
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
    m_vao.release();
    
    m_texture->release();
    m_shaderProgram->release();
}

void ImageCanvas::resizeGL(int width, int height)
{
    glViewport(0, 0, width, height);
    updateProjectionMatrix();
    
    // Center the image if we're at default zoom
    if (qFuzzyCompare(m_zoomFactor, 1.0f)) {
        centerImage();
    }
}

void ImageCanvas::wheelEvent(QWheelEvent *event)
{
    if (!hasImage()) {
        return;
    }
    
    const float zoomSpeed = 0.1f;
    const float maxZoom = 20.0f;
    const float minZoom = 0.05f;
    
    // Calculate zoom anchor point in image coordinates
    QPointF screenPos = event->position();
    QPointF imagePos = screenToImage(screenPos);
    
    // Calculate new zoom factor
    float oldZoom = m_zoomFactor;
    if (event->angleDelta().y() > 0) {
        m_zoomFactor *= (1.0f + zoomSpeed);
    } else {
        m_zoomFactor *= (1.0f - zoomSpeed);
    }
    
    // Clamp zoom factor
    m_zoomFactor = qBound(minZoom, m_zoomFactor, maxZoom);
    
    if (!qFuzzyCompare(oldZoom, m_zoomFactor)) {
        // Adjust offset to keep the point under the cursor stationary
        QPointF newScreenPos = imageToScreen(imagePos);
        QPointF offset = screenPos - newScreenPos;
        m_imageOffset += offset;
        
        emit zoomChanged(m_zoomFactor);
        update();
    }
    
    event->accept();
}

void ImageCanvas::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && hasImage()) {
        m_isPanning = true;
        m_lastMousePos = event->pos();
        m_panStartOffset = m_imageOffset;
        m_panImagePoint = screenToImage(event->pos());
        setCursor(Qt::ClosedHandCursor);
    }
}

void ImageCanvas::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isPanning && (event->buttons() & Qt::LeftButton)) {
        QPoint delta = event->pos() - m_lastMousePos;
        m_imageOffset = m_panStartOffset + QPointF(delta);
        update();
    }
}

void ImageCanvas::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_isPanning = false;
        setCursor(Qt::ArrowCursor);
    }
}

void ImageCanvas::loadImage(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        emit imageLoadFailed(filePath);
        return;
    }
    
    QPixmap pixmap;
    
    // Try loading HEIC files first
    if (HeicImageLoader::isHeicFile(filePath)) {
        pixmap = HeicImageLoader::loadHeicImage(filePath);
    } else {
        // Use Qt's standard image loading
        QImageReader reader(filePath);
        reader.setAutoTransform(true); // Handle EXIF orientation
        QImage image = reader.read();
        
        if (!image.isNull()) {
            pixmap = QPixmap::fromImage(image);
        }
    }
    
    if (pixmap.isNull()) {
        emit imageLoadFailed(filePath);
        return;
    }
    
    m_currentImage = pixmap;
    m_currentImagePath = filePath;
    m_imageSize = pixmap.size();
    m_textureNeedsUpdate = true;
    
    // Reset view state
    resetZoom();
    centerImage();
    
    // Build image list for navigation
    buildImageList(filePath);
    
    emit imageLoaded(filePath);
    update();
}

void ImageCanvas::loadImageFromPixmap(const QPixmap &pixmap)
{
    if (pixmap.isNull()) {
        return;
    }
    
    m_currentImage = pixmap;
    m_currentImagePath.clear();
    m_imageSize = pixmap.size();
    m_textureNeedsUpdate = true;
    
    resetZoom();
    centerImage();
    
    update();
}

void ImageCanvas::buildImageList(const QString &currentImagePath)
{
    m_imageList.clear();
    m_currentImageIndex = -1;
    
    QFileInfo fileInfo(currentImagePath);
    QDir dir = fileInfo.dir();
    
    // Supported image formats
    QStringList filters;
    filters << "*.jpg" << "*.jpeg" << "*.png" << "*.bmp" << "*.gif" 
            << "*.tiff" << "*.tif" << "*.webp" << "*.heic" << "*.heif"
            << "*.JPG" << "*.JPEG" << "*.PNG" << "*.BMP" << "*.GIF"
            << "*.TIFF" << "*.TIF" << "*.WEBP" << "*.HEIC" << "*.HEIF";
    
    QStringList imageFiles = dir.entryList(filters, QDir::Files, QDir::Name);
    
    for (const QString &file : imageFiles) {
        QString fullPath = dir.absoluteFilePath(file);
        m_imageList.append(fullPath);
        
        if (fullPath == currentImagePath) {
            m_currentImageIndex = m_imageList.size() - 1;
        }
    }
}

void ImageCanvas::loadNextImage()
{
    if (m_imageList.isEmpty() || m_currentImageIndex < 0) {
        return;
    }
    
    int nextIndex = (m_currentImageIndex + 1) % m_imageList.size();
    loadImage(m_imageList[nextIndex]);
    m_currentImageIndex = nextIndex;
}

void ImageCanvas::loadPreviousImage()
{
    if (m_imageList.isEmpty() || m_currentImageIndex < 0) {
        return;
    }
    
    int prevIndex = (m_currentImageIndex - 1 + m_imageList.size()) % m_imageList.size();
    loadImage(m_imageList[prevIndex]);
    m_currentImageIndex = prevIndex;
}

void ImageCanvas::zoomIn()
{
    const float zoomStep = 1.2f;
    const float maxZoom = 20.0f;
    
    m_zoomFactor = qMin(m_zoomFactor * zoomStep, maxZoom);
    emit zoomChanged(m_zoomFactor);
    update();
}

void ImageCanvas::zoomOut()
{
    const float zoomStep = 1.2f;
    const float minZoom = 0.05f;
    
    m_zoomFactor = qMax(m_zoomFactor / zoomStep, minZoom);
    emit zoomChanged(m_zoomFactor);
    update();
}

void ImageCanvas::resetZoom()
{
    m_zoomFactor = 1.0f;
    emit zoomChanged(m_zoomFactor);
}

void ImageCanvas::fitToWindow()
{
    if (!hasImage()) {
        return;
    }
    
    QSizeF widgetSize = size();
    QSizeF imageSize = m_imageSize;
    
    float scaleX = widgetSize.width() / imageSize.width();
    float scaleY = widgetSize.height() / imageSize.height();
    
    m_zoomFactor = qMin(scaleX, scaleY) * 0.95f; // 95% to add some padding
    centerImage();
    
    emit zoomChanged(m_zoomFactor);
    update();
}

void ImageCanvas::actualSize()
{
    m_zoomFactor = 1.0f;
    centerImage();
    emit zoomChanged(m_zoomFactor);
    update();
}

void ImageCanvas::centerImage()
{
    m_imageOffset = QPointF(0, 0);
}

void ImageCanvas::setupShaders()
{
    m_shaderProgram = new QOpenGLShaderProgram;
    
    // Vertex shader for image rendering
    const char *vertexSource = R"(
        #version 330 core
        layout (location = 0) in vec3 position;
        layout (location = 1) in vec2 texCoord;
        
        out vec2 TexCoord;
        
        uniform mat4 projection;
        uniform mat4 view;
        uniform mat4 model;
        
        void main()
        {
            gl_Position = projection * view * model * vec4(position, 1.0);
            TexCoord = texCoord;
        }
    )";
    
    // Fragment shader with better filtering
    const char *fragmentSource = R"(
        #version 330 core
        in vec2 TexCoord;
        out vec4 FragColor;
        
        uniform sampler2D ourTexture;
        
        void main()
        {
            FragColor = texture(ourTexture, TexCoord);
            // Ensure proper alpha handling
            if (FragColor.a < 0.01)
                discard;
        }
    )";
    
    m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexSource);
    m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentSource);
    
    if (!m_shaderProgram->link()) {
        qDebug() << "Failed to link shader program:" << m_shaderProgram->log();
    }
}

void ImageCanvas::setupVertexData()
{
    // Full-screen quad with texture coordinates
    float vertices[] = {
        // positions        // texture coords
        -1.0f, -1.0f, 0.0f,  0.0f, 1.0f,
         1.0f, -1.0f, 0.0f,  1.0f, 1.0f,
         1.0f,  1.0f, 0.0f,  1.0f, 0.0f,
        -1.0f,  1.0f, 0.0f,  0.0f, 0.0f
    };
    
    unsigned int indices[] = {
        0, 1, 2,
        2, 3, 0
    };
    
    m_vao.create();
    m_vao.bind();
    
    m_vertexBuffer.create();
    m_vertexBuffer.bind();
    m_vertexBuffer.allocate(vertices, sizeof(vertices));
    
    // Position attribute
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    
    // Texture coordinate attribute
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
    
    m_elementBuffer.create();
    m_elementBuffer.bind();
    m_elementBuffer.allocate(indices, sizeof(indices));
    
    m_vao.release();
}

void ImageCanvas::loadImageToTexture()
{
    if (m_currentImage.isNull()) {
        return;
    }
    
    delete m_texture;
    m_texture = nullptr;
    
    // Convert to OpenGL-friendly format
    QImage glImage = m_currentImage.toImage().convertToFormat(QImage::Format_RGBA8888);
    
    m_texture = new QOpenGLTexture(QOpenGLTexture::Target2D);
    m_texture->setFormat(QOpenGLTexture::RGBA8_UNorm);
    m_texture->setSize(glImage.width(), glImage.height());
    m_texture->allocateStorage();
    m_texture->setData(QOpenGLTexture::RGBA, QOpenGLTexture::UInt8, glImage.constBits());
    
    // Set texture parameters for high-quality rendering
    m_texture->setMinificationFilter(QOpenGLTexture::LinearMipMapLinear);
    m_texture->setMagnificationFilter(QOpenGLTexture::Linear);
    m_texture->setWrapMode(QOpenGLTexture::ClampToEdge);
    m_texture->generateMipMaps();
    
    m_textureNeedsUpdate = false;
}

void ImageCanvas::updateProjectionMatrix()
{
    m_projectionMatrix.setToIdentity();
    float aspect = static_cast<float>(width()) / static_cast<float>(height());
    m_projectionMatrix.ortho(-aspect, aspect, -1.0f, 1.0f, -1.0f, 1.0f);
}

void ImageCanvas::calculateImageTransform()
{
    if (!hasImage()) {
        return;
    }
    
    // Calculate scaled image dimensions
    m_scaledImageSize = m_imageSize * m_zoomFactor;
    
    // Calculate aspect ratios
    float widgetAspect = static_cast<float>(width()) / static_cast<float>(height());
    float imageAspect = m_imageSize.width() / m_imageSize.height();
    
    // Scale factor to fit image in normalized device coordinates
    float scaleX, scaleY;
    if (imageAspect > widgetAspect) {
        // Image is wider than widget
        scaleX = m_zoomFactor;
        scaleY = m_zoomFactor * widgetAspect / imageAspect;
    } else {
        // Image is taller than widget
        scaleX = m_zoomFactor * imageAspect / widgetAspect;
        scaleY = m_zoomFactor;
    }
    
    // Apply offset in normalized coordinates
    float offsetX = m_imageOffset.x() * 2.0f / width();
    float offsetY = -m_imageOffset.y() * 2.0f / height(); // Flip Y axis
    
    // Set up model matrix
    m_modelMatrix.setToIdentity();
    m_modelMatrix.translate(offsetX, offsetY, 0.0f);
    m_modelMatrix.scale(scaleX, scaleY, 1.0f);
    
    // View matrix is identity for 2D rendering
    m_viewMatrix.setToIdentity();
}

QPointF ImageCanvas::screenToImage(const QPointF &screenPos) const
{
    if (!hasImage()) {
        return QPointF();
    }
    
    // Convert screen coordinates to normalized device coordinates
    float ndcX = (screenPos.x() / width()) * 2.0f - 1.0f;
    float ndcY = 1.0f - (screenPos.y() / height()) * 2.0f;
    
    // Apply inverse transformations
    float widgetAspect = static_cast<float>(width()) / static_cast<float>(height());
    float imageAspect = m_imageSize.width() / m_imageSize.height();
    
    float scaleX, scaleY;
    if (imageAspect > widgetAspect) {
        scaleX = m_zoomFactor;
        scaleY = m_zoomFactor * widgetAspect / imageAspect;
    } else {
        scaleX = m_zoomFactor * imageAspect / widgetAspect;
        scaleY = m_zoomFactor;
    }
    
    float offsetX = m_imageOffset.x() * 2.0f / width();
    float offsetY = -m_imageOffset.y() * 2.0f / height();
    
    // Remove offset and scale
    float imageX = (ndcX - offsetX) / scaleX;
    float imageY = (ndcY - offsetY) / scaleY;
    
    // Convert to image pixel coordinates
    imageX = (imageX + 1.0f) * 0.5f * m_imageSize.width();
    imageY = (1.0f - imageY) * 0.5f * m_imageSize.height();
    
    return QPointF(imageX, imageY);
}

QPointF ImageCanvas::imageToScreen(const QPointF &imagePos) const
{
    if (!hasImage()) {
        return QPointF();
    }
    
    // Convert image coordinates to normalized coordinates
    float normX = (imagePos.x() / m_imageSize.width()) * 2.0f - 1.0f;
    float normY = 1.0f - (imagePos.y() / m_imageSize.height()) * 2.0f;
    
    // Apply transformations
    float widgetAspect = static_cast<float>(width()) / static_cast<float>(height());
    float imageAspect = m_imageSize.width() / m_imageSize.height();
    
    float scaleX, scaleY;
    if (imageAspect > widgetAspect) {
        scaleX = m_zoomFactor;
        scaleY = m_zoomFactor * widgetAspect / imageAspect;
    } else {
        scaleX = m_zoomFactor * imageAspect / widgetAspect;
        scaleY = m_zoomFactor;
    }
    
    float offsetX = m_imageOffset.x() * 2.0f / width();
    float offsetY = -m_imageOffset.y() * 2.0f / height();
    
    // Apply scale and offset
    float ndcX = normX * scaleX + offsetX;
    float ndcY = normY * scaleY + offsetY;
    
    // Convert to screen coordinates
    float screenX = (ndcX + 1.0f) * 0.5f * width();
    float screenY = (1.0f - ndcY) * 0.5f * height();
    
    return QPointF(screenX, screenY);
}

#include "ImageCanvas.moc"
