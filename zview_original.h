#ifndef ZVIEW_H
#define ZVIEW_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLTexture>
#include <QMatrix4x4>
#include <QPaintEvent>
#include <QPainter>
#include <QPainterPath>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QPixmap>
#include <QWheelEvent>
#include <QStringList>
#include <QTimer>

// Forward declarations
class MpvWrapper;

QT_BEGIN_NAMESPACE
namespace Ui {
class Zview;
}
QT_END_NAMESPACE

class Zview : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    Zview(QWidget *parent = nullptr);
    ~Zview();
    
    // Public method for opening files (used by main.cpp for command line arguments)
    void openFile(const QString &filePath);

protected:
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;
    void resizeEvent(QResizeEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private:
    void setRoundedMask();
    void loadImage(const QString &filePath);
    void loadVideo(const QString &filePath);
    bool isVideoFile(const QString &filePath);
    bool isImageFile(const QString &filePath);
    void buildImageList(const QString &currentImagePath);
    void loadNextImage();
    void loadPreviousImage();
    void loadImageAtIndex(int index);
    void resetZoom();
    void setupShaders();
    void setupVertexData();
    void loadImageToTexture();
    void repositionButtons();
    void toggleVideoLoop(); // Toggle video loop on/off
    
    // A-B loop functionality
    void setABLoopPoint(bool isAPoint);
    void clearABLoop();
    void checkABLoopPosition(double currentTime);
    void updateABButtonStates();
    void updateSeekbarABMarkers();
    
    // Control visibility methods
    void showControls();
    void hideControls();
    bool isMouseInControlArea(const QPoint &pos);
    void ensureControlHideTimer(); // Ensure timer is created when needed
    
    // Window resize helpers
    int getResizeEdge(const QPoint &pos);
    void updateCursorForResize(const QPoint &pos);
    
    int m_cornerRadius = 15;
    QPixmap m_currentImage;
    QString m_currentImagePath;
    QStringList m_imageList;
    int m_currentImageIndex = -1;
    
    // Zoom and panning state
    float m_zoomFactor = 1.0f;
    QPointF m_imageOffset = QPointF(0, 0);
    QPoint m_lastMousePos;
    QPoint m_zoomAnchor;
    QPoint m_rightClickStartPos;
    bool m_isRightDragging = false;
    
    // Left mouse drag state
    bool m_isLeftDragging = false;
    bool m_actualDragOccurred = false; // Track if actual dragging movement happened
    QPoint m_leftDragStartPos; // Global coordinates for window dragging
    QPoint m_panDragStartPos;  // Local coordinates for image panning
    QPoint m_windowDragStartPos;
    QPoint m_lastWindowPos;
    QPointF m_panStartOffset; // Initial image offset when panning starts
    QPointF m_panImagePoint;  // Image coordinates of the point under cursor when panning starts
    
    // Window resize state
    bool m_isResizing = false;
    int m_resizeEdge = 0; // Bitmask: 1=left, 2=right, 4=top, 8=bottom
    QPoint m_resizeStartPos;
    QRect m_resizeStartGeometry;
    static const int RESIZE_BORDER_WIDTH = 8;
    
    // OpenGL rendering
    QOpenGLShaderProgram *m_shaderProgram = nullptr;
    QOpenGLBuffer m_vertexBuffer;
    QOpenGLBuffer m_elementBuffer;
    QOpenGLVertexArrayObject m_vao;
    QOpenGLTexture *m_texture = nullptr;
    QMatrix4x4 m_projectionMatrix;
    QMatrix4x4 m_viewMatrix;
    bool m_textureNeedsUpdate = false;
    
    // Video playback support
    QWidget *m_videoWidget = nullptr;
    MpvWrapper *m_mpvPlayer = nullptr;
    bool m_isPlayingVideo = false;
    bool m_isLoopEnabled = true; // Track if looping is enabled
    
    // Seekbar support
    bool m_seekbarDragging = false;
    bool m_expandedSeekbarDragging = false; // Track dragging in expanded seekbar area
    bool m_wasPlayingBeforeSeek = false; // Track play state before seeking
    QTimer *m_seekbarTimer = nullptr;
    QTimer *m_seekThrottleTimer = nullptr;
    double m_pendingSeekPosition = -1.0;
    
    // A-B loop functionality
    bool m_abLoopEnabled = false;
    double m_abLoopStartTime = -1.0;
    double m_abLoopEndTime = -1.0;
    bool m_hasAPoint = false;
    bool m_hasBPoint = false;
    QWidget *m_aMarker = nullptr;
    QWidget *m_bMarker = nullptr;
    
    // A-B marker dragging
    bool m_draggingAMarker = false;
    bool m_draggingBMarker = false;
    QPoint m_markerDragStartPos;
    bool m_markerSeekInProgress = false; // Prevent seekbar interference during marker dragging
    
    // Control visibility and hover behavior
    bool m_controlsVisible = false;
    QTimer *m_controlHideTimer = nullptr;
    
    // A-B marker interaction helpers
    bool isPointOnMarker(const QPoint &pos, QWidget *marker);
    void updateMarkerTimeFromPosition(QWidget *marker, const QPoint &pos, bool isAMarker);
    
    Ui::Zview *ui;
};

#endif // ZVIEW_H
