{"artifacts": [{"path": "Zview.exe"}, {"path": "Zview.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "link_directories", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 19, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 50, "parent": 0}, {"command": 5, "file": 1, "line": 16, "parent": 0}, {"command": 5, "file": 1, "line": 17, "parent": 0}, {"command": 6, "file": 1, "line": 34, "parent": 0}, {"command": 6, "file": 1, "line": 45, "parent": 0}, {"command": 9, "file": 1, "line": 8, "parent": 0}, {"file": 4, "parent": 10}, {"command": 9, "file": 4, "line": 218, "parent": 11}, {"file": 3, "parent": 12}, {"command": 8, "file": 3, "line": 55, "parent": 13}, {"file": 2, "parent": 14}, {"command": 7, "file": 2, "line": 61, "parent": 15}, {"command": 6, "file": 0, "line": 640, "parent": 2}, {"command": 9, "file": 4, "line": 218, "parent": 11}, {"file": 6, "parent": 18}, {"command": 8, "file": 6, "line": 57, "parent": 19}, {"file": 5, "parent": 20}, {"command": 7, "file": 5, "line": 61, "parent": 21}, {"command": 8, "file": 6, "line": 45, "parent": 19}, {"file": 11, "parent": 23}, {"command": 11, "file": 11, "line": 46, "parent": 24}, {"command": 10, "file": 10, "line": 137, "parent": 25}, {"command": 9, "file": 9, "line": 76, "parent": 26}, {"file": 8, "parent": 27}, {"command": 8, "file": 8, "line": 55, "parent": 28}, {"file": 7, "parent": 29}, {"command": 7, "file": 7, "line": 61, "parent": 30}, {"command": 7, "file": 7, "line": 83, "parent": 30}, {"command": 8, "file": 3, "line": 43, "parent": 13}, {"file": 14, "parent": 33}, {"command": 11, "file": 14, "line": 45, "parent": 34}, {"command": 10, "file": 10, "line": 137, "parent": 35}, {"command": 9, "file": 9, "line": 76, "parent": 36}, {"file": 13, "parent": 37}, {"command": 8, "file": 13, "line": 55, "parent": 38}, {"file": 12, "parent": 39}, {"command": 7, "file": 12, "line": 61, "parent": 40}, {"command": 12, "file": 1, "line": 13, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 17, "fragment": "-Zc:__cplusplus"}, {"backtrace": 17, "fragment": "-permissive-"}, {"backtrace": 17, "fragment": "-utf-8"}], "defines": [{"backtrace": 17, "define": "QT_CORE_LIB"}, {"backtrace": 8, "define": "QT_GUI_LIB"}, {"backtrace": 8, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 8, "define": "QT_OPENGL_LIB"}, {"backtrace": 8, "define": "QT_WIDGETS_LIB"}, {"backtrace": 17, "define": "UNICODE"}, {"backtrace": 17, "define": "WIN32"}, {"backtrace": 17, "define": "WIN64"}, {"backtrace": 17, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 17, "define": "_UNICODE"}, {"backtrace": 17, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Desktop/Zview/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Zview_autogen/include"}, {"backtrace": 42, "path": "C:/Users/<USER>/Desktop/Zview/lib"}, {"backtrace": 17, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtCore"}, {"backtrace": 17, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include"}, {"backtrace": 17, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 8, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtWidgets"}, {"backtrace": 8, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtGui"}, {"backtrace": 8, "isSystem": true, "path": "C:/VulkanSDK/1.4.313.0/Include"}, {"backtrace": 8, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGL"}, {"backtrace": 8, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGLWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [17, 17], "standard": "17"}, "sourceIndexes": [0, 1, 2, 5, 7, 9]}], "dependencies": [{"id": "Zview_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "Zview_autogen::@6890427a1f51a3e7e1df"}], "id": "Zview::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Zview"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\Zview\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "role": "libraryPath"}, {"backtrace": 7, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\Zview\\build", "role": "libraryPath"}, {"backtrace": 8, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6OpenGLWidgetsd.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "mpv.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "windowscodecs.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6OpenGLd.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Zview", "nameOnDisk": "Zview.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 12, 13]}, {"name": "Source Files", "sourceIndexes": [1, 2, 5, 7, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 6, 8, 10, 11]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "CMake Rules", "sourceIndexes": [14]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Zview_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "zview.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "zview.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "zview.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "MpvWrapper.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "MpvWrapper.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "HeicImageLoader.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "HeicImageLoader.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "FrameIndex.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "FrameIndex.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "SimpleGpuDecoder.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Zview_autogen/include/ui_zview.h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Zview_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/Zview_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}