# Automatic Proxy Video Generation

## Overview
Zview now automatically generates proxy videos when you open a video file, providing better scrubbing performance and smoother playback experience.

## Features

### 1. Automatic Proxy Generation
- **Location**: Proxy videos are saved in `Documents/Zview_Proxies/` folder
- **Trigger**: Automatically starts when any video file is opened
- **Types**: Two proxy types are generated:
  - **Scrubbing Proxy**: 640x360, 500kbps - For fast scrubbing
  - **Preview Proxy**: 1280x720, 2000kbps - For high-quality preview

### 2. Progress Bar Display
- **Location**: Top-left corner of the video player
- **Style**: Semi-transparent dark background with blue progress bar
- **Information**: Shows generation progress as percentage
- **Behavior**: 
  - Appears when proxy generation starts
  - Updates in real-time showing progress
  - Disappears when generation completes

### 3. Enhanced Scrubbing Performance
- **MPV Engine**: Uses MPV for superior video processing
- **Smart Caching**: Integrates with smart caching system
- **Real-time Processing**: FFmpeg-based generation with progress tracking

## Technical Implementation

### Proxy Generation Process
1. When a video opens, the system checks if proxies already exist
2. If not found, FFmpeg is launched to generate proxy files
3. Progress is parsed from FFmpeg output and displayed in UI
4. Generated proxies are cached for future use

### File Naming Convention
Proxy files use this naming pattern:
```
[original_filename]_[hash]_[type].mp4
```
Where:
- `hash`: 8-character MD5 hash of source file path
- `type`: Either `scrub` or `preview`

### FFmpeg Parameters
**Scrubbing Proxy (640x360)**:
```
-vf scale=640:360 -c:v libx264 -preset fast -crf 23 -b:v 500k -c:a aac -b:a 128k -movflags +faststart
```

**Preview Proxy (1280x720)**:
```
-vf scale=1280:720 -c:v libx264 -preset fast -crf 23 -b:v 2000k -c:a aac -b:a 128k -movflags +faststart
```

## UI Components

### Progress Bar Container
- **Size**: 300x60 pixels
- **Position**: Fixed at top-left (10, 10)
- **Background**: Rounded semi-transparent dark overlay
- **Border**: Subtle white border for visibility

### Progress Bar
- **Size**: 280x20 pixels (within container)
- **Range**: 0-100%
- **Style**: Blue gradient fill with white text
- **Updates**: Real-time based on FFmpeg output parsing

### Progress Label
- **Text**: "Generating proxy video... X%"
- **Position**: Above progress bar
- **Style**: White bold text, center-aligned

## Integration

### Smart Cache Integration
- Proxy generation integrates with existing smart caching system
- Uses `SimpleProxyManager` class for queue management
- Connects to main video player for seamless operation

### Event Handling
- `proxyGenerationStarted`: Shows progress UI
- `proxyGenerationProgress`: Updates progress percentage  
- `proxyGenerationCompleted`: Hides progress UI and enables proxy use
- `proxyGenerationFailed`: Hides progress UI and logs error

## Dependencies

### Required Software
- **FFmpeg**: Must be installed and available in system PATH
- **MPV**: For video playback and rendering
- **Qt**: For UI components and file handling

### Qt Components Used
- `QProgressBar`: Main progress indicator
- `QLabel`: Progress text display
- `QWidget`: Container for UI elements
- `QProcess`: FFmpeg process management
- `QTimer`: Progress update timing

## Error Handling

### FFmpeg Not Found
- System falls back to simulated proxy generation
- Error logged to debug output
- User experience continues without interruption

### Generation Failures
- Failed generations are logged
- Progress UI is hidden on failure
- Original video playback continues normally

### Insufficient Disk Space
- FFmpeg will fail naturally if disk is full
- Error captured and logged
- System continues with original video file

## Performance Considerations

### Resource Usage
- Maximum 2 concurrent proxy generation jobs
- Low-priority background processing
- Minimal impact on video playback performance

### Storage Management
- Proxies stored in user's Documents folder
- Automatic cleanup of old proxies (configurable)
- Hash-based naming prevents conflicts

### Memory Usage
- Streaming progress updates to minimize memory usage
- Efficient FFmpeg output parsing
- UI components created only when needed

## Future Enhancements

### Planned Features
- Proxy generation queue management UI
- User-configurable proxy settings
- Batch proxy generation for multiple files
- Integration with video library management

### Possible Improvements
- GPU-accelerated encoding support
- Adaptive bitrate based on source quality
- Preview thumbnails during generation
- Network storage support for proxies
