/********************************************************************************
** Form generated from reading UI file 'zview.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ZVIEW_H
#define UI_ZVIEW_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Zview
{
public:
    QWidget *modernToolbox;
    QToolButton *cropTool;
    QToolButton *editTool;
    QToolButton *viewTool;
    QToolButton *toolButton;
    QToolButton *toolButton_2;
    QSlider *seekSlider;
    QPushButton *buttonA;
    QPushButton *buttonB;
    QSlider *volumeSlider;
    QWidget *modernToolbox_2;
    QToolButton *toolButton_5;
    QToolButton *toolButton_4;
    QToolButton *toolButton_3;

    void setupUi(QWidget *Zview)
    {
        if (Zview->objectName().isEmpty())
            Zview->setObjectName("Zview");
        Zview->resize(1400, 1000);
        modernToolbox = new QWidget(Zview);
        modernToolbox->setObjectName("modernToolbox");
        modernToolbox->setGeometry(QRect(570, 0, 180, 60));
        modernToolbox->setVisible(true);
        cropTool = new QToolButton(modernToolbox);
        cropTool->setObjectName("cropTool");
        cropTool->setGeometry(QRect(10, 6, 48, 48));
        QIcon icon;
        icon.addFile(QString::fromUtf8("c:/Users/<USER>/Desktop/Zview/crop-icon-white.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        cropTool->setIcon(icon);
        cropTool->setIconSize(QSize(20, 20));
        cropTool->setCheckable(true);
        editTool = new QToolButton(modernToolbox);
        editTool->setObjectName("editTool");
        editTool->setGeometry(QRect(65, 6, 48, 48));
        QIcon icon1(QIcon::fromTheme(QString::fromUtf8("document-edit")));
        editTool->setIcon(icon1);
        editTool->setCheckable(true);
        viewTool = new QToolButton(modernToolbox);
        viewTool->setObjectName("viewTool");
        viewTool->setGeometry(QRect(120, 6, 48, 48));
        QIcon icon2(QIcon::fromTheme(QString::fromUtf8("zoom-fit-best")));
        viewTool->setIcon(icon2);
        viewTool->setCheckable(true);
        toolButton = new QToolButton(Zview);
        toolButton->setObjectName("toolButton");
        toolButton->setGeometry(QRect(1200, 710, 36, 36));
        QIcon icon3(QIcon::fromTheme(QString::fromUtf8("edit-delete")));
        toolButton->setIcon(icon3);
        toolButton_2 = new QToolButton(Zview);
        toolButton_2->setObjectName("toolButton_2");
        toolButton_2->setGeometry(QRect(1155, 790, 36, 36));
        QIcon icon4(QIcon::fromTheme(QIcon::ThemeIcon::DocumentProperties));
        toolButton_2->setIcon(icon4);
        seekSlider = new QSlider(Zview);
        seekSlider->setObjectName("seekSlider");
        seekSlider->setGeometry(QRect(20, 600, 1135, 40));
        seekSlider->setVisible(false);
        seekSlider->setOrientation(Qt::Orientation::Horizontal);
        buttonA = new QPushButton(Zview);
        buttonA->setObjectName("buttonA");
        buttonA->setGeometry(QRect(20, 650, 30, 25));
        buttonA->setVisible(false);
        buttonB = new QPushButton(Zview);
        buttonB->setObjectName("buttonB");
        buttonB->setGeometry(QRect(55, 650, 30, 25));
        buttonB->setVisible(false);
        volumeSlider = new QSlider(Zview);
        volumeSlider->setObjectName("volumeSlider");
        volumeSlider->setGeometry(QRect(950, 740, 120, 30));
        volumeSlider->setVisible(false);
        volumeSlider->setMinimum(0);
        volumeSlider->setMaximum(100);
        volumeSlider->setValue(100);
        volumeSlider->setOrientation(Qt::Orientation::Horizontal);
        modernToolbox_2 = new QWidget(Zview);
        modernToolbox_2->setObjectName("modernToolbox_2");
        modernToolbox_2->setGeometry(QRect(1090, 520, 101, 131));
        modernToolbox_2->setVisible(false);
        toolButton_5 = new QToolButton(modernToolbox_2);
        toolButton_5->setObjectName("toolButton_5");
        toolButton_5->setGeometry(QRect(10, 10, 81, 31));
        toolButton_5->setVisible(false);
        toolButton_4 = new QToolButton(modernToolbox_2);
        toolButton_4->setObjectName("toolButton_4");
        toolButton_4->setGeometry(QRect(10, 50, 81, 31));
        toolButton_4->setVisible(false);
        toolButton_3 = new QToolButton(modernToolbox_2);
        toolButton_3->setObjectName("toolButton_3");
        toolButton_3->setGeometry(QRect(10, 90, 81, 31));
        toolButton_3->setVisible(false);

        retranslateUi(Zview);

        QMetaObject::connectSlotsByName(Zview);
    } // setupUi

    void retranslateUi(QWidget *Zview)
    {
        Zview->setWindowTitle(QCoreApplication::translate("Zview", "Zview", nullptr));
        Zview->setStyleSheet(QCoreApplication::translate("Zview", "background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                                            stop:0 rgb(28, 28, 30),\n"
"                                            stop:1 rgb(22, 22, 25));", nullptr));
        modernToolbox->setStyleSheet(QCoreApplication::translate("Zview", "QWidget#modernToolbox {\n"
"    background: rgba(35, 35, 38, 0.95);\n"
"    border: 1px solid rgba(60, 60, 65, 0.8);\n"
"    border-radius: 8px;\n"
"}\n"
"\n"
"QWidget#modernToolbox:hover {\n"
"    background: rgba(40, 40, 43, 0.95);\n"
"    border: 1px solid rgba(70, 70, 75, 0.9);\n"
"}", nullptr));
#if QT_CONFIG(tooltip)
        cropTool->setToolTip(QCoreApplication::translate("Zview", "Crop", nullptr));
#endif // QT_CONFIG(tooltip)
        cropTool->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}", nullptr));
        cropTool->setText(QString());
#if QT_CONFIG(tooltip)
        editTool->setToolTip(QCoreApplication::translate("Zview", "Edit", nullptr));
#endif // QT_CONFIG(tooltip)
        editTool->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}", nullptr));
        editTool->setText(QString());
#if QT_CONFIG(tooltip)
        viewTool->setToolTip(QCoreApplication::translate("Zview", "View", nullptr));
#endif // QT_CONFIG(tooltip)
        viewTool->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}", nullptr));
        viewTool->setText(QString());
        toolButton->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}", nullptr));
        toolButton->setText(QCoreApplication::translate("Zview", "...", nullptr));
        toolButton_2->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}", nullptr));
        toolButton_2->setText(QCoreApplication::translate("Zview", "...", nullptr));
        seekSlider->setStyleSheet(QCoreApplication::translate("Zview", "QSlider {\n"
"    background: transparent;\n"
"}\n"
"\n"
"QSlider::groove:horizontal {\n"
"    background: rgba(255, 255, 255, 0.15);\n"
"    height: 6px;\n"
"    border-radius: 3px;\n"
"}\n"
"\n"
"QSlider::handle:horizontal {\n"
"    background: #0078D4;\n"
"    border: none;\n"
"    width: 18px;\n"
"    height: 18px;\n"
"    border-radius: 9px;\n"
"    margin: -6px 0;\n"
"}\n"
"\n"
"QSlider::handle:horizontal:hover {\n"
"    background: #106EBE;\n"
"    transform: scale(1.1);\n"
"}\n"
"\n"
"QSlider::sub-page:horizontal {\n"
"    background: #0078D4;\n"
"    border-radius: 3px;\n"
"}", nullptr));
        buttonA->setStyleSheet(QCoreApplication::translate("Zview", "QPushButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    font-weight: 700;\n"
"    font-size: 14px;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"    padding: 6px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}\n"
"\n"
"QPushButton:checked {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}", nullptr));
        buttonA->setText(QCoreApplication::translate("Zview", "A", nullptr));
        buttonB->setStyleSheet(QCoreApplication::translate("Zview", "QPushButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    font-weight: 700;\n"
"    font-size: 14px;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"    padding: 6px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}\n"
"\n"
"QPushButton:checked {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}", nullptr));
        buttonB->setText(QCoreApplication::translate("Zview", "B", nullptr));
        volumeSlider->setStyleSheet(QCoreApplication::translate("Zview", "QSlider {\n"
"    background: transparent;\n"
"}\n"
"\n"
"QSlider::groove:horizontal {\n"
"    background: rgba(255, 255, 255, 0.15);\n"
"    height: 6px;\n"
"    border-radius: 3px;\n"
"}\n"
"\n"
"QSlider::handle:horizontal {\n"
"    background: #0078D4;\n"
"    border: none;\n"
"    width: 18px;\n"
"    height: 18px;\n"
"    border-radius: 9px;\n"
"    margin: -6px 0;\n"
"}\n"
"\n"
"QSlider::handle:horizontal:hover {\n"
"    background: #106EBE;\n"
"    transform: scale(1.1);\n"
"}\n"
"\n"
"QSlider::sub-page:horizontal {\n"
"    background: #0078D4;\n"
"    border-radius: 3px;\n"
"}", nullptr));
        modernToolbox_2->setStyleSheet(QCoreApplication::translate("Zview", "QWidget#modernToolbox_2 {\n"
"    background: rgba(35, 35, 38, 0.95);\n"
"    border: 1px solid rgba(60, 60, 65, 0.8);\n"
"    border-radius: 8px;\n"
"}\n"
"\n"
"QWidget#modernToolbox_2:hover {\n"
"    background: rgba(40, 40, 43, 0.95);\n"
"    border: 1px solid rgba(70, 70, 75, 0.9);\n"
"}", nullptr));
        toolButton_5->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}", nullptr));
        toolButton_5->setText(QCoreApplication::translate("Zview", "Apply Crop", nullptr));
        toolButton_4->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}", nullptr));
        toolButton_4->setText(QCoreApplication::translate("Zview", "Undo", nullptr));
        toolButton_3->setStyleSheet(QCoreApplication::translate("Zview", "QToolButton {\n"
"    background: rgba(60, 60, 65, 0.95);\n"
"    color: #FFFFFF;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: 600;\n"
"    font-family: 'Segoe UI', system-ui, sans-serif;\n"
"}\n"
"\n"
"QToolButton:hover {\n"
"    background: rgba(0, 120, 212, 1.0);\n"
"    color: #FFFFFF;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: rgba(0, 90, 158, 1.0);\n"
"    transform: scale(0.96);\n"
"}", nullptr));
        toolButton_3->setText(QCoreApplication::translate("Zview", "Exit Crop", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Zview: public Ui_Zview {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ZVIEW_H
