{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "C:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "C:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "C:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-f81000cd1c70efe74ce1.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-9a4193955c626abfb3db.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-01c32c7f08102f744e8b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-9a4193955c626abfb3db.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-01c32c7f08102f744e8b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-f81000cd1c70efe74ce1.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}