#include "zview.h"
#include "ui_zview.h"
#include "ImageCanvas.h"
#include "VideoCanvas.h"
#include "ToolPanel.h"
#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QStackedWidget>
#include <QTimer>
#include <QFileInfo>
#include <QMimeData>
#include <QUrl>
#include <QKeyEvent>
#include <QPainter>
#include <QPainterPath>
#include <QBitmap>
#include <QRegion>
#include <QDebug>

Zview::Zview(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::Zview)
{
    ui->setupUi(this);
    
    // Remove title bar and window decorations
    setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
    
    // Enable transparency for rounded corners
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);
    
    // Enable drag and drop
    setAcceptDrops(true);
    
    // Set default window size
    resize(1200, 800);
    
    setupUI();
    setupConnections();
    
    // Setup control visibility timer
    m_controlVisibilityTimer = new QTimer(this);
    m_controlVisibilityTimer->setSingleShot(true);
    connect(m_controlVisibilityTimer, &QTimer::timeout, this, &Zview::onControlVisibilityTimer);
    
    // Initially show in image mode
    m_stackedWidget->setCurrentWidget(m_imageCanvas);
    m_currentMode = ViewMode::Image;
}

Zview::~Zview()
{
    delete ui;
}

void Zview::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);
    
    // Main layout
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // Create stacked widget for switching between image and video modes
    m_stackedWidget = new QStackedWidget();
    
    // Create canvas components
    m_imageCanvas = new ImageCanvas();
    m_videoCanvas = new VideoCanvas();
    
    m_stackedWidget->addWidget(m_imageCanvas);
    m_stackedWidget->addWidget(m_videoCanvas);
    
    // Create tool panel
    m_toolPanel = new ToolPanel();
    
    // Add to main layout
    m_mainLayout->addWidget(m_stackedWidget);
    
    // Position tool panel as overlay (will be positioned in resizeEvent)
    m_toolPanel->setParent(m_centralWidget);
    m_toolPanel->hide();
}

void Zview::setupConnections()
{
    // Image canvas connections
    connect(m_imageCanvas, &ImageCanvas::imageLoaded, this, &Zview::onImageLoaded);
    connect(m_imageCanvas, &ImageCanvas::imageLoadFailed, this, &Zview::onImageLoadFailed);
    connect(m_imageCanvas, &ImageCanvas::zoomChanged, this, &Zview::onZoomChanged);
    
    // Video canvas connections
    connect(m_videoCanvas, &VideoCanvas::videoLoaded, this, &Zview::onVideoLoaded);
    connect(m_videoCanvas, &VideoCanvas::videoLoadFailed, this, &Zview::onVideoLoadFailed);
    connect(m_videoCanvas, &VideoCanvas::positionChanged, this, &Zview::onVideoPositionChanged);
    connect(m_videoCanvas, &VideoCanvas::durationChanged, this, &Zview::onVideoDurationChanged);
    connect(m_videoCanvas, &VideoCanvas::playStateChanged, this, &Zview::onVideoPlayStateChanged);
    connect(m_videoCanvas, &VideoCanvas::volumeChanged, this, &Zview::onVideoVolumeChanged);
    
    // Tool panel connections
    connect(m_toolPanel, &ToolPanel::playPauseClicked, this, &Zview::onPlayPauseClicked);
    connect(m_toolPanel, &ToolPanel::stopClicked, this, &Zview::onStopClicked);
    connect(m_toolPanel, &ToolPanel::positionChanged, this, &Zview::onPositionChanged);
    connect(m_toolPanel, &ToolPanel::volumeChanged, this, &Zview::onVolumeChanged);
    connect(m_toolPanel, &ToolPanel::previousClicked, this, &Zview::onPreviousClicked);
    connect(m_toolPanel, &ToolPanel::nextClicked, this, &Zview::onNextClicked);
    connect(m_toolPanel, &ToolPanel::setAPointClicked, this, &Zview::onSetAPointClicked);
    connect(m_toolPanel, &ToolPanel::setBPointClicked, this, &Zview::onSetBPointClicked);
    connect(m_toolPanel, &ToolPanel::clearABLoopClicked, this, &Zview::onClearABLoopClicked);
    connect(m_toolPanel, &ToolPanel::zoomInClicked, this, &Zview::onZoomInClicked);
    connect(m_toolPanel, &ToolPanel::zoomOutClicked, this, &Zview::onZoomOutClicked);
    connect(m_toolPanel, &ToolPanel::resetZoomClicked, this, &Zview::onResetZoomClicked);
    connect(m_toolPanel, &ToolPanel::fitToWindowClicked, this, &Zview::onFitToWindowClicked);
    connect(m_toolPanel, &ToolPanel::actualSizeClicked, this, &Zview::onActualSizeClicked);
    connect(m_toolPanel, &ToolPanel::fullscreenToggled, this, &Zview::onFullscreenToggled);
}

void Zview::openFile(const QString &filePath)
{
    m_currentFilePath = filePath;
    updateWindowTitle(filePath);
    
    if (isVideoFile(filePath)) {
        loadVideoFile(filePath);
    } else if (isImageFile(filePath)) {
        loadImageFile(filePath);
    }
}

void Zview::loadImageFile(const QString &filePath)
{
    m_currentMode = ViewMode::Image;
    m_stackedWidget->setCurrentWidget(m_imageCanvas);
    m_imageCanvas->loadImage(filePath);
}

void Zview::loadVideoFile(const QString &filePath)
{
    m_currentMode = ViewMode::Video;
    m_stackedWidget->setCurrentWidget(m_videoCanvas);
    m_videoCanvas->loadVideo(filePath);
}

bool Zview::isVideoFile(const QString &filePath)
{
    static QStringList videoExtensions = {
        "mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v",
        "3gp", "3g2", "f4v", "f4p", "f4a", "f4b", "ogv", "ogg",
        "MP4", "MKV", "AVI", "MOV", "WMV", "FLV", "WEBM", "M4V"
    };
    
    QFileInfo fileInfo(filePath);
    return videoExtensions.contains(fileInfo.suffix());
}

bool Zview::isImageFile(const QString &filePath)
{
    static QStringList imageExtensions = {
        "jpg", "jpeg", "png", "bmp", "gif", "tiff", "tif", "webp", "heic", "heif",
        "JPG", "JPEG", "PNG", "BMP", "GIF", "TIFF", "TIF", "WEBP", "HEIC", "HEIF"
    };
    
    QFileInfo fileInfo(filePath);
    return imageExtensions.contains(fileInfo.suffix());
}

void Zview::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    }
}

void Zview::dropEvent(QDropEvent *event)
{
    const QMimeData *mimeData = event->mimeData();
    if (mimeData->hasUrls()) {
        QUrl url = mimeData->urls().first();
        QString filePath = url.toLocalFile();
        
        if (!filePath.isEmpty()) {
            openFile(filePath);
        }
    }
}

void Zview::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Escape:
        if (m_isFullscreen) {
            onFullscreenToggled();
        } else {
            close();
        }
        break;
    case Qt::Key_F11:
        onFullscreenToggled();
        break;
    case Qt::Key_Left:
        if (m_currentMode == ViewMode::Image) {
            m_imageCanvas->loadPreviousImage();
        }
        break;
    case Qt::Key_Right:
        if (m_currentMode == ViewMode::Image) {
            m_imageCanvas->loadNextImage();
        }
        break;
    case Qt::Key_Space:
        if (m_currentMode == ViewMode::Video) {
            onPlayPauseClicked();
        }
        break;
    default:
        QMainWindow::keyPressEvent(event);
    }
}

void Zview::mouseMoveEvent(QMouseEvent *event)
{
    showControls();
    QMainWindow::mouseMoveEvent(event);
}

void Zview::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    repositionControls();
    setRoundedMask();
}

// Image canvas slots
void Zview::onImageLoaded(const QString &filePath)
{
    updateWindowTitle(filePath);
    
    // Update tool panel with image info
    QFileInfo fileInfo(filePath);
    QString info = QString("%1 - %2").arg(fileInfo.fileName()).arg(fileInfo.size());
    m_toolPanel->setImageInfo(info);
}

void Zview::onImageLoadFailed(const QString &filePath)
{
    qDebug() << "Failed to load image:" << filePath;
}

void Zview::onZoomChanged(float zoomFactor)
{
    m_toolPanel->setZoomFactor(zoomFactor);
}

// Video canvas slots
void Zview::onVideoLoaded(const QString &filePath)
{
    updateWindowTitle(filePath);
    showControls();
}

void Zview::onVideoLoadFailed(const QString &filePath)
{
    qDebug() << "Failed to load video:" << filePath;
}

void Zview::onVideoPositionChanged(double position)
{
    m_toolPanel->setPosition(position);
}

void Zview::onVideoDurationChanged(double duration)
{
    m_toolPanel->setDuration(duration);
}

void Zview::onVideoPlayStateChanged(bool playing)
{
    m_toolPanel->setPlaying(playing);
}

void Zview::onVideoVolumeChanged(int volume)
{
    m_toolPanel->setVolume(volume);
}

// Tool panel slots
void Zview::onPlayPauseClicked()
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->togglePlayPause();
    }
}

void Zview::onStopClicked()
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->stop();
    }
}

void Zview::onPositionChanged(double position)
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->seek(position);
    }
}

void Zview::onVolumeChanged(int volume)
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->setVolume(volume);
    }
}

void Zview::onPreviousClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->loadPreviousImage();
    }
}

void Zview::onNextClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->loadNextImage();
    }
}

void Zview::onSetAPointClicked()
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->setAPoint();
    }
}

void Zview::onSetBPointClicked()
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->setBPoint();
    }
}

void Zview::onClearABLoopClicked()
{
    if (m_currentMode == ViewMode::Video) {
        m_videoCanvas->clearABLoop();
    }
}

void Zview::onZoomInClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->zoomIn();
    }
}

void Zview::onZoomOutClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->zoomOut();
    }
}

void Zview::onResetZoomClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->resetZoom();
    }
}

void Zview::onFitToWindowClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->fitToWindow();
    }
}

void Zview::onActualSizeClicked()
{
    if (m_currentMode == ViewMode::Image) {
        m_imageCanvas->actualSize();
    }
}

void Zview::onFullscreenToggled()
{
    if (m_isFullscreen) {
        // Exit fullscreen
        setWindowState(windowState() & ~Qt::WindowFullScreen);
        setGeometry(m_normalGeometry);
        setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
        show();
        m_isFullscreen = false;
    } else {
        // Enter fullscreen
        m_normalGeometry = geometry();
        setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
        setWindowState(windowState() | Qt::WindowFullScreen);
        show();
        m_isFullscreen = true;
    }
    
    repositionControls();
}

void Zview::onControlVisibilityTimer()
{
    hideControls();
}

void Zview::showControls()
{
    if (!m_controlsVisible) {
        m_controlsVisible = true;
        m_toolPanel->showControls();
    }
    
    // Reset the auto-hide timer
    m_controlVisibilityTimer->start(3000); // Hide after 3 seconds
}

void Zview::hideControls()
{
    if (m_controlsVisible) {
        m_controlsVisible = false;
        m_toolPanel->hideControls();
    }
    
    m_controlVisibilityTimer->stop();
}

void Zview::updateWindowTitle(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    setWindowTitle(QString("Zview - %1").arg(fileInfo.fileName()));
}

void Zview::setRoundedMask()
{
    if (!m_isFullscreen) {
        QPainterPath path;
        path.addRoundedRect(rect(), m_cornerRadius, m_cornerRadius);
        QRegion region(path.toFillPolygon().toPolygon());
        setMask(region);
    } else {
        clearMask();
    }
}

void Zview::repositionControls()
{
    if (m_toolPanel) {
        // Position tool panel at the bottom of the window
        int panelWidth = width() - 40; // 20px margin on each side
        int panelHeight = m_toolPanel->height();
        int x = 20;
        int y = height() - panelHeight - 20;
        
        m_toolPanel->setGeometry(x, y, panelWidth, panelHeight);
    }
}

#include "zview.moc"
