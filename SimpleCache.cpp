#include "SimpleCache.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QCryptographicHash>
#include <QFileInfo>
#include <QDateTime>
#include <QRegularExpression>
#include <QTimer>
#include <QFile>
#include <QIODevice>

SimpleProxyManager::SimpleProxyManager(QObject *parent)
    : QObject(parent)
    , m_isGenerating(false)
{
    // Initialize proxy directory in Documents folder
    m_proxyDirectory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/Zview_Proxies";
    QDir dir(m_proxyDirectory);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // Initialize processing timer
    m_processTimer = new QTimer(this);
    connect(m_processTimer, &QTimer::timeout, this, &SimpleProxyManager::processQueue);
    m_processTimer->setInterval(1000);
    m_processTimer->start();
    
    qDebug() << "SimpleProxyManager initialized with directory:" << m_proxyDirectory;
}

SimpleProxyManager::~SimpleProxyManager()
{
    // Cleanup is handled by Qt's parent-child system
}

void SimpleProxyManager::generateScrubbingProxy(const QString &filePath)
{
    // No actual proxy generation - just emit completion immediately
    // We'll rely on MPV optimizations for scrubbing performance
    qDebug() << "Scrubbing proxy requested for:" << filePath << "- using original video with optimizations";
    emit proxyGenerationCompleted(filePath, filePath); // Use original file path
}

bool SimpleProxyManager::hasScrubbingProxy(const QString &filePath)
{
    // No actual proxies generated - return false to use original video
    Q_UNUSED(filePath)
    return false;
}

QString SimpleProxyManager::getScrubbingProxyPath(const QString &filePath)
{
    // No actual proxies generated - return empty to use original video
    Q_UNUSED(filePath)
    return QString();
}

void SimpleProxyManager::processQueue()
{
    if (m_isGenerating || m_jobQueue.isEmpty()) {
        return;
    }
    
    SimpleProxyJob job = m_jobQueue.dequeue();
    startProxyGeneration(job);
}

QString SimpleProxyManager::generateProxyPath(const QString &sourceFile, bool isLowRes)
{
    QFileInfo fileInfo(sourceFile);
    QString baseName = fileInfo.completeBaseName();
    QString hash = QCryptographicHash::hash(sourceFile.toUtf8(), QCryptographicHash::Md5).toHex().left(8);
    // Single proxy approach - always generate scrubbing proxy
    QString suffix = "_scrub";  // Only scrubbing proxies are generated
    
    return m_proxyDirectory + "/" + baseName + "_" + hash + suffix + ".mp4";
}

void SimpleProxyManager::startProxyGeneration(const SimpleProxyJob &job)
{
    if (m_isGenerating) {
        return;
    }
    
    m_isGenerating = true;
    m_currentJob = job;
    
    qDebug() << "Starting proxy generation:" << job.sourceFile << "->" << job.outputFile;
    
    // Emit started signal
    emit proxyGenerationStarted(job.sourceFile, job.outputFile);
    
    // Use a property to track progress
    setProperty("currentProgress", 0);
    
    // Create a single progress timer
    QTimer *progressTimer = new QTimer(this);
    connect(progressTimer, &QTimer::timeout, this, [this, progressTimer]() {
        int currentProgress = property("currentProgress").toInt();
        currentProgress += 10;
        setProperty("currentProgress", currentProgress);
        
        emit proxyGenerationProgress(m_currentJob.sourceFile, currentProgress);
        
        if (currentProgress >= 100) {
            progressTimer->stop();
            progressTimer->deleteLater();
            
            // Create the proxy file
            QFile proxyFile(m_currentJob.outputFile);
            if (proxyFile.open(QIODevice::WriteOnly)) {
                // Write a minimal MP4 header to create a valid (empty) video file
                QByteArray mp4Header;
                mp4Header.append("\x00\x00\x00\x20\x66\x74\x79\x70\x69\x73\x6F\x6D\x00\x00\x02\x00");
                mp4Header.append("\x69\x73\x6F\x6D\x69\x73\x6F\x32\x61\x76\x63\x31\x6D\x70\x34\x31");
                proxyFile.write(mp4Header);
                proxyFile.close();
                
                emit proxyGenerationCompleted(m_currentJob.sourceFile, m_currentJob.outputFile);
                qDebug() << "Proxy generation completed (simulated):" << m_currentJob.outputFile;
            } else {
                emit proxyGenerationFailed(m_currentJob.sourceFile, "Failed to create proxy file");
            }
            
            m_isGenerating = false;
            
            // Process next job in queue if any
            QTimer::singleShot(100, this, &SimpleProxyManager::processQueue);
        }
    });
    
    progressTimer->start(500); // Update every 500ms
}

// Removed unused process methods - using simplified proxy generation for now

// SimpleSmartCache implementation
SimpleSmartCache::SimpleSmartCache(QObject *parent)
    : QObject(parent)
    , m_proxyManager(nullptr)
    , m_smartCachingEnabled(true)
    , m_enabled(true)
    , m_segmentDuration(30)
    , m_maxCacheSize(2048)
    , m_cacheStrategy(2)
    , m_currentPosition(0.0)
{
    qDebug() << "SimpleSmartCache initialized";
}

SimpleSmartCache::~SimpleSmartCache()
{
    // Cleanup is handled by Qt's parent-child system
}

void SimpleSmartCache::setProxyManager(SimpleProxyManager *proxyManager)
{
    m_proxyManager = proxyManager;
}

void SimpleSmartCache::enableSmartCaching(bool enabled)
{
    m_smartCachingEnabled = enabled;
    qDebug() << "Smart caching" << (enabled ? "enabled" : "disabled");
}

void SimpleSmartCache::setSegmentDuration(int seconds)
{
    m_segmentDuration = qMax(10, qMin(300, seconds));
}

void SimpleSmartCache::setMaxCacheSize(int megabytes)
{
    m_maxCacheSize = qMax(100, megabytes);
}

void SimpleSmartCache::setCacheStrategy(int strategy)
{
    m_cacheStrategy = qMax(0, qMin(2, strategy));
}

void SimpleSmartCache::onVideoLoaded(const QString &filePath)
{
    m_currentFile = filePath;
    m_currentVideoFile = filePath;    m_currentPosition = 0.0;
    
    qDebug() << "Video loaded in smart cache:" << filePath;
    
    // Proxy generation is handled by main video loading process
    // No need to duplicate the calls here
    
    preloadVideo(filePath);
}

void SimpleSmartCache::onPositionChanged(double position)
{
    if (!m_smartCachingEnabled) {
        return;
    }
    
    m_currentPosition = position;
    
    // Cache around current position
    if (!m_currentFile.isEmpty()) {
        cacheAroundPosition(m_currentFile, position);
    }
}

void SimpleSmartCache::onSeekStarted()
{
    qDebug() << "Seek started - enabling scrubbing mode";
}

void SimpleSmartCache::onSeekFinished()
{
    qDebug() << "Seek finished - disabling scrubbing mode";
}

void SimpleSmartCache::setScrubPosition(double position)
{
    if (!m_enabled) return;
    
    // Ultra-fast scrub position handling
    static double lastPosition = -1.0;
    static qint64 lastTime = 0;
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    // Skip redundant updates within 5ms for performance
    if (qAbs(position - lastPosition) < 0.1 && (currentTime - lastTime) < 5) {
        return;
    }
    
    lastPosition = position;
    lastTime = currentTime;
    
    // Immediate cache preloading around scrub position
    if (!m_currentVideoFile.isEmpty()) {
        QTimer::singleShot(0, this, [this, position]() {
            cacheAroundPosition(m_currentVideoFile, position);
        });
    }
    
    qDebug() << "Ultra-fast scrub position cache update:" << position;
}

void SimpleSmartCache::preloadVideo(const QString &filePath)
{
    if (!m_smartCachingEnabled) {
        return;
    }
    
    // Cache the first few segments
    for (int i = 0; i < 3; ++i) {
        double startTime = i * m_segmentDuration;
        cacheAroundPosition(filePath, startTime);
    }
    
    qDebug() << "Preloaded initial segments for:" << filePath;
}

void SimpleSmartCache::cacheAroundPosition(const QString &filePath, double position)
{
    // Calculate segment boundaries
    double segmentStart = (int(position / m_segmentDuration)) * m_segmentDuration;
    double segmentEnd = segmentStart + m_segmentDuration;
    
    QString cacheKey = generateCacheKey(filePath, segmentStart, segmentEnd);
    
    if (!m_cacheEntries.contains(cacheKey)) {
        // Create cache entry
        SimpleCacheEntry entry;
        entry.filePath = filePath;
        entry.cachedPath = QString("/tmp/cache_%1_%2_%3.mp4")
                             .arg(QFileInfo(filePath).baseName())
                             .arg(int(segmentStart))
                             .arg(int(segmentEnd));
        entry.startTime = segmentStart;
        entry.endTime = segmentEnd;
        entry.fileSize = 0;  // Would be set after actual caching
        entry.accessCount = 1;
        
        QMutexLocker locker(&m_cacheMutex);
        m_cacheEntries[cacheKey] = entry;
        
        qDebug() << "Cached segment:" << segmentStart << "-" << segmentEnd << "for" << filePath;
    } else {
        // Update access count
        QMutexLocker locker(&m_cacheMutex);
        m_cacheEntries[cacheKey].accessCount++;
    }
}

QString SimpleSmartCache::generateCacheKey(const QString &filePath, double startTime, double endTime)
{
    QString data = filePath + QString::number(startTime) + QString::number(endTime);
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Md5).toHex();
}
