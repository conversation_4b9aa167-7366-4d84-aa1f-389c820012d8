#ifndef FRAMEINDEX_H
#define FRAMEINDEX_H

#include <vector>
#include <map>
#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>

// Frame index entry structure
struct FrameIndexEntry {
    double timestamp;           // Frame timestamp in seconds
    int64_t filePosition;      // Byte offset in file
    bool isKeyFrame;           // Is this a keyframe/I-frame?
    int frameSize;             // Size of frame data in bytes
    int width, height;         // Frame dimensions
    int streamIndex;           // Stream index (for multi-stream files)
    
    FrameIndexEntry() : timestamp(0.0), filePosition(0), isKeyFrame(false),
                       frameSize(0), width(0), height(0), streamIndex(0) {}
    
    FrameIndexEntry(double ts, int64_t pos, bool key, int size, int w, int h, int stream = 0)
        : timestamp(ts), filePosition(pos), isKeyFrame(key), frameSize(size),
          width(w), height(h), streamIndex(stream) {}
};

// Seek table for fast random access
class SeekTable {
public:
    SeekTable();
    ~SeekTable();
    
    // Build seek table from video file
    bool buildFromFile(const std::string& filePath);
    
    // Find nearest keyframe before or at timestamp
    FrameIndexEntry findNearestKeyFrame(double timestamp) const;
    
    // Find exact frame at timestamp (interpolated if necessary)
    FrameIndexEntry findFrameAtTime(double timestamp) const;
    
    // Get all keyframes in a time range
    std::vector<FrameIndexEntry> getKeyFramesInRange(double startTime, double endTime) const;
    
    // Get frame for scrubbing (optimized for preview)
    FrameIndexEntry getFrameForScrubbing(double timestamp) const;
    
    // Statistics
    size_t getFrameCount() const { return m_frames.size(); }
    size_t getKeyFrameCount() const { return m_keyFrames.size(); }
    double getDuration() const { return m_duration; }
    double getAverageFrameRate() const;
    
    // Serialization for caching
    bool saveToFile(const std::string& cacheFile) const;
    bool loadFromFile(const std::string& cacheFile);
    
    // Memory management
    void clear();
    size_t getMemoryUsage() const;

private:
    std::vector<FrameIndexEntry> m_frames;      // All frames
    std::vector<size_t> m_keyFrames;            // Indices of keyframes in m_frames
    std::map<double, size_t> m_timeIndex;       // Timestamp to frame index mapping
    
    double m_duration;
    std::string m_filePath;
    mutable std::mutex m_mutex;
    
    // Helper functions
    size_t findFrameIndexByTime(double timestamp) const;
    void buildTimeIndex();
    bool parseVideoFile(const std::string& filePath);
};

// Advanced frame indexing with preview generation
class FrameIndexer {
public:
    struct IndexingOptions {
        bool generatePreviews = true;       // Generate thumbnail previews
        int previewWidth = 160;             // Preview thumbnail width
        int previewHeight = 90;             // Preview thumbnail height
        double keyFrameInterval = 10.0;     // Desired keyframe interval for scrubbing
        bool enableCaching = true;          // Cache index to disk
        std::string cacheDirectory = "./cache";
        
        IndexingOptions() = default;
    };
    
    struct PreviewFrame {
        double timestamp;
        std::vector<uint8_t> imageData;     // RGBA image data
        int width, height;
        
        PreviewFrame() : timestamp(0.0), width(0), height(0) {}
    };
    
    enum class IndexingState {
        Idle,
        Indexing,
        Complete,
        Error
    };

public:
    FrameIndexer();
    ~FrameIndexer();
    
    // Configuration
    void setOptions(const IndexingOptions& options);
    IndexingOptions getOptions() const { return m_options; }
    
    // Indexing operations
    bool startIndexing(const std::string& filePath);
    void stopIndexing();
    bool isIndexingComplete() const;
    IndexingState getState() const { return m_state; }
    double getIndexingProgress() const { return m_progress; }
    
    // Access indexed data
    const SeekTable& getSeekTable() const { return m_seekTable; }
    
    // Preview generation for scrubbing
    PreviewFrame getPreviewAtTime(double timestamp) const;
    std::vector<PreviewFrame> getPreviewsInRange(double startTime, double endTime, int maxCount = 10) const;
    
    // Smart seeking for optimal playback
    double findOptimalSeekPosition(double targetTime) const;
    
    // Scrubbing support
    struct ScrubFrame {
        double timestamp;
        std::vector<uint8_t> thumbnailData;
        int thumbnailWidth, thumbnailHeight;
        bool isKeyFrame;
    };
    
    std::vector<ScrubFrame> generateScrubTrack(int trackWidth, double videoDuration) const;
    ScrubFrame getScrubFrameAtPosition(double normalizedPosition) const; // 0.0 to 1.0
    
    // Performance optimization
    void setMaxMemoryUsage(size_t bytes) { m_maxMemoryUsage = bytes; }
    void enableBackgroundIndexing(bool enable) { m_backgroundIndexing = enable; }
    
    // Error handling
    std::string getLastError() const { return m_lastError; }

private:
    // Threading
    void indexingWorker();
    std::thread m_indexingThread;
    std::atomic<bool> m_stopRequested{false};
    std::atomic<bool> m_indexingActive{false};
    
    // State
    IndexingState m_state;
    std::atomic<double> m_progress{0.0};
    std::string m_currentFile;
    std::string m_lastError;
    
    // Configuration
    IndexingOptions m_options;
    size_t m_maxMemoryUsage;
    bool m_backgroundIndexing;
    
    // Data structures
    SeekTable m_seekTable;
    std::map<double, PreviewFrame> m_previewCache;
    std::vector<ScrubFrame> m_scrubTrack;
    
    // Synchronization
    mutable std::mutex m_dataMutex;
    mutable std::mutex m_previewMutex;
    
    // Helper functions
    bool generatePreviewAtTime(double timestamp, PreviewFrame& preview);
    void buildScrubTrack();
    std::string getCacheFilePath(const std::string& videoPath) const;
    bool loadCachedIndex();
    void saveCachedIndex();
    void cleanupMemory();
};

// J-K-L scrubbing controller
class ScrubController {
public:
    enum class ScrubMode {
        Stopped,
        PlayForward,
        PlayBackward,
        FastForward,
        FastBackward,
        FrameByFrame
    };
    
    struct ScrubState {
        ScrubMode mode = ScrubMode::Stopped;
        double currentTime = 0.0;
        double playbackSpeed = 1.0;
        bool isPlaying = false;
        double frameRate = 23.976; // Default frame rate
    };

public:
    ScrubController(FrameIndexer* indexer);
    ~ScrubController() = default;
    
    // J-K-L controls
    void handleJKey(); // Step backward / slow reverse
    void handleKKey(); // Pause / stop
    void handleLKey(); // Step forward / fast forward
    
    // Frame stepping
    void stepFrame(int frames); // Positive = forward, negative = backward
    void stepToKeyFrame(bool forward);
    
    // Playback speed control
    void setPlaybackSpeed(double speed); // 1.0 = normal, 2.0 = 2x, -1.0 = reverse
    double getPlaybackSpeed() const { return m_state.playbackSpeed; }
    
    // Position control
    void seekToTime(double timestamp);
    void seekToPosition(double normalizedPosition); // 0.0 to 1.0
    double getCurrentTime() const { return m_state.currentTime; }
    
    // State
    ScrubState getState() const { return m_state; }
    ScrubMode getMode() const { return m_state.mode; }
    
    // Frame rate
    void setFrameRate(double fps) { m_state.frameRate = fps; }
    double getFrameRate() const { return m_state.frameRate; }

private:
    FrameIndexer* m_indexer;
    ScrubState m_state;
    std::chrono::steady_clock::time_point m_lastUpdate;
    
    // Helper functions
    void updatePosition();
    double getFrameDuration() const { return 1.0 / m_state.frameRate; }
};

#endif // FRAMEINDEX_H
