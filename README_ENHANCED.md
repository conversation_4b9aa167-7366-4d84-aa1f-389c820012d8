# Enhanced Zview Video Player

A professional-grade video player with advanced features for smooth playback, precise control, and optimal performance.

## 🚀 Key Features

### GPU-Accelerated Playback
- **Hardware Acceleration**: Auto-detects and uses NVIDIA, AMD, Intel, DXVA2, or D3D11 acceleration
- **Optimized Rendering**: Custom OpenGL shaders for maximum performance
- **Smart Fallback**: Gracefully falls back to software decoding when needed

### Professional J-K-L Scrubbing
- **Industry Standard**: Professional video editing controls
- **Variable Speed**: 0.25x to 8x playback speeds
- **Frame-Perfect**: Single frame stepping and keyframe navigation
- **Smooth Control**: Fluid speed transitions and precise positioning

### Intelligent Caching System
- **Smart Previews**: Automatic thumbnail and proxy generation
- **Background Processing**: Non-blocking cache operations
- **Memory Efficient**: LRU cache management with configurable limits
- **Persistent Storage**: Caches survive application restarts

### Advanced Frame Indexing
- **Fast Seeking**: Build comprehensive seek tables for instant navigation
- **Keyframe Detection**: Identify and leverage I-frames for optimal seeking
- **Background Indexing**: Non-blocking index generation with progress monitoring
- **Cached Indexes**: Persistent storage of frame data

### Visual Preview Scrubbing
- **Real-time Thumbnails**: Live preview generation during scrubbing
- **Timeline Visualization**: Keyframe markers and buffer visualization
- **Smooth Animations**: Fluid preview updates and transitions
- **Customizable Display**: Configurable preview size and behavior

## 🎮 Controls

### J-K-L Scrubbing (Professional Mode)
- **J**: Reverse playback (1x, 2x, 4x, 8x speeds)
- **K**: Stop/Pause (always returns to stopped state)
- **L**: Forward playback (1x, 2x, 4x, 8x speeds)

### Frame Navigation
- **← →**: Step single frames backward/forward
- **< >** (Comma/Period): Jump to previous/next keyframes
- **0-9**: Seek to percentage (0=start, 1=10%, 2=20%, etc.)

### Playback Control
- **Space**: Play/Pause toggle
- **↑ ↓**: Volume up/down
- **+ -**: Playback speed control (with Shift) or duration adjustment

### Feature Toggles
- **T**: Toggle J-K-L scrubbing mode
- **I**: Start/check frame indexing progress
- **P**: Toggle visual preview scrubbing
- **F**: Toggle fullscreen mode

## 🛠️ Building

### Prerequisites
- Visual Studio 2022 with C++ tools
- Qt 6.5 or later
- CMake 3.19 or later
- MPV development libraries

### Quick Build
```bash
# Run the automated build script
build_enhanced.bat
```

### Manual Build
```bash
mkdir build
cd build
cmake -G "Visual Studio 17 2022" -A x64 ..
cmake --build . --config Release
```

## ⚙️ Configuration

### Automatic Configuration
The player automatically detects optimal settings based on your hardware:
- GPU capabilities and acceleration type
- Available memory and optimal buffer sizes
- CPU cores for threading
- Storage for caching

### Manual Configuration
Edit `zview_enhanced.conf` to customize:
- Hardware acceleration preferences
- Cache sizes and locations
- Preview generation settings
- Performance optimization parameters

### Runtime Configuration
Use keyboard shortcuts to toggle features:
- Enable/disable J-K-L mode with **T**
- Start frame indexing with **I**
- Toggle visual previews with **P**

## 📊 Performance Monitoring

### Real-time Metrics
- Frame rate and dropped frame statistics
- Buffer health and status
- Hardware acceleration status
- Memory usage tracking
- Decoding and rendering latency

### Optimization Features
- Adaptive buffering based on content
- Intelligent frame dropping
- Multi-threaded decoding
- GPU memory management

## 🎯 Use Cases

### Professional Video Editing
- Frame-accurate navigation
- J-K-L scrubbing for precise control
- Keyframe-based editing workflow
- High-performance playback

### Media Review and Analysis
- Detailed frame inspection
- Performance monitoring
- Quality assessment tools
- Batch processing capabilities

### General Video Playback
- Smooth playback of any format
- Intelligent caching for instant access
- Hardware-accelerated rendering
- Professional-grade controls

## 🔧 Technical Details

### Architecture
```
Enhanced Playback Engine
├── GPU Decoder (Hardware acceleration)
├── Frame Indexer (Seek optimization)
├── Smart Cache (Preview generation)
├── Scrub Controller (J-K-L handling)
└── Performance Monitor (Metrics tracking)
```

### Supported Formats
- All formats supported by MPV
- Hardware-accelerated formats (H.264, H.265, VP9, AV1)
- High-resolution content (4K, 8K)
- High frame rate content (60fps, 120fps)

### Hardware Requirements
- **Minimum**: DirectX 11 compatible GPU
- **Recommended**: Dedicated GPU with hardware decoding
- **Optimal**: Modern GPU with latest drivers

## 🐛 Troubleshooting

### Common Issues

**Hardware acceleration not working:**
- Update GPU drivers
- Check GPU compatibility
- Verify DirectX/OpenGL support
- Use automatic detection mode

**Performance issues:**
- Reduce buffer size
- Disable preview generation
- Use lower proxy quality
- Check available system memory

**Seeking problems:**
- Enable frame indexing
- Wait for indexing completion
- Clear cache and rebuild
- Check video file integrity

### Debug Information
Enable debug logging in configuration:
```ini
[Logging]
EnableDebugLogging=true
LogPerformanceMetrics=true
```

## 📈 Performance Tips

### Optimal Settings
1. **Enable hardware acceleration** for best performance
2. **Build frame indexes** for smooth seeking
3. **Use appropriate buffer sizes** based on content
4. **Enable proxy generation** for large files
5. **Configure cache directory** on fast storage

### System Optimization
- Use SSD storage for cache directory
- Ensure adequate RAM for buffer
- Keep GPU drivers updated
- Close unnecessary background applications

## 🔄 Updates and Roadmap

### Current Version Features
- ✅ GPU-accelerated decoding and rendering
- ✅ Professional J-K-L scrubbing controls
- ✅ Smart caching with proxy generation
- ✅ Frame indexing for fast seeking
- ✅ Visual preview scrubbing
- ✅ Hardware acceleration auto-detection
- ✅ Advanced performance monitoring

### Planned Enhancements
- 🔄 Network streaming optimization
- 🔄 AI-powered scene detection
- 🔄 Advanced color grading
- 🔄 Multi-stream support
- 🔄 VR/360° video support
- 🔄 Cloud integration

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines and submit pull requests for any improvements.

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review the configuration documentation
- Submit issues on the project repository
- Join the community discussions

---

**Enhanced Zview** - Professional video playback with advanced features for demanding users.
