#include "HeicImageLoader.h"
#include <QFileInfo>
#include <QImageReader>
#include <QBuffer>
#include <vector>

#ifdef Q_OS_WIN
#include <windows.h>
#include <wincodec.h>
#include <wrl/client.h>
using Microsoft::WRL::ComPtr;
#endif

bool HeicImageLoader::isHeicFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    return (extension == "heic" || extension == "heif");
}

QPixmap HeicImageLoader::loadHeicImage(const QString &filePath)
{
    if (!isHeicFile(filePath)) {
        return QPixmap();
    }
    
    // First try Qt's native loading (in case HEIC plugin is available)
    QImageReader reader(filePath);
    if (reader.canRead()) {
        QImage image = reader.read();
        if (!image.isNull()) {
            qDebug() << "Loaded HEIC using Qt native reader";
            return QPixmap::fromImage(image);
        }
    }
    
    // Try Qt with explicit format specification
    QImageReader heicReader(filePath, "heic");
    if (heicReader.canRead()) {
        QImage image = heicReader.read();
        if (!image.isNull()) {
            qDebug() << "Loaded HEIC using Qt explicit format reader";
            return QPixmap::fromImage(image);
        }
    }
    
    // Try HEIF format explicitly
    QImageReader heifReader(filePath, "heif");
    if (heifReader.canRead()) {
        QImage image = heifReader.read();
        if (!image.isNull()) {
            qDebug() << "Loaded HEIC using Qt HEIF format reader";
            return QPixmap::fromImage(image);
        }
    }
    
    // If Qt can't handle it, try fallback method
    qDebug() << "Qt couldn't load HEIC, trying fallback method";
    return loadHeicFallback(filePath);
}

#ifdef Q_OS_WIN
QPixmap HeicImageLoader::loadHeicFallback(const QString &filePath)
{
    // Try using Windows Imaging Component (WIC) on Windows
    HRESULT hr;
    
    // Initialize COM
    hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
    if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
        qDebug() << "Failed to initialize COM";
        return QPixmap();
    }
    
    ComPtr<IWICImagingFactory> pFactory;
    hr = CoCreateInstance(CLSID_WICImagingFactory, nullptr, CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&pFactory));
    if (FAILED(hr)) {
        qDebug() << "Failed to create WIC factory";
        CoUninitialize();
        return QPixmap();
    }
    
    // Create decoder
    ComPtr<IWICBitmapDecoder> pDecoder;
    hr = pFactory->CreateDecoderFromFilename(
        reinterpret_cast<const wchar_t*>(filePath.utf16()),
        nullptr,
        GENERIC_READ,
        WICDecodeMetadataCacheOnDemand,
        &pDecoder
    );
    
    if (FAILED(hr)) {
        qDebug() << "Failed to create decoder for HEIC file";
        CoUninitialize();
        return QPixmap();
    }
    
    // Get the first frame
    ComPtr<IWICBitmapFrameDecode> pFrame;
    hr = pDecoder->GetFrame(0, &pFrame);
    if (FAILED(hr)) {
        qDebug() << "Failed to get frame from HEIC file";
        CoUninitialize();
        return QPixmap();
    }
    
    // Get frame size
    UINT width, height;
    hr = pFrame->GetSize(&width, &height);
    if (FAILED(hr)) {
        qDebug() << "Failed to get frame size";
        CoUninitialize();
        return QPixmap();
    }
    
    // Convert to BGRA format
    ComPtr<IWICFormatConverter> pConverter;
    hr = pFactory->CreateFormatConverter(&pConverter);
    if (FAILED(hr)) {
        qDebug() << "Failed to create format converter";
        CoUninitialize();
        return QPixmap();
    }
    
    hr = pConverter->Initialize(
        pFrame.Get(),
        GUID_WICPixelFormat32bppBGRA,
        WICBitmapDitherTypeNone,
        nullptr,
        0.0,
        WICBitmapPaletteTypeCustom
    );
    
    if (FAILED(hr)) {
        qDebug() << "Failed to initialize format converter";
        CoUninitialize();
        return QPixmap();
    }
    
    // Copy pixels
    UINT stride = width * 4; // 4 bytes per pixel for BGRA
    UINT bufferSize = stride * height;
    std::vector<BYTE> buffer(bufferSize);
    
    hr = pConverter->CopyPixels(nullptr, stride, bufferSize, buffer.data());
    if (FAILED(hr)) {
        qDebug() << "Failed to copy pixels";
        CoUninitialize();
        return QPixmap();
    }
    
    // Create QImage from buffer
    QImage image(buffer.data(), width, height, stride, QImage::Format_ARGB32);
    
    // Convert BGRA to ARGB (swap R and B channels)
    image = image.rgbSwapped();
    
    CoUninitialize();
    
    if (!image.isNull()) {
        qDebug() << "Successfully loaded HEIC using Windows WIC";
        return QPixmap::fromImage(image);
    }
    
    return QPixmap();
}
#else
QPixmap HeicImageLoader::loadHeicFallback(const QString &filePath)
{
    // On non-Windows platforms, we could try other libraries like libheif
    // For now, just return empty pixmap
    qDebug() << "HEIC fallback not implemented for this platform";
    Q_UNUSED(filePath);
    return QPixmap();
}
#endif