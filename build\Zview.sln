﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{CFB55F95-05B6-3B6F-95D6-C4BA4B5C14B2}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}"
	ProjectSection(ProjectDependencies) = postProject
		{8730B47B-7760-3618-AB23-D0E118011125} = {8730B47B-7760-3618-AB23-D0E118011125}
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8} = {20AA419E-DFE8-3827-8845-4DF300BDCEC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{F0BECCBB-7532-3875-AE1E-1845354850F5}"
	ProjectSection(ProjectDependencies) = postProject
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D} = {F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}
		{8730B47B-7760-3618-AB23-D0E118011125} = {8730B47B-7760-3618-AB23-D0E118011125}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{8730B47B-7760-3618-AB23-D0E118011125}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Zview", "Zview.vcxproj", "{20AA419E-DFE8-3827-8845-4DF300BDCEC8}"
	ProjectSection(ProjectDependencies) = postProject
		{8730B47B-7760-3618-AB23-D0E118011125} = {8730B47B-7760-3618-AB23-D0E118011125}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.Debug|x64.ActiveCfg = Debug|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.Debug|x64.Build.0 = Debug|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.Release|x64.ActiveCfg = Release|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.Release|x64.Build.0 = Release|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F0BECCBB-7532-3875-AE1E-1845354850F5}.Debug|x64.ActiveCfg = Debug|x64
		{F0BECCBB-7532-3875-AE1E-1845354850F5}.Release|x64.ActiveCfg = Release|x64
		{F0BECCBB-7532-3875-AE1E-1845354850F5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F0BECCBB-7532-3875-AE1E-1845354850F5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.Debug|x64.ActiveCfg = Debug|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.Debug|x64.Build.0 = Debug|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.Release|x64.ActiveCfg = Release|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.Release|x64.Build.0 = Release|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8730B47B-7760-3618-AB23-D0E118011125}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.Debug|x64.ActiveCfg = Debug|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.Debug|x64.Build.0 = Debug|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.Release|x64.ActiveCfg = Release|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.Release|x64.Build.0 = Release|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{20AA419E-DFE8-3827-8845-4DF300BDCEC8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F67BB5D4-4DFE-37D2-A68B-6C0084ECB62D} = {CFB55F95-05B6-3B6F-95D6-C4BA4B5C14B2}
		{F0BECCBB-7532-3875-AE1E-1845354850F5} = {CFB55F95-05B6-3B6F-95D6-C4BA4B5C14B2}
		{8730B47B-7760-3618-AB23-D0E118011125} = {CFB55F95-05B6-3B6F-95D6-C4BA4B5C14B2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A35D37D3-2140-330F-AD41-6C25652B99BC}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
