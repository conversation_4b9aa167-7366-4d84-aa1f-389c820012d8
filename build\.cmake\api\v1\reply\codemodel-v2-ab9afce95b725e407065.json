{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-37e4613dbd8023db7e9d.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Zview", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-890747a8f4c3b07f4237.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-9eff912d4625dfacac84.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "Zview::@6890427a1f51a3e7e1df", "jsonFile": "target-Zview-Debug-7547ad8510974152dd65.json", "name": "Zview", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-cf132ba9044b16b04dc1.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "Zview", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-890747a8f4c3b07f4237.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-9eff912d4625dfacac84.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "Zview::@6890427a1f51a3e7e1df", "jsonFile": "target-Zview-Release-ee21a99de7dd7a628afb.json", "name": "Zview", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-bb6e8d9e7c6fd7253921.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "Zview", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-890747a8f4c3b07f4237.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-9eff912d4625dfacac84.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "Zview::@6890427a1f51a3e7e1df", "jsonFile": "target-Zview-MinSizeRel-45a89ac1ef0426c19025.json", "name": "Zview", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-534b839c5cf3662b8e61.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Zview", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-890747a8f4c3b07f4237.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-9eff912d4625dfacac84.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "Zview::@6890427a1f51a3e7e1df", "jsonFile": "target-Zview-RelWithDebInfo-5504da660db3eb33173e.json", "name": "Zview", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Zview/build", "source": "C:/Users/<USER>/Desktop/Zview"}, "version": {"major": 2, "minor": 7}}