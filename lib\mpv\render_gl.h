/* Copyright (C) 2018 the mpv developers
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 */

#ifndef MPV_CLIENT_API_RENDER_GL_H_
#define MPV_CLIENT_API_RENDER_GL_H_

#include "client.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * OpenGL backend
 */
#define MPV_RENDER_API_TYPE_OPENGL "opengl"

/**
 * For MPV_RENDER_PARAM_OPENGL_INIT_PARAMS.
 */
typedef struct mpv_opengl_init_params {
    /**
     * This retrieves OpenGL function pointers, and will use them in subsequent
     * operation.
     * Usually, you can simply call the GL context APIs from here (e.g. eglGetProcAddress
     * or glfwGetProcAddress) to retrieve function pointers.
     * Calling this on the render thread is meaningless, as the OpenGL context
     * must be current on the calling thread.
     */
    void *(*get_proc_address)(void *ctx, const char *name);
    /**
     * Value passed as ctx parameter to get_proc_address().
     */
    void *get_proc_address_ctx;
} mpv_opengl_init_params;

/**
 * For MPV_RENDER_PARAM_OPENGL_FBO.
 */
typedef struct mpv_opengl_fbo {
    /**
     * Framebuffer object name. This must be either a valid FBO generated by
     * glGenFramebuffers() that is complete and color-renderable, or 0. If the
     * value is 0, this refers to the OpenGL default framebuffer.
     */
    int fbo;
    /**
     * Valid dimensions. This must refer to the size of the framebuffer. This
     * must always be set.
     */
    int w, h;
    /**
     * Underlying texture internal format (e.g. GL_RGBA8), or 0 if unknown. If
     * this is the default framebuffer, this can be an equivalent.
     */
    int internal_format;
} mpv_opengl_fbo;

/**
 * Opaque context, returned by mpv_render_context_create().
 */
typedef struct mpv_render_context mpv_render_context;

/**
 * Parameters for mpv_render_context_create().
 * The meaning and type of the p argument of each entry is as follows:
 * - char* for MPV_RENDER_PARAM_API_TYPE
 * - mpv_opengl_init_params* for MPV_RENDER_PARAM_OPENGL_INIT_PARAMS
 * - mpv_opengl_fbo* for MPV_RENDER_PARAM_OPENGL_FBO
 * - int* for MPV_RENDER_PARAM_FLIP_Y
 */
typedef enum mpv_render_param_type {
    MPV_RENDER_PARAM_INVALID = 0,
    /**
     * The render API to use. Valid values:
     * MPV_RENDER_API_TYPE_OPENGL
     */
    MPV_RENDER_PARAM_API_TYPE = 1,
    /**
     * Required if you use MPV_RENDER_API_TYPE_OPENGL.
     */
    MPV_RENDER_PARAM_OPENGL_INIT_PARAMS = 2,
    /**
     * Specify the default framebuffer object to render on. Valid only for
     * MPV_RENDER_API_TYPE_OPENGL. This implies MPV_RENDER_PARAM_FLIP_Y=0 and
     * MPV_RENDER_PARAM_BLOCK_FOR_TARGET_TIME=1, unless they are explicitly
     * provided.
     */
    MPV_RENDER_PARAM_OPENGL_FBO = 3,
    /**
     * Control flipped rendering. Valid for all render APIs. This is optional
     * for all render APIs, and usually is set to 0.
     */
    MPV_RENDER_PARAM_FLIP_Y = 4,
} mpv_render_param_type;

/**
 * Used to pass arbitrary parameters to some mpv_render_* functions. The
 * meaning of the data parameter is determined by the type, and each
 * MPV_RENDER_PARAM_* documents what type the value must point to.
 */
typedef struct mpv_render_param {
    enum mpv_render_param_type type;
    void *data;
} mpv_render_param;

/**
 * Initialize the renderer state. Depending on the backend used, this will
 * access the underlying GPU API and initialize the GPU context.
 *
 * You must free the context with mpv_render_context_free(). Not doing so before
 * the mpv core is destroyed may result in memory leaks or crashes.
 *
 * Currently, only at most 1 context can exists per mpv core (it represents the
 * main video output).
 *
 * You should pass the following parameters:
 *  - MPV_RENDER_PARAM_API_TYPE to select the underlying backend/GPU API.
 *  - Backend-specific init parameters, like MPV_RENDER_PARAM_OPENGL_INIT_PARAMS.
 *
 * @param[out] res set to the context (on success) or NULL (on failure).
 * @param      mpv handle from mpv_create() or mpv_create_client().
 * @param      params an array of parameters, terminated by type==0. It's left
 *                    unspecified what happens with unknown parameters. For
 *                    compatibility, mpv might ignore them, or return an error,
 *                    or kill the process.
 * @return error code (including, but not limited to MPV_ERROR_UNSUPPORTED if
 *         an unsupported API was provided, or if required parameters were not
 *         provided, or if the GPU context could not be created).
 */
int mpv_render_context_create(mpv_render_context **res, mpv_handle *mpv,
                              mpv_render_param *params);

/**
 * Attempt to change a single parameter. Not all backends and parameter types
 * support all kinds of changes.
 *
 * @param ctx a valid render context
 * @param param the parameter type and data that should be set
 * @return error code. If a parameter could not be set, MPV_ERROR_UNSUPPORTED
 *         is returned.
 */
int mpv_render_context_set_parameter(mpv_render_context *ctx,
                                     mpv_render_param param);

/**
 * Retrieve information from the render context. This is NOT a counterpart to
 * mpv_render_context_set_parameter(), because you generally can't read
 * parameters set with it, and this function is not meant for this purpose.
 * Instead, this is for communicating information from the mpv render context
 * to the user.
 *
 * @param ctx a valid render context
 * @param param the parameter type and data that should be retrieved
 * @return error code. If a parameter could not be retrieved, MPV_ERROR_UNSUPPORTED
 *         is returned.
 */
int mpv_render_context_get_info(mpv_render_context *ctx,
                                mpv_render_param param);

typedef void (*mpv_render_update_fn)(void *cb_ctx);

/**
 * Set the callback that notifies you when a new video frame is available, or
 * if the video display configuration somehow changed and requires a redraw.
 * Similar to mpv_set_wakeup_callback(), you must not call any mpv API from
 * the callback, and all the other listed restrictions apply (such as not
 * exiting the callback by throwing exceptions).
 *
 * You can query the new state with mpv_render_context_update().
 * By default, this callback is not set.
 *
 * @param callback callback(callback_ctx) is called if the frame should be
 *                 redrawn
 * @param callback_ctx opaque argument to the callback
 */
void mpv_render_context_set_update_callback(mpv_render_context *ctx,
                                            mpv_render_update_fn callback,
                                            void *callback_ctx);

/**
 * The API user is supposed to call this when the update callback was invoked
 * (like all mpv_render_* functions, this has to happen on the render thread,
 * and _not_ from the update callback itself).
 *
 * This is optional if MPV_RENDER_PARAM_BLOCK_FOR_TARGET_TIME is not used (or
 * is set to 0), but using it is still recommended for better frame pacing.
 *
 * If you don't use this function, you should at least use
 * mpv_render_context_report_swap() at the end of your rendering logic.
 *
 * @return a bitset of mpv_render_update_flag values (i.e. multiple flags are
 *         combined with bitwise or). Typically, this will tell the API user
 *         what should happen next. E.g. if the MPV_RENDER_UPDATE_FRAME flag is
 *         set, mpv_render_context_render() should be called.
 */
unsigned mpv_render_context_update(mpv_render_context *ctx);

/**
 * Render video.
 *
 * Typically renders the video to a target surface provided via mpv_render_param
 * (the details depend on the backend).
 *
 * @param ctx a valid render context
 * @param params an array of parameters, terminated by type==0. Which parameters
 *               are required depends on the backend. It's left unspecified what
 *               happens with unknown parameters.
 * @return error code
 */
int mpv_render_context_render(mpv_render_context *ctx, mpv_render_param *params);

/**
 * Tell the renderer that a frame was flipped at the given time. This is
 * optional, but can help the player to achieve better timing.
 *
 * Note that calling this at least once informs libmpv that you will use this
 * function. If you use it inconsistently, expect bad video playback.
 *
 * If this is called while no video is initialized, it is ignored.
 *
 * @param ctx a valid render context
 */
void mpv_render_context_report_swap(mpv_render_context *ctx);

/**
 * Destroy the mpv renderer state.
 *
 * If video is still active (i.e. a file playing), video will be disabled
 * forcefully.
 *
 * @param ctx a valid render context. After this function returns, this is not
 *            a valid pointer anymore. NULL is also allowed and does nothing.
 */
void mpv_render_context_free(mpv_render_context *ctx);

#ifdef __cplusplus
}
#endif

#endif
