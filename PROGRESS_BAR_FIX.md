# Fixed: Progress Bar Loading 4 Times Issue

## Problem Identified
- Progress bar was showing 4 times instead of 2 times when loading a video
- Two proxy videos were being generated (scrubbing + preview) as expected
- But the progress system was creating multiple overlapping progress indicators

## Root Causes Found

### 1. **Multiple Timer Events**
- Original implementation created 11 separate `QTimer::singleShot` events (0%, 10%, 20%, ..., 100%)
- For 2 proxy types × 11 progress updates = 22 timer events
- This caused rapid-fire progress updates and multiple restarts of the progress bar

### 2. **Simultaneous Progress Bars**
- Both scrubbing and preview proxy generation were triggering separate progress bars
- No coordination between the two proxy generation processes
- Progress bar was being shown/hidden for each individual proxy instead of the overall process

### 3. **Queue Processing Issue**
- Second proxy generation was queued but progress bar management wasn't aware
- Sequential processing wasn't properly triggering next job completion

## Fixes Implemented

### ✅ **1. Fixed Progress Timer Logic**
```cpp
// OLD: Multiple singleShot timers causing overlap
for (int i = 0; i <= 100; i += 10) {
    QTimer::singleShot(i * 50, this, [this, i]() {
        emit proxyGenerationProgress(m_currentJob.sourceFile, i);
    });
}

// NEW: Single repeating timer with proper cleanup
QTimer *progressTimer = new QTimer(this);
connect(progressTimer, &QTimer::timeout, this, [this, progressTimer]() {
    int currentProgress = property("currentProgress").toInt();
    currentProgress += 10;
    // ... proper cleanup and completion handling
});
progressTimer->start(500);
```

### ✅ **2. Unified Progress Bar Management**
```cpp
// Added member variable to track multiple proxy generations
int m_pendingProxyCount = 0;

// Updated signal handling to coordinate multiple proxies
connect(m_proxyManager, &SimpleProxyManager::proxyGenerationStarted, this, [this](...) {
    if (m_currentProxyFile.isEmpty() || m_currentProxyFile != sourceFile) {
        m_currentProxyFile = sourceFile;
        m_pendingProxyCount = 2; // Expect 2 proxies: scrubbing + preview
        showProxyGenerationProgress();
    }
});

connect(m_proxyManager, &SimpleProxyManager::proxyGenerationCompleted, this, [this](...) {
    m_pendingProxyCount--;
    if (m_pendingProxyCount <= 0) {
        hideProxyGenerationProgress(); // Only hide when ALL proxies done
    }
});
```

### ✅ **3. Sequential Job Processing**
```cpp
// Added automatic queue processing after job completion
m_isGenerating = false;
QTimer::singleShot(100, this, &SimpleProxyManager::processQueue);
```

### ✅ **4. Enhanced Progress Display**
```cpp
// Show remaining proxy count in progress label
QString label = QString("Proxy %1% (%2 left)").arg(percentage).arg(m_pendingProxyCount);
m_progressLabel->setText(label);
```

## Expected Behavior Now

### ✅ **Single Progress Bar Session**
1. **Video Opens** → Progress bar appears once
2. **First Proxy** (scrubbing) → Progress 0-100% with "Proxy X% (2 left)"
3. **Second Proxy** (preview) → Progress 0-100% with "Proxy X% (1 left)"  
4. **Both Complete** → Progress bar disappears

### ✅ **Proper File Generation**
- **Scrubbing Proxy**: `[filename]_[hash]_scrub.mp4` (640x360)
- **Preview Proxy**: `[filename]_[hash]_preview.mp4` (1280x720)
- **Location**: `Documents/Zview_Proxies/` folder
- **Sequential Creation**: Second proxy starts after first completes

### ✅ **Visual Improvements**
- **Single Progress Session**: No overlapping progress bars
- **Clear Indication**: Shows how many proxies remaining
- **Smaller Size**: 200x40 pixels (was 300x60)
- **Non-intrusive**: Top-left corner placement

## Technical Improvements

### **Timer Management**
- **Single Timer per Job**: No more overlapping timer events
- **Proper Cleanup**: Timers are deleted after completion
- **Consistent Updates**: 500ms intervals for smooth progress

### **State Management**
- **Pending Counter**: Tracks multiple proxy generations
- **File-based Grouping**: Groups proxies by source video file
- **Completion Detection**: Only hides progress when all proxies done

### **Queue Processing**
- **Sequential Processing**: One proxy at a time for cleaner progress
- **Auto-continuation**: Next job starts automatically after completion
- **Error Handling**: Failed jobs don't block queue processing

## Testing Results Expected

### ✅ **Load Video Test**
1. Open any video file
2. **Expected**: Progress bar appears ONCE at top-left
3. **Expected**: Shows "Proxy 0% (2 left)" → "Proxy 100% (1 left)" → "Proxy 0% (1 left)" → "Proxy 100% (1 left)"
4. **Expected**: Progress bar disappears after both proxies complete
5. **Expected**: 2 proxy files created in Documents/Zview_Proxies/

### ✅ **Multiple Video Test**
1. Open different video files sequentially
2. **Expected**: Each video gets its own progress session
3. **Expected**: No interference between different video proxy generations
4. **Expected**: Clean progress bar state for each video

The progress bar should now show exactly **2 progress cycles** (one for each proxy type) instead of 4, with proper coordination and a much cleaner user experience.
