^C:\USERS\<USER>\DESKTOP\ZVIEW\BUILD\CMAKEFILES\C82FE56C17ECD9348EC059199A080C3C\AUTOUIC_(CONFIG).STAMP.RULE
setlocal
cd C:\Users\<USER>\Desktop\Zview\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\ZVIEW\CMAKELISTS.TXT
setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Desktop/Zview -BC:/Users/<USER>/Desktop/Zview/build --check-stamp-file C:/Users/<USER>/Desktop/Zview/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
