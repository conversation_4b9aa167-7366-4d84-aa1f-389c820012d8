#ifndef IMAGECANVAS_H
#define IMAGECANVAS_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLTexture>
#include <QMatrix4x4>
#include <QPixmap>
#include <QWheelEvent>
#include <QMouseEvent>
#include <QStringList>

class ImageCanvas : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    explicit ImageCanvas(QWidget *parent = nullptr);
    ~ImageCanvas();

    // Image management
    void loadImage(const QString &filePath);
    void loadImageFromPixmap(const QPixmap &pixmap);
    bool hasImage() const { return !m_currentImage.isNull(); }
    QString getCurrentImagePath() const { return m_currentImagePath; }
    
    // Navigation
    void loadNextImage();
    void loadPreviousImage();
    void buildImageList(const QString &currentImagePath);
    
    // Zoom and pan controls
    void zoomIn();
    void zoomOut();
    void resetZoom();
    void fitToWindow();
    void actualSize();
    float getZoomFactor() const { return m_zoomFactor; }
    
    // Pan controls
    void panUp();
    void panDown();
    void panLeft();
    void panRight();
    void centerImage();

signals:
    void imageLoaded(const QString &filePath);
    void imageLoadFailed(const QString &filePath);
    void zoomChanged(float zoomFactor);

protected:
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    void setupShaders();
    void setupVertexData();
    void loadImageToTexture();
    void updateProjectionMatrix();
    void calculateImageTransform();
    QPointF screenToImage(const QPointF &screenPos) const;
    QPointF imageToScreen(const QPointF &imagePos) const;
    
    // Image data
    QPixmap m_currentImage;
    QString m_currentImagePath;
    QStringList m_imageList;
    int m_currentImageIndex = -1;
    
    // Zoom and pan state
    float m_zoomFactor = 1.0f;
    QPointF m_imageOffset = QPointF(0, 0);
    QPoint m_lastMousePos;
    QPoint m_zoomAnchor;
    bool m_isPanning = false;
    QPointF m_panStartOffset;
    QPointF m_panImagePoint;
    
    // OpenGL rendering
    QOpenGLShaderProgram *m_shaderProgram = nullptr;
    QOpenGLBuffer m_vertexBuffer;
    QOpenGLBuffer m_elementBuffer;
    QOpenGLVertexArrayObject m_vao;
    QOpenGLTexture *m_texture = nullptr;
    QMatrix4x4 m_projectionMatrix;
    QMatrix4x4 m_viewMatrix;
    QMatrix4x4 m_modelMatrix;
    bool m_textureNeedsUpdate = false;
    
    // Image dimensions and transform
    QSizeF m_imageSize;
    QSizeF m_scaledImageSize;
    QPointF m_imagePosition;
};

#endif // IMAGECANVAS_H
