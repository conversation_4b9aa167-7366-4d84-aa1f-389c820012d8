/****************************************************************************
** Meta object code from reading C++ file 'VisualPreviewScrubber.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../VisualPreviewScrubber.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VisualPreviewScrubber.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN21VisualPreviewScrubberE_t {};
} // unnamed namespace

template <> constexpr inline auto VisualPreviewScrubber::qt_create_metaobjectdata<qt_meta_tag_ZN21VisualPreviewScrubberE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "VisualPreviewScrubber",
        "positionChanged",
        "",
        "position",
        "scrubStarted",
        "scrubEnded",
        "seekRequested",
        "previewRequested",
        "timestamp",
        "jklModeChanged",
        "enabled",
        "onPreviewReady",
        "preview",
        "onPositionUpdateTimer",
        "onAnimationFinished",
        "onPreviewGenerationFinished"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'positionChanged'
        QtMocHelpers::SignalData<void(double)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 },
        }}),
        // Signal 'scrubStarted'
        QtMocHelpers::SignalData<void(double)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 },
        }}),
        // Signal 'scrubEnded'
        QtMocHelpers::SignalData<void(double)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 },
        }}),
        // Signal 'seekRequested'
        QtMocHelpers::SignalData<void(double)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 },
        }}),
        // Signal 'previewRequested'
        QtMocHelpers::SignalData<void(double)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 8 },
        }}),
        // Signal 'jklModeChanged'
        QtMocHelpers::SignalData<void(bool)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 10 },
        }}),
        // Slot 'onPreviewReady'
        QtMocHelpers::SlotData<void(double, const QPixmap &)>(11, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 8 }, { QMetaType::QPixmap, 12 },
        }}),
        // Slot 'onPositionUpdateTimer'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onAnimationFinished'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onPreviewGenerationFinished'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<VisualPreviewScrubber, qt_meta_tag_ZN21VisualPreviewScrubberE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject VisualPreviewScrubber::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN21VisualPreviewScrubberE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN21VisualPreviewScrubberE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN21VisualPreviewScrubberE_t>.metaTypes,
    nullptr
} };

void VisualPreviewScrubber::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<VisualPreviewScrubber *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->positionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 1: _t->scrubStarted((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 2: _t->scrubEnded((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 3: _t->seekRequested((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 4: _t->previewRequested((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 5: _t->jklModeChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 6: _t->onPreviewReady((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPixmap>>(_a[2]))); break;
        case 7: _t->onPositionUpdateTimer(); break;
        case 8: _t->onAnimationFinished(); break;
        case 9: _t->onPreviewGenerationFinished(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(double )>(_a, &VisualPreviewScrubber::positionChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(double )>(_a, &VisualPreviewScrubber::scrubStarted, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(double )>(_a, &VisualPreviewScrubber::scrubEnded, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(double )>(_a, &VisualPreviewScrubber::seekRequested, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(double )>(_a, &VisualPreviewScrubber::previewRequested, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (VisualPreviewScrubber::*)(bool )>(_a, &VisualPreviewScrubber::jklModeChanged, 5))
            return;
    }
}

const QMetaObject *VisualPreviewScrubber::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VisualPreviewScrubber::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN21VisualPreviewScrubberE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int VisualPreviewScrubber::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void VisualPreviewScrubber::positionChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void VisualPreviewScrubber::scrubStarted(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void VisualPreviewScrubber::scrubEnded(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void VisualPreviewScrubber::seekRequested(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void VisualPreviewScrubber::previewRequested(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void VisualPreviewScrubber::jklModeChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
namespace {
struct qt_meta_tag_ZN13PreviewWorkerE_t {};
} // unnamed namespace

template <> constexpr inline auto PreviewWorker::qt_create_metaobjectdata<qt_meta_tag_ZN13PreviewWorkerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "PreviewWorker",
        "previewReady",
        "",
        "timestamp",
        "preview",
        "batchComplete",
        "generatePreview",
        "size",
        "generatePreviewBatch",
        "QList<double>",
        "timestamps"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'previewReady'
        QtMocHelpers::SignalData<void(double, const QPixmap &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 }, { QMetaType::QPixmap, 4 },
        }}),
        // Signal 'batchComplete'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'generatePreview'
        QtMocHelpers::SlotData<void(double, const QSize &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 }, { QMetaType::QSize, 7 },
        }}),
        // Slot 'generatePreviewBatch'
        QtMocHelpers::SlotData<void(const QList<double> &, const QSize &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 9, 10 }, { QMetaType::QSize, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<PreviewWorker, qt_meta_tag_ZN13PreviewWorkerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject PreviewWorker::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13PreviewWorkerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13PreviewWorkerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13PreviewWorkerE_t>.metaTypes,
    nullptr
} };

void PreviewWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<PreviewWorker *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->previewReady((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPixmap>>(_a[2]))); break;
        case 1: _t->batchComplete(); break;
        case 2: _t->generatePreview((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QSize>>(_a[2]))); break;
        case 3: _t->generatePreviewBatch((*reinterpret_cast< std::add_pointer_t<QList<double>>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QSize>>(_a[2]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QList<double> >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (PreviewWorker::*)(double , const QPixmap & )>(_a, &PreviewWorker::previewReady, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (PreviewWorker::*)()>(_a, &PreviewWorker::batchComplete, 1))
            return;
    }
}

const QMetaObject *PreviewWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *PreviewWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13PreviewWorkerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int PreviewWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void PreviewWorker::previewReady(double _t1, const QPixmap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void PreviewWorker::batchComplete()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
namespace {
struct qt_meta_tag_ZN18EnhancedSeekSliderE_t {};
} // unnamed namespace

template <> constexpr inline auto EnhancedSeekSlider::qt_create_metaobjectdata<qt_meta_tag_ZN18EnhancedSeekSliderE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "EnhancedSeekSlider"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<EnhancedSeekSlider, qt_meta_tag_ZN18EnhancedSeekSliderE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject EnhancedSeekSlider::staticMetaObject = { {
    QMetaObject::SuperData::link<QSlider::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18EnhancedSeekSliderE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18EnhancedSeekSliderE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18EnhancedSeekSliderE_t>.metaTypes,
    nullptr
} };

void EnhancedSeekSlider::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<EnhancedSeekSlider *>(_o);
    (void)_t;
    (void)_c;
    (void)_id;
    (void)_a;
}

const QMetaObject *EnhancedSeekSlider::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EnhancedSeekSlider::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18EnhancedSeekSliderE_t>.strings))
        return static_cast<void*>(this);
    return QSlider::qt_metacast(_clname);
}

int EnhancedSeekSlider::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QSlider::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
