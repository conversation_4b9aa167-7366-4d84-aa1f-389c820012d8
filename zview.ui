<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Zview</class>
 <widget class="QWidget" name="Zview">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Zview</string>
  </property>
  <property name="styleSheet">
   <string>background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                            stop:0 rgb(28, 28, 30),
                                            stop:1 rgb(22, 22, 25));</string>
  </property>
  <widget class="QWidget" name="modernToolbox" native="true">
   <property name="geometry">
    <rect>
     <x>570</x>
     <y>0</y>
     <width>180</width>
     <height>60</height>
    </rect>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
   <property name="styleSheet">
    <string>QWidget#modernToolbox {
    background: rgba(35, 35, 38, 0.95);
    border: 1px solid rgba(60, 60, 65, 0.8);
    border-radius: 8px;
}

QWidget#modernToolbox:hover {
    background: rgba(40, 40, 43, 0.95);
    border: 1px solid rgba(70, 70, 75, 0.9);
}</string>
   </property>
   <widget class="QToolButton" name="cropTool">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>6</y>
      <width>48</width>
      <height>48</height>
     </rect>
    </property>
    <property name="toolTip">
     <string>Crop</string>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}

QToolButton:checked {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}</string>
    </property>
    <property name="text">
     <string/>
    </property>    <property name="icon">
     <iconset>
      <normaloff>c:/Users/<USER>/Desktop/Zview/crop-icon-white.svg</normaloff>c:/Users/<USER>/Desktop/Zview/crop-icon-white.svg</iconset>
    </property><property name="iconSize">
     <size>
      <width>20</width>
      <height>20</height>
     </size>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QToolButton" name="editTool">
    <property name="geometry">
     <rect>
      <x>65</x>
      <y>6</y>
      <width>48</width>
      <height>48</height>
     </rect>
    </property>
    <property name="toolTip">
     <string>Edit</string>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}

QToolButton:checked {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset theme="document-edit"/>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QToolButton" name="viewTool">
    <property name="geometry">
     <rect>
      <x>120</x>
      <y>6</y>
      <width>48</width>
      <height>48</height>
     </rect>
    </property>
    <property name="toolTip">
     <string>View</string>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}

QToolButton:checked {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset theme="zoom-fit-best"/>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
   </widget>
  </widget>
  <widget class="QToolButton" name="toolButton">
   <property name="geometry">
    <rect>
     <x>1200</x>
     <y>710</y>
     <width>36</width>
     <height>36</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string>QToolButton {    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}</string>
   </property>
   <property name="text">
    <string>...</string>
   </property>
   <property name="icon">
    <iconset theme="edit-delete"/>
   </property>
  </widget>
  <widget class="QToolButton" name="toolButton_2">
   <property name="geometry">
    <rect>
     <x>1155</x>
     <y>790</y>
     <width>36</width>
     <height>36</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}</string>
   </property>
   <property name="text">
    <string>...</string>
   </property>
   <property name="icon">
    <iconset theme="QIcon::ThemeIcon::DocumentProperties"/>
   </property>
  </widget>
  <widget class="QSlider" name="seekSlider">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>600</y>
     <width>1135</width>
     <height>40</height>
    </rect>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string>QSlider {
    background: transparent;
}

QSlider::groove:horizontal {
    background: rgba(255, 255, 255, 0.15);
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background: #0078D4;
    border: none;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background: #106EBE;
    transform: scale(1.1);
}

QSlider::sub-page:horizontal {
    background: #0078D4;
    border-radius: 3px;
}</string>
   </property>
   <property name="orientation">
    <enum>Qt::Orientation::Horizontal</enum>
   </property>
  </widget>
  <widget class="QPushButton" name="buttonA">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>650</y>
     <width>30</width>
     <height>25</height>
    </rect>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string>QPushButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Segoe UI', system-ui, sans-serif;
    padding: 6px;
}

QPushButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QPushButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}

QPushButton:checked {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}</string>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QPushButton" name="buttonB">
   <property name="geometry">
    <rect>
     <x>55</x>
     <y>650</y>
     <width>30</width>
     <height>25</height>
    </rect>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string>QPushButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Segoe UI', system-ui, sans-serif;
    padding: 6px;
}

QPushButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QPushButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}

QPushButton:checked {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}</string>
   </property>
   <property name="text">
    <string>B</string>
   </property>
  </widget>
  <widget class="QSlider" name="volumeSlider">
   <property name="geometry">
    <rect>
     <x>950</x>
     <y>740</y>
     <width>120</width>
     <height>30</height>
    </rect>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string>QSlider {
    background: transparent;
}

QSlider::groove:horizontal {
    background: rgba(255, 255, 255, 0.15);
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background: #0078D4;
    border: none;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background: #106EBE;
    transform: scale(1.1);
}

QSlider::sub-page:horizontal {
    background: #0078D4;
    border-radius: 3px;
}</string>
   </property>
   <property name="minimum">
    <number>0</number>
   </property>
   <property name="maximum">
    <number>100</number>
   </property>
   <property name="value">
    <number>100</number>
   </property>
   <property name="orientation">
    <enum>Qt::Orientation::Horizontal</enum>
   </property>
  </widget>
  <widget class="QWidget" name="modernToolbox_2" native="true">
   <property name="geometry">
    <rect>
     <x>1090</x>
     <y>520</y>
     <width>101</width>
     <height>131</height>
    </rect>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string>QWidget#modernToolbox_2 {
    background: rgba(35, 35, 38, 0.95);
    border: 1px solid rgba(60, 60, 65, 0.8);
    border-radius: 8px;
}

QWidget#modernToolbox_2:hover {
    background: rgba(40, 40, 43, 0.95);
    border: 1px solid rgba(70, 70, 75, 0.9);
}</string>
   </property>
   <widget class="QToolButton" name="toolButton_5">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>81</width>
      <height>31</height>
     </rect>
    </property>
    <property name="visible">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}</string>
    </property>
    <property name="text">
     <string>Apply Crop</string>
    </property>
   </widget>
   <widget class="QToolButton" name="toolButton_4">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>50</y>
      <width>81</width>
      <height>31</height>
     </rect>
    </property>
    <property name="visible">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}</string>
    </property>
    <property name="text">
     <string>Undo</string>
    </property>
   </widget>
   <widget class="QToolButton" name="toolButton_3">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>90</y>
      <width>81</width>
      <height>31</height>
     </rect>
    </property>
    <property name="visible">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string>QToolButton {
    background: rgba(60, 60, 65, 0.95);
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

QToolButton:hover {
    background: rgba(0, 120, 212, 1.0);
    color: #FFFFFF;
}

QToolButton:pressed {
    background: rgba(0, 90, 158, 1.0);
    transform: scale(0.96);
}</string>
    </property>
    <property name="text">
     <string>Exit Crop</string>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
