# Crop Overlay Constrained to Image Area

## Issue Description
When crop mode was activated, the transparent blue overlay covered the entire window area instead of being constrained to just the image area.

## Solution Implemented
Modified the crop overlay drawing logic to only apply the blue transparent overlay within the image bounds, leaving the surrounding window area uncovered.

## Technical Changes

### Before (Full Window Coverage):
```cpp
// Create inverted overlay: cover everything except the crop region
QPainterPath fullPath;
fullPath.addRect(rect()); // Full widget area - ENTIRE WINDOW
```

### After (Image Area Only):
```cpp
// Get the actual image bounds to constrain overlay to image area only
QRect imageBounds = getImageBounds();

if (imageBounds.isValid()) {
    // Create inverted overlay: cover only the image area except the crop region
    QPainterPath fullPath;
    fullPath.addRect(imageBounds); // Only image area, not entire widget
}
```

## Visual Behavior

### Before:
- ❌ Blue overlay covered entire window
- ❌ Areas outside image (black bars, UI space) were also overlaid
- ❌ Confusing visual coverage beyond actual image content

### After:
- ✅ Blue overlay only covers the actual image area
- ✅ Black bars and UI areas remain unaffected
- ✅ Clear visual distinction between image and non-image areas
- ✅ Professional and focused crop selection experience

## Implementation Details

### Safety Check:
```cpp
if (imageBounds.isValid()) {
    // Only apply overlay if we have valid image bounds
    // Prevents overlay drawing when no image is loaded
}
```

### Overlay Logic:
1. **Get Image Bounds**: Use `getImageBounds()` to determine actual image area
2. **Create Image Path**: `fullPath.addRect(imageBounds)` covers only image area
3. **Subtract Crop Area**: Remove crop region from image area to create inverted overlay
4. **Fill with Blue**: Apply semi-transparent blue only to image area outside crop region

### Border Drawing:
- Crop region border still draws normally using `painter.drawRect(m_cropRect)`
- Border is not affected by the overlay area constraint

## User Experience Improvements

### ✅ **Focused Crop Selection**
- Overlay clearly shows which parts of the IMAGE will be cropped out
- No confusion about areas outside the image

### ✅ **Professional Appearance**
- Matches behavior of professional image editing software
- Clean separation between image content and application UI

### ✅ **Better Visual Clarity**
- Black bars and window chrome remain unobscured
- Easier to see image boundaries during crop selection

### ✅ **Responsive to Image Position**
- Works correctly with zoomed images
- Adapts to different image sizes and aspect ratios
- Handles image panning and positioning

## Testing Scenarios

### Image Types:
- ✅ **Landscape images**: Overlay respects horizontal image bounds
- ✅ **Portrait images**: Overlay respects vertical image bounds  
- ✅ **Square images**: Overlay correctly constrains to square bounds
- ✅ **Small images**: Overlay only covers actual image, not padded area

### Window States:
- ✅ **Windowed mode**: Overlay constrained to image within window
- ✅ **Fullscreen mode**: Overlay follows image bounds in fullscreen
- ✅ **Zoomed images**: Overlay adapts to zoom level and position

## Files Modified
- `c:\Users\<USER>\Desktop\Zview\zview.cpp`: Modified `paintEvent()` crop overlay drawing logic

## Status
✅ **COMPLETE**: Crop overlay now properly constrains the transparent blue overlay to only the image area, providing a professional and focused crop selection experience.
