#include "MpvWrapper.h"
#include <stdexcept>
#include <QDebug>
#include <QOpenGLContext>
#include <QOpenGLFunctions>
#include <QTimer>
#include <QtMath>
#include <QDateTime>
#include <QFileInfo>

// Include mpv headers here
#include <mpv/client.h>
#include <mpv/render_gl.h>

// MPV format constants if not defined
#ifndef MPV_FORMAT_DOUBLE
#define MPV_FORMAT_DOUBLE 5
#endif

#ifndef MPV_FORMAT_FLAG
#define MPV_FORMAT_FLAG 3
#endif

// Missing function declarations from the MPV library
extern "C" {
    int mpv_get_property(mpv_handle *ctx, const char *name, int format, void *data);
    int mpv_observe_property(mpv_handle *ctx, uint64_t reply_userdata, const char *name, int format);
    int mpv_unobserve_property(mpv_handle *ctx, uint64_t registered_reply_userdata);
    
    // Event system for real-time updates
    typedef struct mpv_event {
        int event_id;
        int error;
        uint64_t reply_userdata;
        void *data;
    } mpv_event;
    
    typedef struct mpv_event_property {
        const char *name;
        int format;
        void *data;
    } mpv_event_property;
    
    mpv_event *mpv_wait_event(mpv_handle *ctx, double timeout);
    void mpv_wakeup(mpv_handle *ctx);
    void mpv_set_wakeup_callback(mpv_handle *ctx, void (*cb)(void *d), void *d);
    
         // Event IDs
     #define MPV_EVENT_PROPERTY_CHANGE 22
     #define MPV_EVENT_PLAYBACK_RESTART 21
     #define MPV_EVENT_VIDEO_RECONFIG 23
     #define MPV_EVENT_QUEUE_OVERFLOW 24
}

MpvWrapper::MpvWrapper(QObject *parent) 
    : QObject(parent), mpv(nullptr), mpv_render_ctx(nullptr), 
      m_initialized(false), m_renderInitialized(false), m_hasNewFrame(false),
      m_simulatedPosition(0.0), m_simulatedDuration(300.0), m_isPaused(true), m_lastUpdateTime(0)
{    
    // Initialize timers for position updates
    m_positionTimer = new QTimer(this);
    connect(m_positionTimer, &QTimer::timeout, this, &MpvWrapper::updatePosition);
            m_positionTimer->setInterval(16); // ~60 FPS optimal interval for smooth navigation
    
    // Simple event timer as backup
    m_eventTimer = new QTimer(this);
    connect(m_eventTimer, &QTimer::timeout, this, &MpvWrapper::processEvents);
            m_eventTimer->setInterval(20); // Optimal backup event processing
    
    try {
        mpv = mpv_create();
        if (!mpv) {
            qDebug() << "Failed to create mpv instance";
            return;
        }        // Set essential options BEFORE mpv_initialize
        mpv_set_option_string(mpv, "keep-open", "yes");
        mpv_set_option_string(mpv, "idle", "yes");
        mpv_set_option_string(mpv, "loop", "inf");  // Enable infinite loop/repeat by default
        mpv_set_option_string(mpv, "vo", "libmpv");  // Use libmpv video output for rendering
        mpv_set_option_string(mpv, "hwdec", "no");  // Disable hardware decoding to avoid artifacts
        mpv_set_option_string(mpv, "force-window", "no");  // Don't create separate window
        
        // Video quality and rendering options to eliminate sparkling artifacts
        mpv_set_option_string(mpv, "video-sync", "display-vdrop");  // Drop frames instead of interpolating
        mpv_set_option_string(mpv, "interpolation", "no");  // Explicitly disable interpolation
        mpv_set_option_string(mpv, "deinterlace", "yes");  // Enable deinterlacing for interlaced content
        mpv_set_option_string(mpv, "vf", "yadif=mode=send_frame:parity=auto:deint=interlaced");  // Advanced deinterlacing
        mpv_set_option_string(mpv, "opengl-swapinterval", "1");  // Enable vsync
        mpv_set_option_string(mpv, "background", "#000000");  // Black background
        mpv_set_option_string(mpv, "keepaspect", "yes");  // Maintain aspect ratio
        mpv_set_option_string(mpv, "scale", "bilinear");  // Use simple scaling
        mpv_set_option_string(mpv, "cscale", "bilinear");  // Use simple chroma scaling
        mpv_set_option_string(mpv, "temporal-dither", "no");  // Disable temporal dithering
        mpv_set_option_string(mpv, "dither", "no");  // Disable spatial dithering        
        
        // Initialize mpv
        if (mpv_initialize(mpv) < 0) {
            qDebug() << "Failed to initialize mpv";
            mpv_terminate_destroy(mpv);
            mpv = nullptr;
            return;
        }
          
        // Set up property observation for real-time updates
        mpv_observe_property(mpv, 1, "time-pos", MPV_FORMAT_DOUBLE);
        mpv_observe_property(mpv, 2, "duration", MPV_FORMAT_DOUBLE);
        mpv_observe_property(mpv, 3, "pause", MPV_FORMAT_FLAG);
        
        // Set up wakeup callback with thread-safe queued connection
        mpv_set_wakeup_callback(mpv, on_mpv_wakeup, this);
        
        // Start backup event timer
        m_eventTimer->start();
        
        m_initialized = true;
        qDebug() << "MpvWrapper initialized successfully with thread-safe event processing";
        
    } catch (const std::exception& e) {
        qDebug() << "Exception in MpvWrapper constructor:" << e.what();
        if (mpv) {
            mpv_terminate_destroy(mpv);
            mpv = nullptr;
        }
    }
}

MpvWrapper::~MpvWrapper()
{
    if (mpv_render_ctx) {
        mpv_render_context_free(mpv_render_ctx);
        mpv_render_ctx = nullptr;
    }
    
    if (mpv) {
        mpv_terminate_destroy(mpv);
        mpv = nullptr;
    }
}

// Static function for OpenGL procedure address lookup
static void* get_proc_address_wrapper(void*, const char* name) {
    QOpenGLContext *ctx = QOpenGLContext::currentContext();
    if (!ctx) return nullptr;
    return reinterpret_cast<void*>(ctx->getProcAddress(QByteArray(name)));
}

bool MpvWrapper::initializeRenderContext(QOpenGLContext *glContext)
{
    if (!mpv || !m_initialized) {
        qDebug() << "MPV not initialized, cannot create render context";
        return false;
    }
    
    if (mpv_render_ctx) {
        qDebug() << "Render context already initialized";
        return true;
    }
    
    // Make sure the OpenGL context is current
    if (!glContext->makeCurrent(glContext->surface())) {
        qDebug() << "Failed to make OpenGL context current";
        return false;
    }    // Set up render context parameters
    mpv_opengl_init_params gl_init_params{0};
    gl_init_params.get_proc_address = get_proc_address_wrapper;
    gl_init_params.get_proc_address_ctx = nullptr;
    
    mpv_render_param params[] = {
        {MPV_RENDER_PARAM_API_TYPE, const_cast<char*>(MPV_RENDER_API_TYPE_OPENGL)},
        {MPV_RENDER_PARAM_OPENGL_INIT_PARAMS, &gl_init_params},
        {MPV_RENDER_PARAM_INVALID, nullptr}
    };
    
    // Create render context
    int result = mpv_render_context_create(&mpv_render_ctx, mpv, params);
    if (result < 0) {
        qDebug() << "Failed to create mpv render context:" << mpv_error_string(result);
        return false;
    }
    
    // Set up render update callback
    mpv_render_context_set_update_callback(mpv_render_ctx, on_mpv_render_update, this);
    
    m_renderInitialized = true;
    qDebug() << "MPV render context initialized successfully";
    return true;
}

void MpvWrapper::on_mpv_render_update(void *ctx)
{
    MpvWrapper *wrapper = static_cast<MpvWrapper*>(ctx);
    wrapper->m_hasNewFrame = true;
    emit wrapper->frameReady();
}

void MpvWrapper::renderFrame(int width, int height, int fbo)
{
    if (!mpv_render_ctx || !m_renderInitialized) {
        return;
    }
    
    // Set up render parameters
    mpv_opengl_fbo opengl_fbo{0};
    opengl_fbo.fbo = fbo;
    opengl_fbo.w = width;
    opengl_fbo.h = height;
    opengl_fbo.internal_format = 0;  // Use default
    
    int flip_y = 1;  // Qt OpenGL coordinate system is flipped
    
    mpv_render_param params[] = {
        {MPV_RENDER_PARAM_OPENGL_FBO, &opengl_fbo},
        {MPV_RENDER_PARAM_FLIP_Y, &flip_y},
        {MPV_RENDER_PARAM_INVALID, nullptr}
    };
    
    // Render the frame
    mpv_render_context_render(mpv_render_ctx, params);
    m_hasNewFrame = false;
}

bool MpvWrapper::hasNewFrame() const
{
    return m_hasNewFrame;
}

void MpvWrapper::acknowledgeFrame()
{
    if (mpv_render_ctx) {
        mpv_render_context_report_swap(mpv_render_ctx);
    }
}

void MpvWrapper::loadFile(const QString &filePath)
{
    if (!mpv || !m_initialized) {
        qDebug() << "MpvWrapper not initialized, cannot load file";
        return;
    }

    const QByteArray filePathUtf8 = filePath.toUtf8();
    const char *cmd[] = {"loadfile", filePathUtf8.constData(), NULL};
    
    int result = mpv_command(mpv, cmd);
    if (result < 0) {
        qDebug() << "Failed to load file:" << filePath << "Error:" << mpv_error_string(result);
    } else {        qDebug() << "Loading file:" << filePath;
        m_currentFilePath = filePath;        // Reset position tracking
        m_simulatedPosition = 0.0;
        m_simulatedDuration = 0.0; // Will be updated by events
        m_isPaused = true; // Videos start paused
        
        qDebug() << "Loading video with real-time event system:" << filePath;
        
        // Property observations are already set up in constructor
        // Events will automatically update position/duration in real-time
        
        // Emit initial values (will be updated by events immediately)
        emit positionChanged(0.0);
        emit durationChanged(0.0);
    }
}

void MpvWrapper::play()
{
    if (!mpv || !m_initialized) return;
    
    int result = mpv_set_property_string(mpv, "pause", "no");
    if (result < 0) {
        qDebug() << "Failed to play:" << mpv_error_string(result);
    } else {
        m_isPaused = false;
        // Start position updates timer to track real position/duration
        if (!m_positionTimer->isActive()) {
            m_positionTimer->start();
        }
        qDebug() << "Video playback started";
    }
}

void MpvWrapper::pause()
{
    if (!mpv || !m_initialized) return;
    
    int result = mpv_set_property_string(mpv, "pause", "yes");
    if (result < 0) {
        qDebug() << "Failed to pause:" << mpv_error_string(result);
    } else {
        m_isPaused = true;
        // Keep timer running to detect state changes and duration
        qDebug() << "Video playback paused";
    }
}

void MpvWrapper::stop()
{
    if (!mpv || !m_initialized) return;
    
    const char *cmd[] = {"stop", NULL};
    int result = mpv_command(mpv, cmd);
    if (result < 0) {
        qDebug() << "Failed to stop:" << mpv_error_string(result);
    }
    m_isPaused = true;
    m_simulatedPosition = 0.0;
    m_positionTimer->stop(); // Stop position updates when stopped
    qDebug() << "Video playback stopped";
}

void MpvWrapper::setLoop(bool enabled)
{
    if (!mpv || !m_initialized) return;
    
    const char *loopValue = enabled ? "inf" : "no";
    int result = mpv_set_property_string(mpv, "loop", loopValue);
    if (result < 0) {
        qDebug() << "Failed to set loop:" << mpv_error_string(result);
    } else {
        qDebug() << "Video loop" << (enabled ? "enabled" : "disabled");
    }
}

void MpvWrapper::setVolume(int volume)
{
    if (!mpv || !m_initialized) return;
    
    // Clamp volume to valid range (0-100)
    volume = qBound(0, volume, 100);
    
    // Convert volume to string for mpv
    QString volumeStr = QString::number(volume);
    QByteArray volumeCStr = volumeStr.toUtf8();
    int result = mpv_set_property_string(mpv, "volume", volumeCStr.constData());
    if (result < 0) {
        qDebug() << "Failed to set volume:" << mpv_error_string(result);
    } else {
        qDebug() << "Volume set to:" << volume << "%";
    }
}

bool MpvWrapper::isInitialized() const
{
    return m_initialized && m_renderInitialized;
}

void MpvWrapper::seek(double position)
{
    if (!mpv || !m_initialized) return;
    
    // Convert position to string and use mpv_command for seeking
    QString posStr = QString::number(position, 'f', 3);
    QByteArray posCStr = posStr.toUtf8();
    const char *cmd[] = {"seek", posCStr.constData(), "absolute", NULL};
    
    int result = mpv_command(mpv, cmd);
    if (result < 0) {
        qDebug() << "Failed to seek to position" << position << ":" << mpv_error_string(result);    } else {
        qDebug() << "Seeking to position:" << position << "seconds";
        
        // Immediately update our internal state for instant visual feedback
        m_simulatedPosition = position;
        emit positionChanged(position);
        
        // Process any immediate events from the seek command
        processEventsImmediate();
    }
}

double MpvWrapper::getPosition() const
{
    if (!mpv || !m_initialized) return 0.0;
    
    double position = 0.0;
    int result = mpv_get_property(mpv, "time-pos", MPV_FORMAT_DOUBLE, &position);
    if (result < 0) {
        // If we can't get the real position, fall back to simulated
        return m_simulatedPosition;
    }
    return position;
}

double MpvWrapper::getDuration() const
{
    if (!mpv || !m_initialized) return 0.0;
    
    double duration = 0.0;
    int result = mpv_get_property(mpv, "duration", MPV_FORMAT_DOUBLE, &duration);
    if (result < 0) {
        // If we can't get the real duration, fall back to simulated
        return m_simulatedDuration;
    }
    return duration;
}

bool MpvWrapper::isPaused() const
{
    if (!mpv || !m_initialized) return true;
    
    int paused = 1;
    int result = mpv_get_property(mpv, "pause", MPV_FORMAT_FLAG, &paused);
    if (result < 0) {
        // If we can't get the real pause state, fall back to simulated
        return m_isPaused;
    }
    return paused != 0;
}

void MpvWrapper::adjustDuration(double newDuration)
{
    if (newDuration > 0) {
        m_simulatedDuration = newDuration;
        qDebug() << "Manually adjusted duration to:" << newDuration << "seconds";
        emit durationChanged(m_simulatedDuration);
    }
}

void MpvWrapper::updatePosition()
{
    if (!mpv || !m_initialized) return;
    
    static double lastPosition = -1.0;
    static double lastDuration = -1.0;
    
    // Get real position and duration from MPV
    double position = getPosition();
    double duration = getDuration();
    bool paused = isPaused();
      // Ultra-responsive updates - emit on any change for fastest display
    if (qAbs(position - lastPosition) > 0.008) { // Update if changed by more than 8ms (125fps)
        lastPosition = position;
        emit positionChanged(position);
    }
    
    if (qAbs(duration - lastDuration) > 0.001) { // Update duration on tiniest changes
        lastDuration = duration;
        emit durationChanged(duration);
    }
    
    // Update internal state for fallback purposes
    m_simulatedPosition = position;
    m_simulatedDuration = duration;
    m_isPaused = paused;
}

void MpvWrapper::updatePositionInstant(double position)
{
    // Ultra-fast instant position update - optimized for maximum speed
    m_simulatedPosition = position;
    emit positionChanged(position);
}

void MpvWrapper::processEvents()
{
    if (!mpv || !m_initialized) return;
    
    // Process all pending MPV events with zero timeout for immediate response
    while (true) {
        mpv_event *event = mpv_wait_event(mpv, 0.0); // Non-blocking call
        
        if (!event || event->event_id == 0) { // No more events
            break;
        }
        
        if (event->event_id == MPV_EVENT_PROPERTY_CHANGE) {
            mpv_event_property *prop = (mpv_event_property*)event->data;
            
            if (prop && prop->data) {
                if (strcmp(prop->name, "time-pos") == 0 && prop->format == MPV_FORMAT_DOUBLE) {
                    double position = *(double*)prop->data;
                    m_simulatedPosition = position;
                    emit positionChanged(position);
                }
                else if (strcmp(prop->name, "duration") == 0 && prop->format == MPV_FORMAT_DOUBLE) {
                    double duration = *(double*)prop->data;
                    m_simulatedDuration = duration;
                    emit durationChanged(duration);
                }
                else if (strcmp(prop->name, "pause") == 0 && prop->format == MPV_FORMAT_FLAG) {
                    int paused = *(int*)prop->data;
                    m_isPaused = (paused != 0);
                }
            }
        }
        else if (event->event_id == MPV_EVENT_PLAYBACK_RESTART) {
            // Playback restarted, e.g., after a seek
            qDebug() << "MPV_EVENT_PLAYBACK_RESTART (seek finished)";
            emit seekCompleted();
        }
    }
}

void MpvWrapper::on_mpv_wakeup(void *ctx)
{
    // This is called from MPV thread - use QueuedConnection for thread safety
    MpvWrapper *wrapper = static_cast<MpvWrapper*>(ctx);
    
    // Process events safely using QUEUED connection for thread safety
    QMetaObject::invokeMethod(wrapper, "processEventsImmediate", Qt::QueuedConnection);
}

void MpvWrapper::processEventsImmediate()
{
    if (!mpv || !m_initialized) return;
    
    // Process MPV events with safety limit to prevent infinite loops
    int maxEvents = 100; // Safety limit
    int eventCount = 0;
    
    while (eventCount < maxEvents) {
        mpv_event *event = mpv_wait_event(mpv, 0.0); // Non-blocking call
        
        if (!event || event->event_id == 0) { // No more events
            break;
        }
        
        eventCount++;
        
        if (event->event_id == MPV_EVENT_PROPERTY_CHANGE) {
            mpv_event_property *prop = (mpv_event_property*)event->data;
            
            if (prop && prop->data) {
                if (strcmp(prop->name, "time-pos") == 0 && prop->format == MPV_FORMAT_DOUBLE) {
                    double position = *(double*)prop->data;
                    if (qAbs(position - m_simulatedPosition) > 0.01) { // Only emit on significant changes
                        m_simulatedPosition = position;
                        emit positionChanged(position);
                    }
                }
                else if (strcmp(prop->name, "duration") == 0 && prop->format == MPV_FORMAT_DOUBLE) {
                    double duration = *(double*)prop->data;
                    if (qAbs(duration - m_simulatedDuration) > 0.1) { // Only emit on significant changes
                        m_simulatedDuration = duration;
                        emit durationChanged(duration);
                    }
                }
                else if (strcmp(prop->name, "pause") == 0 && prop->format == MPV_FORMAT_FLAG) {
                    int paused = *(int*)prop->data;
                    bool newPauseState = (paused != 0);
                    if (newPauseState != m_isPaused) { // Only emit on state changes
                        m_isPaused = newPauseState;
                    }
                }
            }
        }
        else if (event->event_id == MPV_EVENT_PLAYBACK_RESTART) {
            // Playback restarted, e.g., after a seek
            qDebug() << "MPV_EVENT_PLAYBACK_RESTART (seek finished)";
            emit seekCompleted();
        }
    }
}

void MpvWrapper::enableScrubbingMode(bool enabled)
{
    if (!mpv || !m_initialized) return;
    
    if (enabled) {
        // Optimize MPV for ultra-fast scrubbing
        mpv_set_property_string(mpv, "video-sync", "display-vdrop");
        mpv_set_property_string(mpv, "interpolation", "no");
        mpv_set_property_string(mpv, "tscale", "oversample");
        mpv_set_property_string(mpv, "video-output-levels", "limited");
        mpv_set_property_string(mpv, "hwdec", "auto-safe");
        mpv_set_property_string(mpv, "vd-lavc-dr", "yes");
          // Reduce seeking overhead
        mpv_set_property_string(mpv, "demuxer-seekable-cache", "no");
        mpv_set_property_string(mpv, "cache-secs", "0.5");  // Minimal cache for speed
        mpv_set_property_string(mpv, "demuxer-max-packets", "20");  // Reduce packet buffer
        mpv_set_property_string(mpv, "demuxer-max-bytes", "1048576");  // 1MB max buffer
        
        // Enable frame dropping for smooth scrubbing
        mpv_set_property_string(mpv, "framedrop", "decoder+vo");
        mpv_set_property_string(mpv, "video-latency-hacks", "yes");
        
        // Disable audio during scrubbing for better performance
        mpv_set_property_string(mpv, "mute", "yes");
        
        qDebug() << "Scrubbing mode enabled - MPV optimized for ultra-fast seeking";
    } else {        // Restore normal playback settings
        mpv_set_property_string(mpv, "video-sync", "audio");
        mpv_set_property_string(mpv, "interpolation", "yes");
        mpv_set_property_string(mpv, "framedrop", "vo");
        mpv_set_property_string(mpv, "cache-secs", "10");
        mpv_set_property_string(mpv, "demuxer-max-packets", "320");
        mpv_set_property_string(mpv, "demuxer-max-bytes", "16777216");  // 16MB default
        mpv_set_property_string(mpv, "video-latency-hacks", "no");
        
        // Restore audio when exiting scrubbing mode
        mpv_set_property_string(mpv, "mute", "no");
        
        qDebug() << "Scrubbing mode disabled - MPV restored to normal playback with audio";
    }
}

void MpvWrapper::setScrubbingPosition(double position)
{
    if (!mpv || !m_initialized) return;
    
    // Ultra-fast position update for scrubbing
    m_simulatedPosition = position;
    
    // Use MPV's fastest seeking method for scrubbing
    const char *cmd[] = {"seek", QString::number(position, 'f', 6).toLocal8Bit().data(), "absolute+exact", nullptr};
    mpv_command(mpv, cmd);  // Use synchronous command for immediate execution
    
    // Immediate visual feedback
    emit positionChanged(position);
}

void MpvWrapper::flushScrubbingSeeks()
{
    if (!mpv || !m_initialized) return;
    
    // Force MPV to process all pending seeks immediately
    processEventsImmediate();
}

void MpvWrapper::optimizeForScrubbing()
{
    if (!mpv || !m_initialized) return;
    
    // Configure MPV for maximum scrubbing performance
    mpv_set_property_string(mpv, "hr-seek", "yes");
    mpv_set_property_string(mpv, "hr-seek-framedrop", "no");
    mpv_set_property_string(mpv, "video-latency-hacks", "yes");
    mpv_set_property_string(mpv, "opengl-swapinterval", "0");
    
    // NOTE: Don't mute audio during scrubbing to preserve user experience
    // Audio will be handled by pause/play state during scrubbing
    
    qDebug() << "MPV optimized for ultra-fast scrubbing performance (audio preserved)";
}

void MpvWrapper::restoreAudio()
{
    if (!mpv || !m_initialized) return;
    
    // Restore audio after scrubbing or other operations
    mpv_set_property_string(mpv, "mute", "no");
    
    qDebug() << "Audio restored after scrubbing/optimization";
}